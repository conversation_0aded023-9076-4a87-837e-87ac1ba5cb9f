# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
.venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment variables
.env
.env.*

# Credentials and secrets
*.pem
*.key
credentials.json
firebase-service-account.json
google-cloud-credentials.json

# Logs
*.log
logs/
app_output.log

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
uploads/
node_modules/
lambda-layer/
lambda-layer-data/
lambda-layer-core/
cdk.out/
concierge.log
cookies.txt 