# Telnyx Bidirectional Streaming Deployment Instructions

This package contains updated files for the Telnyx bidirectional streaming implementation with Gemini Live API. The changes include:

1. Changed the codec from OPUS to G722 for lower compute/memory requirements
2. Enhanced logging throughout the audio processing pipeline
3. Improved error handling and diagnostic information
4. Fixed welcome message handling
5. Added a send_text method to the GeminiLiveClient class
6. Enhanced audio processing and validation

## Files Included

- `telnyx_bidirectional_streaming.py`: Main implementation of the bidirectional streaming
- `gemini_live_client.py`: Client for interacting with Gemini Live API
- `audio_processor.py`: Audio processing utilities
- `utils.py`: Utility functions
- `call_manager.py`: Call state management

## Deployment Steps

1. Connect to the EC2 instance:
   ```
   ssh -i YOUR_KEY.pem <EMAIL>
   ```

2. Create a backup of the current files:
   ```
   sudo cp -r /path/to/websocket /path/to/websocket_backup_$(date +"%Y%m%d_%H%M%S")
   ```

3. Upload the package to the EC2 instance:
   ```
   scp -i YOUR_KEY.pem websocket_updates.tar.gz <EMAIL>:~
   ```

4. Extract the package on the EC2 instance:
   ```
   ssh -i YOUR_KEY.pem <EMAIL>
   sudo tar -xzf websocket_updates.tar.gz -C /path/to/websocket
   ```

5. Restart the websocket server service:
   ```
   sudo systemctl restart websocket-server
   ```

6. Check the logs to verify the service is running:
   ```
   sudo journalctl -u websocket-server -n 100 -f
   ```

## Testing

1. Make a test call to the Telnyx phone number
2. Check the logs for audio processing information
3. Verify that you can hear the Gemini response

## Troubleshooting

If you encounter issues:

1. Check the logs for errors:
   ```
   sudo journalctl -u websocket-server -n 200
   ```

2. Verify the service is running:
   ```
   sudo systemctl status websocket-server
   ```

3. If needed, restore from backup:
   ```
   sudo cp -r /path/to/websocket_backup_TIMESTAMP/* /path/to/websocket/
   sudo systemctl restart websocket-server
   ```

## Changes Made

### telnyx_bidirectional_streaming.py
- Changed codec from OPUS to G722
- Enhanced logging for audio processing
- Improved error handling for media events
- Fixed welcome message handling

### gemini_live_client.py
- Added send_text method
- Enhanced logging for audio handling
- Improved error handling

### audio_processor.py
- Enhanced resample_audio function
- Added validation for audio data
- Added ratio verification for resampling

### utils.py
- Set default logging level to DEBUG
- Added configuration for specific loggers

### call_manager.py
- No significant changes, just included for completeness
