{"api": {"waitlistapi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "waitlistapi"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}}, "function": {"waitlistapi": {"build": true, "dependsOn": [{"attributeEnvMap": {"Arn": "TABLE_ARN", "Name": "TABLE_NAME"}, "attributes": ["Name", "<PERSON><PERSON>"], "category": "storage", "resourceName": "WaitList"}], "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_function_waitlistapi_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "waitlistapi"}]}, "AMPLIFY_function_waitlistapi_s3Key": {"usedBy": [{"category": "function", "resourceName": "waitlistapi"}]}}, "storage": {"WaitList": {"providerPlugin": "awscloudformation", "service": "DynamoDB"}}}