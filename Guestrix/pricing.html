<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing - Guestrix</title>
    <meta name="description" content="Simple, transparent pricing that grows with you. No hidden fees. No long-term contracts. Just more time back in your day.">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-1R7VK1G6CV"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-1R7VK1G6CV');
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="192x192" href="images/android-chrome-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="images/android-chrome-512x512.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700;800&family=Noto+Sans:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo-container">
                <a href="index.html" class="logo-link">
                    <img src="images/guestrix_logo.svg" alt="Guestrix Logo" class="logo-image">
                    <h2 class="brand-name">Guestrix</h2>
                </a>
            </div>
            <div class="nav-links">
                <a href="index.html" class="nav-link">Home</a>
                <a href="about.html" class="nav-link">About Us</a>
                <a href="pricing.html" class="nav-link active">Pricing</a>
                <a href="stories.html" class="nav-link">Stories Inspired by Guests Like You</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-reduced">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Staycee Pricing</h1>
                <p class="hero-description">
                    Simple, transparent pricing that grows with you.
                </p>
                <p class="hero-description">
                    No hidden fees. No long-term contracts. Just more time back in your day.
                </p>
            </div>
        </div>
    </section>

    <!-- Pricing Plans Section -->
    <section class="pricing-section">
        <div class="container">
            <div class="pricing-grid">
                <!-- Short-Term Rentals Plan -->
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="plan-title">Short-Term Rentals</h3>
                        <div class="plan-price">
                            <span class="price-amount">$12</span>
                            <span class="price-period">/property/month</span>
                        </div>
                        <p class="plan-description">Best for individual Airbnb or Vrbo listings</p>
                    </div>
                    
                    <div class="pricing-features">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> 24/7 guest messaging (text + call)</li>
                            <li><i class="fas fa-check"></i> Custom scripts per property</li>
                            <li><i class="fas fa-check"></i> Personalized onboarding call</li>
                            <li><i class="fas fa-check"></i> Smart call forwarding</li>
                            <li><i class="fas fa-check"></i> Works with Airbnb, Vrbo, direct booking tools</li>
                            <li><i class="fas fa-check"></i> Guest escalation alerts</li>
                            <li><i class="fas fa-check"></i> Property-specific FAQs</li>
                        </ul>
                        
                        <div class="pricing-example">
                            <strong>Example:</strong> Have 3 listings? You pay $36/month.
                        </div>
                    </div>
                    
                    <div class="pricing-footer">
                        <a href="#contact" class="pricing-cta">Get Started</a>
                    </div>
                </div>

                <!-- Hotels & B&Bs Plan -->
                <div class="pricing-card">
                    <div class="pricing-header">
                        <h3 class="plan-title">Hotels & Boutique B&Bs</h3>
                        <div class="plan-price">
                            <span class="price-amount">$40</span>
                            <span class="price-period">/month base + $2/room</span>
                        </div>
                        <p class="plan-description">Ideal for 5+ room properties with staff coordination</p>
                    </div>
                    
                    <div class="pricing-features">
                        <ul class="features-list">
                            <li><i class="fas fa-check"></i> Everything in the short-term rental plan</li>
                            <li><i class="fas fa-check"></i> Bulk room/guest management</li>
                            <li><i class="fas fa-check"></i> Cleaning crew coordination</li>
                            <li><i class="fas fa-check"></i> Call routing by department</li>
                            <li><i class="fas fa-check"></i> Real-time staff alerts via SMS</li>
                            <li><i class="fas fa-check"></i> Multi-property dashboard</li>
                            <li><i class="fas fa-check"></i> White-glove onboarding</li>
                        </ul>
                        
                        <div class="pricing-example">
                            <strong>Example:</strong> 15-room inn = $40 + ($2 × 15) = $70/month
                        </div>
                    </div>
                    
                    <div class="pricing-footer">
                        <a href="#contact" class="pricing-cta">Get Started</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Custom Pricing CTA -->
    <section class="custom-pricing" id="contact">
        <div class="container">
            <div class="two-column">
                <div class="column-left">
                    <h3 class="section-title">Want custom pricing for 20+ units or multiple properties?</h3>
                    <p class="section-text">
                        Let's talk about a solution that fits your specific needs and scale.
                    </p>
                </div>
                <div class="column-right">
                    <div class="waitlist-form">
                        <h4 class="form-title">Let's Talk</h4>
                        <form id="waitlistForm" class="form">
                            <div class="form-group">
                                <input type="text" id="firstName" name="firstName" placeholder="First Name" required>
                            </div>
                            <div class="form-group">
                                <input type="text" id="lastName" name="lastName" placeholder="Last Name" required>
                            </div>
                            <div class="form-group">
                                <input type="email" id="waitlistEmail" name="email" placeholder="Email Address" required>
                            </div>
                            <div class="form-group optional">
                                <textarea id="message" name="message" placeholder="Tell us about your property/properties (optional)" rows="3"></textarea>
                            </div>
                            <button type="submit" class="form-submit-btn">
                                <span class="btn-text">Contact Us</span>
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/guestrix_logo.svg" alt="Guestrix Logo" class="footer-logo-image">
                    <h3 class="footer-brand-name">Guestrix</h3>
                </div>
                <div class="footer-links">
                    <a href="index.html">Home</a>
                    <a href="about.html">About Us</a>
                    <a href="pricing.html">Pricing</a>
                    <a href="stories.html">Stories Inspired by Guests Like You</a>
                </div>
                <div class="footer-contact">
                    <p>&copy; 2025 Guestrix. All rights reserved.</p>
                    <p>Contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Success Modal -->
    <div id="successModal" class="success-modal">
        <div class="success-modal-content">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h3>Thank You!</h3>
            <p>Your submission was successful. We'll reach out to you soon!</p>
            <button class="success-modal-close" onclick="closeSuccessModal()">Got it</button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="config.js" type="module"></script>
    <script src="script.js" type="module"></script>
</body>
</html>
