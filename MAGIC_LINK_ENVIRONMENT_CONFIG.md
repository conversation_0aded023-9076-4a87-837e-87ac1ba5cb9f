# Magic Link Environment Configuration

## Overview
Magic links are generated with different base URLs depending on the environment configuration.

## Environment Variables

### Primary Configuration
- `DEPLOYMENT_ENV`: Explicit environment setting (recommended)
  - `local` or `development`: http://localhost:5001
  - `staging`: https://dev.guestrix.ai
  - `production`: https://app.guestrix.ai

### Legacy Configuration (fallback)
- `FLASK_ENV`: Flask environment setting
  - `development`: http://localhost:5001
  - (other values): https://app.guestrix.ai
- `DEBUG_MODE`: Debug mode flag
  - `true`, `1`, `yes`: http://localhost:5001
  - (other values): https://app.guestrix.ai

## Environment Setup Examples

### Local Development
```bash
export DEPLOYMENT_ENV=local
# OR
export FLASK_ENV=development
# OR
export DEBUG_MODE=true
```

### Staging Environment
```bash
export DEPLOYMENT_ENV=staging
```

### Production Environment
```bash
export DEPLOYMENT_ENV=production
# OR (default fallback)
# No environment variables set
```

## Base URL Override
You can override the environment-based URL generation by passing a `base_url` parameter:

```python
from concierge.utils.firestore_client import generate_magic_link_url

# Override with custom base URL
url = generate_magic_link_url(token, base_url="https://custom.domain.com")
```

## Testing
Run the test script to verify environment configuration:
```bash
python test_magic_link_environments.py
```
