# 🧪 Manual Authentication Flow Testing Guide

## Prerequisites
1. Server running on `http://localhost:5000`
2. Firebase/Firestore configured
3. Test reservation data available

## 🔗 Test Magic Link Creation

### Option 1: Use Test Route
```bash
curl http://localhost:5000/magic/test
```

### Option 2: Create via Admin Interface
1. Go to host dashboard
2. Create a test reservation
3. Generate magic link for the reservation

## 📋 Testing Checklist

### **1. Initial Access Flow**

#### ✅ Test: Valid Session + Same Token → Dashboard
- [ ] Click magic link first time
- [ ] Should show PIN screen (no valid session)
- [ ] Enter correct PIN and access dashboard
- [ ] Click same magic link again
- [ ] Should go directly to dashboard (valid session)

#### ✅ Test: No/Expired/Different Token → PIN Screen
- [ ] Click magic link in incognito/new browser
- [ ] Should show PIN screen
- [ ] Clear cookies and try again
- [ ] Should show PIN screen

### **2. PIN Screen Options**

#### ✅ Required Elements Present:
- [ ] PIN entry (4 digits)
- [ ] "Have an account? Enter your phone number" link
- [ ] Help text ("Having trouble? Contact your host...")

#### ✅ Test PIN Screen Navigation:
- [ ] Click "Have an account?" → Should go to phone login
- [ ] PIN input should only accept digits
- [ ] PIN input should auto-advance between fields

### **3. PIN Entry Logic**

#### ✅ Test: Existing Temp User (Active) → Dashboard Access
**Setup:** Use magic link that was previously used with PIN
- [ ] Enter same PIN as before
- [ ] Should go directly to dashboard
- [ ] User data should be preserved

#### ✅ Test: Existing Temp User (Migrated) + Default PIN + Has Name → "Are you [Name]?"
**Setup:** User previously upgraded to permanent account
- [ ] Enter default PIN (last 4 digits of phone)
- [ ] Should show confirmation: "Are you [Name]?"
- [ ] Test "Yes" → Should login to permanent account
- [ ] Test "No" → Should create new temp user

#### ✅ Test: Existing Temp User (Migrated) + Custom PIN → New Temp User
**Setup:** User previously upgraded with custom PIN
- [ ] Enter custom PIN (not last 4 digits)
- [ ] Should create new temporary user
- [ ] Should go to name collection

#### ✅ Test: No Temp User + PIN = Phone Last-4 → Name Collection
**Setup:** Fresh magic link, PIN matches phone last 4
- [ ] Enter PIN matching reservation phone last 4 digits
- [ ] Should go to name collection screen
- [ ] Should create new temporary user

#### ✅ Test: No Match → Verification Failed
**Setup:** Fresh magic link, PIN doesn't match phone last 4
- [ ] Enter random PIN (not matching phone)
- [ ] Should show "Incorrect PIN" error
- [ ] Should stay on PIN screen
- [ ] Should track attempts (max 5)

### **4. User Confirmation Flow**

#### ✅ Test: "Are you [Name]?" Screen
- [ ] Should display user's name clearly
- [ ] "Yes, that's me" button present
- [ ] "No, I'm someone else" button present

#### ✅ Test: Yes → OTP to Permanent Account
- [ ] Click "Yes, that's me"
- [ ] Should initiate OTP verification
- [ ] Should login to existing permanent account
- [ ] Should attach current reservation

#### ✅ Test: No → New Temp User Creation
- [ ] Click "No, I'm someone else"
- [ ] Should create new temporary user
- [ ] Should go to name collection
- [ ] Should not affect existing permanent account

### **5. Phone Login Flow**

#### ✅ Test: Phone Entry → Find Users
**Access via:** "Have an account? Enter your phone number"
- [ ] Phone input field present
- [ ] Phone validation working
- [ ] Submit button functional

#### ✅ Test: Permanent User Found → OTP Verification
- [ ] Enter phone of existing permanent user
- [ ] Should initiate OTP verification
- [ ] After OTP: Should login to permanent account
- [ ] Should attach current reservation to account

#### ✅ Test: No User Found → Signup Choice
- [ ] Enter phone with no existing user
- [ ] Should show account type selection (Guest/Host)
- [ ] Guest option should prompt for magic link
- [ ] Host option should continue with OTP

#### ✅ Test: Multiple Users → First Match
- [ ] Enter phone with multiple users (edge case)
- [ ] Should use first permanent user found
- [ ] Should attach reservation properly

### **6. Name Collection (Always for New Temp Users)**

#### ✅ Required Elements:
- [ ] Name input field (optional)
- [ ] Phone input field (optional)
- [ ] "Continue" button
- [ ] "Skip for Now" button/link
- [ ] Clear explanation of phone number benefits

#### ✅ Test: Name Only/Skip → Temp Account
- [ ] Enter name only, click Continue
- [ ] Should update temp user with name
- [ ] Should go to dashboard
- [ ] Should remain temporary account

- [ ] Click "Skip for Now"
- [ ] Should go to dashboard
- [ ] Should remain temporary account

#### ✅ Test: Name + Phone → OTP → Permanent Account
- [ ] Enter both name and phone
- [ ] Should initiate OTP verification
- [ ] After OTP: Should create permanent account
- [ ] Should disable temp user access
- [ ] Should preserve reservation associations
- [ ] Should go to dashboard as permanent user

## 🔍 **Verification Points**

### Session Management
- [ ] Magic link sessions persist correctly
- [ ] Session cookies set with proper expiration
- [ ] Session validation works across requests

### User Data Integrity
- [ ] Temporary users created with correct data
- [ ] Permanent users preserve all temp user data
- [ ] Reservation associations maintained
- [ ] PIN codes handled correctly

### Error Handling
- [ ] Invalid magic links show proper error
- [ ] Expired magic links handled gracefully
- [ ] Network errors don't break flow
- [ ] Form validation prevents invalid submissions

### Security
- [ ] PIN attempts limited (max 5)
- [ ] Sessions expire appropriately
- [ ] Phone verification required for permanent accounts
- [ ] Temp users properly disabled after migration

## 🐛 **Common Issues to Check**

1. **Magic Link Token Issues**
   - Invalid/expired tokens
   - Token hash mismatches
   - URL encoding problems

2. **Session Problems**
   - Cookie not set/read properly
   - Session validation failures
   - Cross-request session loss

3. **User Detection Issues**
   - Phone number normalization
   - PIN comparison logic
   - Migration status tracking

4. **Database Consistency**
   - Firestore connection issues
   - Document creation/update failures
   - Query result inconsistencies

5. **Template Rendering**
   - Missing template variables
   - Form action URLs incorrect
   - JavaScript functionality broken

## 📊 **Success Criteria**

All flows should work seamlessly with:
- ✅ Proper redirects at each step
- ✅ Data persistence across requests
- ✅ Clear user feedback/messaging
- ✅ Graceful error handling
- ✅ Security measures in place
