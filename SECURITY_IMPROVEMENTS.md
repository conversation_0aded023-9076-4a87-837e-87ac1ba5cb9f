# Security Improvements: API Key and Credential Protection

## Overview

This document outlines the security improvements made to protect sensitive credentials from being exposed in client-side HTML and JavaScript code.

## Problem Identified

Previously, sensitive credentials were being exposed directly in the HTML templates and client-side JavaScript:

- **Gemini API keys** were passed directly in template data attributes
- **Firebase configuration** (including project IDs, auth domains, storage buckets) was injected directly into HTML
- **API keys** were available in `window.GEMINI_API_KEY` globally

This exposed sensitive credentials in the browser's view source and made them accessible to any JavaScript code running on the page.

## Solutions Implemented

### 1. Secure API Endpoints

Created secure server-side endpoints that require authentication to access sensitive credentials:

- `/api/firebase-config` - Provides Firebase configuration only to authenticated users
- `/api/gemini-voice-config` - Provides Gemini API key only to authenticated users  
- `/api/ephemeral-token` - Provides ephemeral tokens for Gemini API access

### 2. Template Security

Updated templates to only include user-specific data, not sensitive credentials:

**Before:**
```html
<div id="template-data"
     data-gemini-api-key="{{ gemini_api_key }}"
     data-user-id="{{ user_id }}">
```

**After:**
```html  
<div id="template-data"
     data-user-id="{{ user_id }}">
<!-- Note: Secure credentials loaded via API endpoints -->
```

### 3. Secure Firebase Initialization

Replaced direct Firebase config injection with secure async initialization:

**Before:**
```javascript
const firebaseConfigData = {{ firebase_config | tojson | safe }};
firebase.initializeApp(firebaseConfigData);
```

**After:**
```javascript
async function initializeFirebaseSecurely() {
  const response = await fetch('/api/firebase-config');
  const data = await response.json();
  firebase.initializeApp(data.config);
}
```

### 4. Ephemeral Token Pattern

Implemented ephemeral token pattern similar to the marketing website:

```javascript
async function fetchEphemeralToken() {
  const response = await fetch('/api/ephemeral-token', {
    method: 'POST',
    credentials: 'same-origin'
  });
  return response.json();
}
```

## Security Benefits

1. **No exposed credentials** - API keys and configurations are not visible in HTML source
2. **Authentication required** - All sensitive endpoints require user authentication
3. **Reduced attack surface** - Credentials not accessible to malicious scripts
4. **Audit trail** - Server logs access to sensitive endpoints
5. **Flexible credential management** - Can implement token rotation and expiration

## User Data Still Exposed

The following user-specific data remains exposed as it belongs to the authenticated user:

- User ID
- Property ID  
- Guest name
- Phone number
- WebSocket URLs

This data is considered safe to expose as it's specific to the authenticated user's session.

## Implementation Notes

### Authentication Requirements

All new secure endpoints require the `@login_required` decorator and validate session state.

### Backwards Compatibility

Fallback mechanisms are in place for scenarios where secure endpoints are not available:

```javascript
try {
  const tokenResponse = await fetchEphemeralToken();
  authToken = tokenResponse.token;
} catch (tokenError) {
  // Fallback to direct API key fetch
  const config = await fetchGeminiConfig();
  authToken = config.apiKey;
}
```

### Environment Variables

The secure endpoints read credentials from environment variables:

- `GEMINI_API_KEY`
- `FIREBASE_API_KEY`
- `FIREBASE_AUTH_DOMAIN`
- `FIREBASE_PROJECT_ID`
- `FIREBASE_STORAGE_BUCKET`
- `FIREBASE_MESSAGING_SENDER_ID`
- `FIREBASE_APP_ID`

## Testing

Test the security improvements by:

1. Viewing page source - no API keys should be visible
2. Checking browser developer tools - credentials not in global variables
3. Verifying voice calls still work with secure token fetching
4. Confirming Firebase authentication works with secure initialization

## Future Enhancements

Consider implementing:

1. **True ephemeral tokens** with expiration times
2. **Token rotation** for long-running sessions
3. **Rate limiting** on credential endpoints
4. **Additional audit logging** for credential access 