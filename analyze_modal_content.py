#!/usr/bin/env python3
"""
Analyze the modal content to understand why we're not extracting the detailed rules.
"""

import re
from bs4 import BeautifulSoup

def analyze_modal_content():
    """Analyze the saved modal content."""
    
    print(f"🔍 ANALYZING MODAL CONTENT")
    print("=" * 80)
    
    try:
        # Read the modal content
        with open('modal_content_debug.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"📄 File size: {len(html_content):,} characters")
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Get all text content
        all_text = soup.get_text()
        print(f"📄 Text content size: {len(all_text):,} characters")
        
        # Search for key sections
        sections = [
            "House rules",
            "Checking in and out", 
            "During your stay",
            "Before you leave",
            "Additional rules"
        ]
        
        print(f"\n🔍 SECTION ANALYSIS:")
        print("-" * 40)
        
        for section in sections:
            # Case-insensitive search
            pattern = re.compile(re.escape(section), re.IGNORECASE)
            matches = pattern.findall(all_text)
            print(f"{section}: {len(matches)} occurrences")
            
            if matches:
                # Find context around the section
                for match in pattern.finditer(all_text):
                    start = max(0, match.start() - 200)
                    end = min(len(all_text), match.end() + 500)
                    context = all_text[start:end]
                    
                    print(f"\n  Context around '{section}':")
                    print(f"  {context[:300]}...")
                    break  # Only show first occurrence
        
        # Search for specific rules we expect
        expected_rules = [
            "6 guests maximum",
            "Pets allowed", 
            "Quiet hours",
            "9:00 PM",
            "7:00 AM",
            "No parties",
            "No events",
            "No smoking",
            "Commercial photography",
            "Gather used towels",
            "Turn things off",
            "Lock up"
        ]
        
        print(f"\n🎯 RULE CONTENT ANALYSIS:")
        print("-" * 40)
        
        found_rules = []
        missing_rules = []
        
        for rule in expected_rules:
            pattern = re.compile(re.escape(rule), re.IGNORECASE)
            matches = pattern.findall(all_text)
            
            if matches:
                found_rules.append(rule)
                print(f"✅ FOUND: '{rule}' ({len(matches)} times)")
                
                # Show context
                for match in pattern.finditer(all_text):
                    start = max(0, match.start() - 100)
                    end = min(len(all_text), match.end() + 100)
                    context = all_text[start:end].replace('\n', ' ').replace('\t', ' ')
                    # Clean up multiple spaces
                    context = re.sub(r'\s+', ' ', context)
                    print(f"    Context: ...{context}...")
                    break
            else:
                missing_rules.append(rule)
                print(f"❌ MISSING: '{rule}'")
        
        print(f"\n📊 SUMMARY:")
        print(f"Found rules: {len(found_rules)}/{len(expected_rules)}")
        print(f"Success rate: {len(found_rules)/len(expected_rules)*100:.1f}%")
        
        # Look for any text that might contain the missing rules with variations
        print(f"\n🔍 SEARCHING FOR VARIATIONS:")
        print("-" * 40)
        
        variations = [
            (r"quiet.*hours", "Quiet hours variations"),
            (r"no.*parties", "No parties variations"),
            (r"no.*smoking", "No smoking variations"),
            (r"commercial.*photo", "Commercial photography variations"),
            (r"gather.*towel", "Gather towels variations"),
            (r"9:?00.*pm", "9 PM time variations"),
            (r"7:?00.*am", "7 AM time variations")
        ]
        
        for pattern, description in variations:
            regex = re.compile(pattern, re.IGNORECASE)
            matches = list(regex.finditer(all_text))
            
            if matches:
                print(f"✅ {description}: {len(matches)} matches")
                for match in matches[:2]:  # Show first 2 matches
                    start = max(0, match.start() - 50)
                    end = min(len(all_text), match.end() + 50)
                    context = all_text[start:end].replace('\n', ' ').replace('\t', ' ')
                    context = re.sub(r'\s+', ' ', context)
                    print(f"    Match: ...{context}...")
            else:
                print(f"❌ {description}: No matches")
        
        return len(found_rules) >= 8  # Success if we find most rules
        
    except Exception as e:
        print(f"❌ Error analyzing modal content: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = analyze_modal_content()
    if success:
        print(f"\n🎉 ANALYSIS SUCCESSFUL: Modal contains expected content!")
    else:
        print(f"\n❌ ANALYSIS FAILED: Modal missing critical content")
