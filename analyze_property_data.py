#!/usr/bin/env python3
"""
Analyze the property data for the newly imported listing to understand:
1. What was extracted during import
2. What was stored in Firestore (property record + knowledge items)
3. What is shown in the UI
4. Why some house rules are missing
"""

import sys
import os
import json
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

def analyze_property_data():
    """Analyze the property data for the newly imported listing."""
    
    property_id = "9684d928-f1e9-41fd-875c-77fa57e837cb"
    listing_url = "https://www.airbnb.com/rooms/700299802944952028"
    
    print(f"🔍 ANALYZING PROPERTY DATA")
    print(f"Property ID: {property_id}")
    print(f"Listing URL: {listing_url}")
    print("=" * 80)
    
    try:
        # Import Firestore functions
        from concierge.utils.firestore_client import get_property, list_knowledge_items_by_property
        
        # 1. Get the property record
        print(f"\n📋 PROPERTY RECORD:")
        print("-" * 40)
        
        property_data = get_property(property_id)
        if property_data:
            print(f"✅ Property found")
            print(f"Name: {property_data.get('name', 'N/A')}")
            print(f"Address: {property_data.get('address', 'N/A')}")
            print(f"Check-in time: {property_data.get('checkInTime', 'N/A')}")
            print(f"Check-out time: {property_data.get('checkOutTime', 'N/A')}")
            print(f"Created: {property_data.get('createdAt', 'N/A')}")
            print(f"Status: {'Active' if property_data.get('status') else 'Inactive'}")
            print(f"New property: {property_data.get('new', 'N/A')}")
            
            # Check for house rules in property data
            house_rules = property_data.get('houseRules', [])
            print(f"House rules in property record: {len(house_rules)}")
            for i, rule in enumerate(house_rules):
                print(f"  {i+1}. {rule}")
                
            # Check for import data
            import_data = property_data.get('importData')
            if import_data:
                print(f"Import data present: Yes")
                raw_data = import_data.get('rawData', {})
                extracted_rules = raw_data.get('house_rules', [])
                print(f"Extracted house rules in import data: {len(extracted_rules)}")
                for i, rule in enumerate(extracted_rules):
                    print(f"  {i+1}. [{rule.get('type', 'N/A')}] {rule.get('description', 'N/A')}")
            else:
                print(f"Import data present: No")
                
        else:
            print(f"❌ Property not found")
            return
        
        # 2. Get knowledge items
        print(f"\n🧠 KNOWLEDGE ITEMS:")
        print("-" * 40)
        
        knowledge_items = list_knowledge_items_by_property(property_id)
        print(f"Total knowledge items: {len(knowledge_items)}")
        
        # Group by type
        by_type = {}
        for item in knowledge_items:
            item_type = item.get('type', 'unknown')
            if item_type not in by_type:
                by_type[item_type] = []
            by_type[item_type].append(item)
        
        for item_type, items in by_type.items():
            print(f"\n{item_type.upper()}: {len(items)} items")
            for i, item in enumerate(items):
                status = item.get('status', 'N/A')
                content = item.get('content', 'N/A')
                tags = item.get('tags', [])
                source = item.get('source', 'N/A')
                print(f"  {i+1}. [{status}] [{source}] {content[:60]}...")
                print(f"      Tags: {tags}")
        
        # 3. Analyze house rules specifically
        print(f"\n🏠 HOUSE RULES ANALYSIS:")
        print("-" * 40)
        
        rule_items = [item for item in knowledge_items if item.get('type') == 'rule']
        print(f"Knowledge items with type 'rule': {len(rule_items)}")
        
        imported_rule_items = [item for item in rule_items if 'imported' in item.get('tags', [])]
        print(f"Imported rule items: {len(imported_rule_items)}")
        
        print(f"\nDetailed rule analysis:")
        for i, item in enumerate(rule_items):
            content = item.get('content', '')
            tags = item.get('tags', [])
            status = item.get('status', 'N/A')
            source = item.get('source', 'N/A')
            
            # Check if it's a time-related rule that should update property times
            is_time_rule = any(keyword in content.lower() for keyword in ['check-in', 'checkout', 'check in', 'check out'])
            
            print(f"  {i+1}. Content: {content}")
            print(f"      Status: {status}")
            print(f"      Source: {source}")
            print(f"      Tags: {tags}")
            print(f"      Time-related: {is_time_rule}")
            print()
        
        # 4. Check what should have been filtered out
        print(f"\n🔍 FILTERING ANALYSIS:")
        print("-" * 40)
        
        time_related_rules = [item for item in rule_items if any(keyword in item.get('content', '').lower() for keyword in ['check-in', 'checkout', 'check in', 'check out'])]
        print(f"Time-related rules that should update property times: {len(time_related_rules)}")
        
        for rule in time_related_rules:
            content = rule.get('content', '')
            print(f"  - {content}")
            
            # Check if property times were actually updated
            if 'check-in' in content.lower() or 'check in' in content.lower():
                print(f"    → Should have updated checkInTime: {property_data.get('checkInTime', 'NOT UPDATED')}")
            elif 'checkout' in content.lower() or 'check out' in content.lower():
                print(f"    → Should have updated checkOutTime: {property_data.get('checkOutTime', 'NOT UPDATED')}")
        
        # 5. Summary
        print(f"\n📊 SUMMARY:")
        print("-" * 40)
        print(f"Property record: ✅ Found")
        print(f"Total knowledge items: {len(knowledge_items)}")
        print(f"Rule-type knowledge items: {len(rule_items)}")
        print(f"Imported rules: {len(imported_rule_items)}")
        print(f"Time-related rules (should be filtered): {len(time_related_rules)}")
        print(f"Property check-in time: {property_data.get('checkInTime', 'N/A')}")
        print(f"Property check-out time: {property_data.get('checkOutTime', 'N/A')}")
        
        # 6. UI Display Analysis
        print(f"\n🖥️ UI DISPLAY ANALYSIS:")
        print("-" * 40)
        
        # Based on the logs, the UI shows:
        # - 8 rule items found in knowledge
        # - 7 valid imported rules after filtering
        # - 3 conflicts detected with default rules
        
        print(f"From console logs:")
        print(f"  - Found rule items in knowledge: 8")
        print(f"  - Filtered to valid imported rules: 7")
        print(f"  - Conflicts with default rules: 3")
        print(f"  - Total unified rules shown: 14 (7 imported + 7 default)")
        
        conflicts = [
            ("No pets", "Pets allowed"),
            ("Check-in time", "Check-in after 3:00 PM"),
            ("Maximum occupancy", "5 guests maximum")
        ]
        
        print(f"\nDetected conflicts:")
        for default_rule, imported_rule in conflicts:
            print(f"  - Default: '{default_rule}' vs Imported: '{imported_rule}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_property_data()
