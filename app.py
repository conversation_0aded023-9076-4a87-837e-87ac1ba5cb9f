# This file serves as an entry point for Elastic Beanstalk
# It imports the Flask application from the 'concierge' package
# and exposes it as 'application' which is what Elastic Beanstalk expects

import os
import sys

# Add the current directory to the path so that 'concierge' can be imported
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the Flask application
from concierge.app import app as application

if __name__ == '__main__':
    # This section is only executed when running the file directly
    # (not when imported by Elastic Beanstalk)
    port = int(os.environ.get('PORT', 8081))  # Default to 8081 instead of 8082
    application.run(host='0.0.0.0', port=port)