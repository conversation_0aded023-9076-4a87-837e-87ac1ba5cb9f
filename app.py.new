# Flask application entry point
import os
import sys

# Add the current directory to the path so that 'concierge' can be imported
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the Flask application
from concierge.app import app

if __name__ == '__main__':
    # This section is only executed when running the file directly
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)
