#!/usr/bin/env python3
"""
Script to check what conversations exist in DynamoDB and their structure.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import boto3
from botocore.exceptions import ClientError
from concierge.config import AWS_REGION

def check_conversations():
    """Check what conversations exist in DynamoDB."""
    
    old_uid = "BqVhdHwSbuhLfEsOJIRf4XLYl9T2"  # Old Firestore UID
    new_uid = "8LnLzt6W9JM3sGFsPWvbGoVTrS32"  # New Firebase Auth UID
    
    # Use the same table name configuration as the DynamoDB client
    conversations_table_name = os.environ.get('CONVERSATIONS_TABLE_NAME', 'Conversations')
    
    print("=== DynamoDB Conversation Check ===\n")
    
    # Initialize DynamoDB client
    try:
        region = AWS_REGION
        dynamodb = boto3.resource('dynamodb', region_name=region)
        conversations_table = dynamodb.Table(conversations_table_name)
        print(f"✅ Connected to DynamoDB in region: {region}")
        print(f"✅ Using table: {conversations_table_name}")
    except Exception as e:
        print(f"❌ ERROR: Failed to connect to DynamoDB: {e}")
        return False
    
    # Check for conversations with old UID in any field
    print(f"\n1. Checking for conversations with old UID anywhere: {old_uid}")
    try:
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('user_id').eq(old_uid)
        )
        old_conversations = response.get('Items', [])
        print(f"   Found {len(old_conversations)} conversations with old UID in user_id field")
        
        # Also check if the UID appears in PK or SK
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('PK').contains(old_uid)
        )
        pk_conversations = response.get('Items', [])
        print(f"   Found {len(pk_conversations)} conversations with old UID in PK")
        
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('SK').contains(old_uid)
        )
        sk_conversations = response.get('Items', [])
        print(f"   Found {len(sk_conversations)} conversations with old UID in SK")
        
        if len(sk_conversations) > 0:
            print(f"   Details of conversations with old UID in SK:")
            for i, conv in enumerate(sk_conversations, 1):
                pk = conv.get('PK', 'Unknown')
                sk = conv.get('SK', 'Unknown')
                message_count = conv.get('MessageCount', 0)
                last_update = conv.get('LastUpdateTime', 'Unknown')
                print(f"   {i}. PK: {pk}")
                print(f"      SK: {sk}")
                print(f"      Messages: {message_count}")
                print(f"      Last Update: {last_update}")
                print()
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Check for conversations with new UID in any field
    print(f"\n2. Checking for conversations with new UID anywhere: {new_uid}")
    try:
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('user_id').eq(new_uid)
        )
        new_conversations = response.get('Items', [])
        print(f"   Found {len(new_conversations)} conversations with new UID in user_id field")
        
        # Also check if the UID appears in PK or SK
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('PK').contains(new_uid)
        )
        pk_conversations = response.get('Items', [])
        print(f"   Found {len(pk_conversations)} conversations with new UID in PK")
        
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('SK').contains(new_uid)
        )
        sk_conversations = response.get('Items', [])
        print(f"   Found {len(sk_conversations)} conversations with new UID in SK")
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        
    # Check for conversations mentioning "Aleksei"
    print(f"\n2.5. Checking for conversations mentioning 'Aleksei':")
    try:
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('Messages').contains('Aleksei')
        )
        aleksei_conversations = response.get('Items', [])
        print(f"   Found {len(aleksei_conversations)} conversations mentioning Aleksei")
        
        if len(aleksei_conversations) > 0:
            print(f"   First few Aleksei conversations:")
            for i, conv in enumerate(aleksei_conversations[:3], 1):
                pk = conv.get('PK', 'Unknown')
                sk = conv.get('SK', 'Unknown')
                message_count = conv.get('MessageCount', 0)
                print(f"   {i}. PK: {pk}, SK: {sk}, Messages: {message_count}")
        
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Get a sample of recent conversations to see the structure
    print(f"\n3. Checking recent conversations (sample of 2 with full structure):")
    try:
        response = conversations_table.scan(Limit=2)
        sample_conversations = response.get('Items', [])
        print(f"   Found {len(sample_conversations)} sample conversations")
        
        for i, conv in enumerate(sample_conversations, 1):
            print(f"   {i}. Full conversation structure:")
            for key, value in conv.items():
                # Truncate long values for readability
                if isinstance(value, str) and len(value) > 50:
                    display_value = value[:50] + "..."
                else:
                    display_value = value
                print(f"      {key}: {display_value}")
            print()
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Check total conversation count
    print(f"\n4. Total conversation count:")
    try:
        response = conversations_table.scan(Select='COUNT')
        total_count = response.get('Count', 0)
        print(f"   Total conversations in table: {total_count}")
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    return True

if __name__ == "__main__":
    try:
        check_conversations()
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc() 