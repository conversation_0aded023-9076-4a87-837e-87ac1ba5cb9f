[{"listing": {"url": "https://www.airbnb.com/rooms/700299802944952028", "name": "<PERSON> (Previous Test)", "expected_improvements": ["Coffee should be basic amenity, not appliance", "No duplicate guest maximum rules", "Compiled 'Before you leave' instructions", "No conflicting party rules"]}, "extraction_time": 92.79067802429199, "house_rules_count": 0, "basic_amenities_count": 31, "appliances_count": 11, "quality_score": 60, "quality_issues": ["Coffee misclassified as appliance", "Low rule count: 0"], "guest_max_rules": 0, "before_leave_rules": 0, "party_rules": 0, "coffee_misclassified": true, "house_rules": [], "basic_amenities": ["Kitchen", "Wifi", "Dedicated workspace", "Free parking on premises", "Pets allowed", "Air conditioning", "Private backyard", "Shampoo", "Body soap", "Hot water", "Shower gel", "Hangers", "Bed linens", "Extra pillows and blankets", "Room-darkening shades", "Iron", "Safe", "Clothing storage", "Heating", "Smoke alarm"], "appliances": [{"name": "TV", "location": "Living Room", "brand": "", "model": ""}, {"name": "<PERSON>her", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Dryer", "location": "<PERSON><PERSON><PERSON>", "brand": "", "model": ""}, {"name": "Hair dryer", "location": "Bathroom", "brand": "", "model": ""}, {"name": "Crib - always at the listing", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Refrigerator", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Microwave", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Dishwasher", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Oven", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Coffee maker: Keurig coffee machine", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Coffee", "location": "Kitchen", "brand": "", "model": ""}]}, {"listing": {"url": "https://www.airbnb.com/rooms/53339040", "name": "Fresh Test Property 1", "expected_improvements": ["Proper amenity classification", "Deduplicated rules", "Compiled checkout instructions"]}, "extraction_time": 25.501710891723633, "house_rules_count": 2, "basic_amenities_count": 24, "appliances_count": 5, "quality_score": 80, "quality_issues": ["Low rule count: 2"], "guest_max_rules": 0, "before_leave_rules": 0, "party_rules": 0, "coffee_misclassified": false, "house_rules": [{"title": "KitchenWifiDedicated workspaceFree parking on premisesPets allowed", "description": "KitchenWifiDedicated workspaceFree parking on premisesPets allowed", "enabled": true, "type": "rule", "source": "airbnb_house_rules_page"}, {"title": "Pets allowed", "description": "Pets allowed", "enabled": true, "type": "rule", "source": "airbnb_house_rules_page"}], "basic_amenities": ["Kitchen", "WiFi", "Dedicated workspace", "Free parking on premises", "Pets allowed", "Central air conditioning", "Patio or balcony", "Outdoor shower", "<PERSON><PERSON><PERSON>", "Pack 'n play/Travel crib", "High chair", "Indoor fireplace", "Heating", "Smoke alarm", "Carbon monoxide alarm", "Fire extinguisher", "Dishes and silverware", "Backyard", "Fire pit", "Outdoor dining area"], "appliances": [{"name": "TV", "location": "Living Room", "brand": "", "model": ""}, {"name": "<PERSON>her", "location": "In unit", "brand": "", "model": ""}, {"name": "Dryer", "location": "<PERSON><PERSON><PERSON>", "brand": "", "model": ""}, {"name": "Refrigerator", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Coffee maker", "location": "Kitchen", "brand": "", "model": ""}]}, {"listing": {"url": "https://www.airbnb.com/rooms/20669368", "name": "Fresh Test Property 2", "expected_improvements": ["Accurate rule extraction", "No misclassified amenities", "Clean rule descriptions"]}, "extraction_time": 27.8433620929718, "house_rules_count": 3, "basic_amenities_count": 26, "appliances_count": 9, "quality_score": 80, "quality_issues": ["Low rule count: 3"], "guest_max_rules": 0, "before_leave_rules": 0, "party_rules": 0, "coffee_misclassified": false, "house_rules": [{"title": "One of the most loved homes...", "description": "One of the most loved homes on Airbnb, according to guests", "enabled": true, "type": "rule", "source": "airbnb_aggressive_extraction"}, {"title": "Check-in", "description": "Recent guests gave the check-in process a 5-star rating.", "enabled": true, "type": "rule", "source": "airbnb_aggressive_extraction"}, {"title": "Located at the end of our...", "description": "Located at the end of our small touring campsite there are 3  cabins each having their own large fenced areas to provide privacy for guests", "enabled": true, "type": "rule", "source": "airbnb_aggressive_extraction"}], "basic_amenities": ["Free parking on premises", "Hot tub", "Patio or balcony", "Indoor fireplace", "Fire pit", "Carbon monoxide alarm", "Cleaning products", "Hot water", "Hangers", "Bed linens", "Heating", "Smoke alarm", "Fire extinguisher", "First aid kit", "Cooking basics", "Dishes and silverware", "Barbecue utensils", "Kitchenette", "Outdoor furniture", "BBQ grill"], "appliances": [{"name": "TV", "location": "Living Room", "brand": "", "model": ""}, {"name": "Hair dryer", "location": "Bathroom", "brand": "", "model": ""}, {"name": "Refrigerator", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Microwave", "location": "Kitchen", "brand": "", "model": ""}, {"name": "<PERSON><PERSON>", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Oven", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Hot water kettle", "location": "Kitchen", "brand": "", "model": ""}, {"name": "Toaster", "location": "Kitchen", "brand": "", "model": ""}, {"name": "<PERSON>her", "location": "<PERSON><PERSON><PERSON>", "brand": "", "model": ""}]}]