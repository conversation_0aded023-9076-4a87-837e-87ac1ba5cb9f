# Concierge AI Host Assistant

## Quick Development Setup

To run the application locally:

1. **<PERSON>reate and activate a virtual environment:**
   ```bash
   python3 -m venv .venv
   source .venv/bin/activate  # On macOS/Linux
   # or
   .venv\Scripts\activate     # On Windows
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the development server:**
   ```bash
   python3 app.py
   ```
   
   The server will start on port 8081 by default. Access it at: http://localhost:8081

## Requirements Files

- **`concierge/requirements.txt`** - Main application dependencies (use this for local development)
- **`../requirements.txt`** - Root level requirements (for deployment configurations)

---

## Overview

An AI-powered concierge application for independent hotels and individual property owners. This application allows logged-in users to view their reservation information and interact with an AI agent that helps them with basic questions and necessary changes to their reservations.

## Features

- Phone number authentication via Firebase
- Reservation viewing and management
- AI-powered chat assistant using Google Gemini
- Responsive design for mobile and desktop
- Vector search capabilities for efficient knowledge retrieval

## Tech Stack

- **Backend**: Python with Flask
- **Frontend**: HTML, CSS, JavaScript
- **Authentication**: Firebase Phone Authentication
- **Database**: Google Cloud Firestore
- **Vector Search**: Firestore Vector Search
- **AI Conversational Agent**: Google Gemini
- **Deployment**: Google Cloud Platform

## Prerequisites

- Python 3.8 or higher
- Google Cloud Platform account
- Firebase account (can be created with your Google account)
- Node.js and npm (for Firebase CLI if needed)

## Local Setup Instructions

### 1. Clone the repository

```bash
git clone https://github.com/aborov/concierge.git
cd concierge
```

### 2. Create a virtual environment and install dependencies

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Set up Firebase and Firestore

#### Firebase Project Setup

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" and follow the steps to create a new project
3. Once your project is created:
   - Click on "Authentication" in the left sidebar
   - Go to the "Sign-in method" tab and enable "Phone" as a sign-in provider
   - Add your phone number for testing in the "Phone numbers for testing" section
4. Set up Firestore:
   - Click on "Firestore Database" in the left sidebar
   - Click "Create database"
   - Choose "Start in production mode"
   - Select a location closest to your users
   - Click "Enable"

#### Firebase Web App Setup

1. In your Firebase project, click on the gear icon next to "Project Overview" and select "Project settings"
2. Scroll down to "Your apps" section and click the web icon (</>) to add a web app
3. Register your app with a nickname (e.g., "Guestrix")
4. Copy the Firebase configuration object (it contains apiKey, authDomain, etc.)
5. Open `static/js/auth.js` in your project and replace the `firebaseConfig` object with your configuration

#### Firebase Admin SDK Setup

1. In Firebase Project settings, go to the "Service accounts" tab
2. Click "Generate new private key" to download a JSON file with your service account credentials
3. Save this file securely in your project directory (e.g., as `firebase-service-account.json`)
4. Make sure this file is in your `.gitignore` to prevent it from being committed to version control

### 4. Set up Google Cloud and Gemini

#### Google Cloud Project Setup

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Make sure you're using the same project as your Firebase project
3. Enable the following APIs:
   - Gemini API
   - Firestore API
   - Cloud Functions API (if using serverless functions)

#### Set up Vector Search in Firestore

1. Create a vector index in Firestore:
   ```bash
   python create_firestore_vector_index.py
   ```
   This script will create the necessary vector index for efficient similarity search.

### 5. Configure Environment Variables

1. Create a `.env` file in your project root:
   ```bash
   touch .env
   ```

2. Add the following variables to your `.env` file:
   ```
   # Flask settings
   SECRET_KEY=your_secure_random_string
   PORT=8080

   # Firebase settings
   FIREBASE_SERVICE_ACCOUNT_PATH=./path/to/your/firebase-service-account.json
   FIREBASE_PROJECT_ID=your-firebase-project-id

   # Google Cloud settings
   GOOGLE_CLOUD_PROJECT_ID=your-google-cloud-project-id
   GOOGLE_APPLICATION_CREDENTIALS=./path/to/your/google-cloud-credentials.json

   # Gemini settings
   GEMINI_API_KEY=your-gemini-api-key
   ```

### 6. Initialize the Database

1. Run the database initialization script:
   ```bash
   python scripts/init_db.py
   ```
   This will create the necessary collections and indexes in Firestore.

### 7. Run the Application

```bash
python app.py
```

Access the application at `http://localhost:8080`

## Development Workflow

1. Make sure you're in the virtual environment:
   ```bash
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Run tests before committing changes:
   ```bash
   python -m pytest tests/
   ```

3. Check for linting issues:
   ```bash
   flake8 .
   ```

## Deployment

The application can be deployed to Google Cloud Platform using the provided deployment scripts:

1. Update the configuration in `deploy_flask_app.sh`
2. Run the deployment script:
   ```bash
   ./deploy_flask_app.sh
   ```

## Troubleshooting

### Common Issues

1. **Firebase Authentication Issues**
   - Ensure your Firebase configuration is correct in `static/js/auth.js`
   - Check that your service account credentials are properly set up
   - Verify that phone authentication is enabled in Firebase Console

2. **Firestore Connection Issues**
   - Verify your service account has the necessary permissions
   - Check that your Firestore rules allow the required operations
   - Ensure your IP address is allowed in Firebase Console

3. **Vector Search Issues**
   - Verify that the vector index was created successfully
   - Check the index status in Firebase Console
   - Ensure your vectors are properly formatted