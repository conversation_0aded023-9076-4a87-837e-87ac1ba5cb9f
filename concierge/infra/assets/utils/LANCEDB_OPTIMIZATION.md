# LanceDB S3 Optimization Guide

This guide provides information on how to optimize LanceDB S3 usage to reduce costs and improve performance.

## Background

LanceDB is a vector database that can be used with an S3 backend for storage. When using LanceDB with S3, each query can generate multiple S3 requests, which can lead to high costs if not optimized properly.

## Implemented Optimizations

The following optimizations have been implemented to reduce S3 costs:

1. **Connection Pooling**: Reuse LanceDB connections across requests to avoid repeated S3 metadata lookups.
2. **Result Caching**: Cache query results to avoid redundant queries for the same property/query combinations.
3. **Table Schema Caching**: Cache table schemas to avoid repeated S3 metadata lookups.
4. **Batch Querying**: Batch similar queries together to reduce the number of S3 requests.
5. **Optimized Query Logic**: Refactored query logic to reduce unnecessary S3 requests.
6. **Increased Cache TTL**: Extended cache TTL to 30 minutes (1800 seconds) for stable data.
7. **Larger Cache Size**: Increased cache size to 200 entries to accommodate more cached results.

## Configuration Options

You can configure the following environment variables to optimize LanceDB S3 usage:

- `LANCEDB_CACHE_SIZE`: Maximum number of items to store in the cache (default: 200)
- `LANCEDB_CACHE_TTL`: Time-to-live in seconds for cache entries (default: 1800)
- `LANCEDB_S3_URI`: URI for the LanceDB S3 backend (e.g., `s3://bucket-name/lancedb`)
- `LANCEDB_TABLE_NAME`: Name of the LanceDB table to use (default: `knowledge_base`)

## Monitoring Tools

Two utility scripts have been provided to help monitor and optimize LanceDB S3 usage:

### 1. LanceDB Monitor (`lancedb_monitor.py`)

This script helps monitor and analyze LanceDB S3 usage to identify optimization opportunities.

```bash
# Check LanceDB settings
python -m concierge.utils.lancedb_monitor --check-settings

# Analyze S3 usage over the last 7 days
python -m concierge.utils.lancedb_monitor --analyze --days 7

# Analyze S3 usage for a specific bucket
python -m concierge.utils.lancedb_monitor --analyze --bucket your-bucket-name
```

### 2. Batch Ingestion Utility (`batch_ingestion.py`)

This script provides utilities for batch ingestion of knowledge items into LanceDB to reduce the number of S3 requests during ingestion.

```bash
# Ingest items from a JSON file
python -m concierge.utils.batch_ingestion --file items.json

# Ingest items with custom settings
python -m concierge.utils.batch_ingestion --file items.json --uri s3://your-bucket/lancedb --table your_table --batch-size 20
```

## Best Practices

To minimize S3 costs when using LanceDB, follow these best practices:

1. **Use Connection Pooling**: Always reuse LanceDB connections across requests.
2. **Cache Results**: Cache query results to avoid redundant queries.
3. **Batch Queries**: Batch similar queries together to reduce S3 requests.
4. **Limit Result Size**: Limit the number of results returned by queries.
5. **Use Appropriate TTL**: Set cache TTL based on how frequently your data changes.
6. **Monitor Usage**: Regularly monitor S3 usage to identify optimization opportunities.
7. **Optimize Ingestion**: Use batch ingestion for adding multiple items to LanceDB.

## Troubleshooting

If you're experiencing high S3 costs despite these optimizations, check the following:

1. **Cache Hit Rate**: Use the `lancedb_monitor.py` script to check the cache hit rate. A low hit rate may indicate that the cache is not being used effectively.
2. **Connection Reuse**: Make sure connections are being reused across requests.
3. **Query Patterns**: Analyze query patterns to identify opportunities for batching.
4. **Cache Size**: Increase cache size if you have a large number of unique queries.
5. **Cache TTL**: Adjust cache TTL based on how frequently your data changes.

## Additional Resources

- [LanceDB Documentation](https://lancedb.github.io/lancedb/)
- [AWS S3 Pricing](https://aws.amazon.com/s3/pricing/)
- [AWS CloudWatch Metrics for S3](https://docs.aws.amazon.com/AmazonS3/latest/userguide/metrics-dimensions.html)
