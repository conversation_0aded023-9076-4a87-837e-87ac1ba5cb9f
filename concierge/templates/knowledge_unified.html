{% extends 'base.html' %}

{% block title %}Knowledge Management: {{ property.name }}{% endblock %}

{% block styles %}
<style>
    /* Styles for the file upload area */
    #addKnowledgeModal .file-dropzone {
        border: 1px dashed #ccc;
        padding: 30px 20px;
        text-align: center;
        margin: 0 0 15px 0;
        background-color: #fff;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        width: 100%;
    }

    /* Add hover effect */
    #addKnowledgeModal .file-dropzone:hover {
        border-color: #999;
    }

    /* Add highlight effect for drag over */
    #addKnowledgeModal .file-dropzone.highlight {
        border-color: #007bff;
        background-color: #f8f9fa;
    }
    #addKnowledgeModal .file-dropzone p {
        color: #666;
        margin-bottom: 15px;
        font-size: 14px;
    }
    #addKnowledgeModal .browse-btn {
        background-color: #f8f9fa;
        border: 1px solid #000;
        color: #000;
        padding: 2px 12px;
        font-size: 13px;
        border-radius: 0;
        cursor: pointer;
    }

    #addKnowledgeModal .browse-btn:hover {
        background-color: #e9ecef;
    }
    .knowledge-item {
        transition: all 0.2s ease;
    }
    .knowledge-item:hover {
        background-color: #f8f9fa;
    }
    /* Status badge styles moved to style.css */
    .item-actions {
        white-space: nowrap;
    }

    /* Styles for the content cell */
    .content-cell {
        max-width: 500px;
        white-space: pre-wrap;
        word-break: break-word;
        padding: 10px !important;
        line-height: 1.5;
    }

    /* Adjust table layout */
    #knowledgeTable th:nth-child(1) { width: 10%; } /* Type */
    #knowledgeTable th:nth-child(2) { width: 15%; } /* Tags */
    #knowledgeTable th:nth-child(3) { width: 50%; } /* Content */
    #knowledgeTable th:nth-child(4) { width: 10%; } /* Status */
    #knowledgeTable th:nth-child(5) { width: 15%; } /* Actions */
    .filter-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        transition: opacity 0.3s ease;
        will-change: opacity;
    }
    .loading-overlay.active {
        display: flex !important;
        opacity: 1 !important;
    }
    .loading-overlay .spinner-border {
        width: 5rem;
        height: 5rem;
        border-width: 0.4rem;
    }
    .loading-overlay .spinner-container {
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 15px;
        padding: 2.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    }
    .loading-overlay .spinner-text {
        color: white;
        margin-top: 1.5rem;
        font-weight: 500;
        font-size: 1.1rem;
    }

    /* Highlighting for pending items */
    .pending-item {
        background-color: #fff3cd !important;
        border-left: 4px solid #ffc107 !important;
    }

    .pending-item:hover {
        background-color: #ffeaa7 !important;
    }

    /* Pulse animation for approve button */
    .pulse-button {
        animation: pulse 2s infinite;
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7);
    }

    @keyframes pulse {
        0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7);
        }

        70% {
            transform: scale(1);
            box-shadow: 0 0 0 10px rgba(25, 135, 84, 0);
        }

        100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4" data-property-id="{{ property.id }}" data-property-name="{{ property.name|e }}">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('views.dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('views.properties_list') }}">Properties</a></li>
            <li class="breadcrumb-item active" aria-current="page">Knowledge: {{ property.name }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Knowledge Management</h1>
        <div>
            <button class="btn btn-primary" id="addKnowledgeBtn">
                <i class="bi bi-plus-circle me-1"></i> Add Knowledge
            </button>
            <button class="btn btn-outline-danger ms-2" id="deleteAllKnowledgeBtn" data-property-id="{{ property.id }}" data-property-name="{{ property.name|e }}">
                <i class="bi bi-trash-fill me-1"></i> Delete All
            </button>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Property: {{ property.name }} ({{ property.address }})</h5>
        </div>
        <div class="card-body">
            <p class="mb-0"><strong>Host:</strong> {{ host_name }}</p>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section mb-4">
        <div class="row">
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Status</label>
                <select class="form-select" id="statusFilter">
                    <option value="all" selected>All</option>
                    <option value="pending">Pending Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                    <option value="error">Error</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="typeFilter" class="form-label">Type</label>
                <select class="form-select" id="typeFilter">
                    <option value="all" selected>All</option>
                    <option value="information">Information</option>
                    <option value="rule">Rule</option>
                    <option value="instruction">Instruction</option>
                    <option value="amenity">Amenity</option>
                    <option value="places">Places</option>
                </select>
            </div>
            <div class="col-md-6">
                <label for="searchFilter" class="form-label">Search</label>
                <input type="text" class="form-control" id="searchFilter" placeholder="Search in content or tags...">
            </div>
        </div>
    </div>

    <!-- Knowledge Items List -->
    <div class="card">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Knowledge Items</h5>
                <span id="itemCount" class="badge bg-secondary">0 items</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="knowledgeTable">
                    <thead class="table-light">
                        <tr>
                            <th>Type</th>
                            <th>Tags</th>
                            <th>Content</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="knowledgeItems">
                        <!-- Items will be loaded here via JavaScript -->
                        <tr>
                            <td colspan="5" class="text-center py-4">Loading knowledge items...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <select class="form-select form-select-sm d-inline-block w-auto" id="itemsPerPage">
                        <option value="10">10 per page</option>
                        <option value="25" selected>25 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                    </select>
                </div>
                <nav aria-label="Knowledge pagination">
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                        <!-- Pagination will be added here via JavaScript -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Add Knowledge Modal -->
<div class="modal fade" id="addKnowledgeModal" tabindex="-1" aria-labelledby="addKnowledgeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addKnowledgeModalLabel">Add Knowledge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Important Note -->
                <div class="alert alert-info mb-3" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Important:</strong> After adding knowledge, remember to review and approve the generated items in the table below so Staycee can use them to help your guests.
                </div>

                <ul class="nav nav-tabs" id="addKnowledgeTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-tab-pane" type="button" role="tab" aria-controls="upload-tab-pane" aria-selected="true">Upload File</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-tab-pane" type="button" role="tab" aria-controls="text-tab-pane" aria-selected="false">Add Raw Text</button>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="addKnowledgeTabContent">
                    <!-- File Upload Tab -->
                    <div class="tab-pane fade show active" id="upload-tab-pane" role="tabpanel" aria-labelledby="upload-tab" tabindex="0">
                        <form id="fileUploadForm" method="POST" action="{{ url_for('views.knowledge_upload', property_id=property.id) }}" enctype="multipart/form-data">
                            <div class="file-dropzone" id="dropzone">
                                <p>Drop files or click to upload</p>
                                <button type="button" class="browse-btn" onclick="document.getElementById('knowledgeFile').click()">
                                    Browse
                                </button>
                                <input type="file" id="knowledgeFile" name="knowledgeFile" accept=".pdf,.docx,.txt,.xlsx" class="d-none">
                            </div>

                            <div class="text-center mb-3">
                                <small class="text-muted">Supported formats: PDF, DOCX, TXT, XLSX</small>
                            </div>

                            <div id="filePreview" class="mb-3 d-none">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-file-earmark fs-1 me-3"></i>
                                            <div>
                                                <h6 id="fileName" class="mb-1">filename.pdf</h6>
                                                <p id="fileSize" class="small text-muted mb-0">Size: 1.2 MB</p>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-danger ms-auto" id="removeFile">
                                                <i class="bi bi-x"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-primary px-4" id="uploadFileBtn" disabled>Upload & Process</button>
                            </div>
                        </form>
                    </div>

                    <!-- Raw Text Tab -->
                    <div class="tab-pane fade" id="text-tab-pane" role="tabpanel" aria-labelledby="text-tab" tabindex="0">
                        <form id="textForm" method="POST" action="{{ url_for('views.knowledge_add_text', property_id=property.id) }}">
                            <div class="mb-3">
                                <label for="knowledge_text" class="form-label">Paste or type text</label>
                                <textarea class="form-control" id="knowledge_text" name="knowledge_text" rows="12" required placeholder="Enter your knowledge content here..."></textarea>
                            </div>
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">Add Text & Process</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Item Modal -->
<div class="modal fade" id="editItemModal" tabindex="-1" aria-labelledby="editItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editItemModalLabel">Edit Knowledge Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editItemForm">
                    <input type="hidden" id="editItemId">
                    <div class="mb-3">
                        <label for="editType" class="form-label">Type</label>
                        <select class="form-select" id="editType" required>
                            <option value="information">Information</option>
                            <option value="rule">Rule</option>
                            <option value="instruction">Instruction</option>
                            <option value="amenity">Amenity</option>
                            <option value="places">Places</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editTags" class="form-label">Tags (comma-separated)</label>
                        <input type="text" class="form-control" id="editTags" placeholder="Enter tags separated by commas">
                    </div>
                    <div class="mb-3">
                        <label for="editContent" class="form-label">Content</label>
                        <textarea class="form-control" id="editContent" rows="8" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveItemBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="spinner-container">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="spinner-text" id="loadingText">Processing...</div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/knowledge-unified.js') }}"></script>
{% endblock %}
