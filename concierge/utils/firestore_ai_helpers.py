"""
AI helper functions for Concierge application using Firestore for RAG.
This module provides functions for retrieving relevant context from Firestore
and generating responses using Gemini.
"""

import os
import logging
import time
import traceback
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
import json

# Import Firestore client
from concierge.utils.firestore_client import (
    initialize_firebase, get_firestore_client, find_similar_knowledge_items,
    generate_embedding, configure_gemini
)

# Import Gemini
try:
    import google.generativeai as genai
except ImportError:
    genai = None
    logging.warning("google.generativeai module not imported - AI functions will fail!")

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Constants
MAX_RESULTS = 10
SIMILARITY_THRESHOLD = 0.7  # Minimum similarity score (0-1) to include in results

# Global variables
firestore_db = None
gen_ai_client = None

# Simple in-memory cache for RAG results
rag_cache = {}

def get_relevant_context(query_text: str, property_id: str, limit: int = MAX_RESULTS, threshold: float = SIMILARITY_THRESHOLD) -> Dict:
    """
    Retrieve relevant context from Firestore based on query text and property ID.

    Args:
        query_text: The user's query or utterance
        property_id: The ID of the property to filter by
        limit: Maximum number of results to return
        threshold: Similarity threshold (0-1)

    Returns:
        dict: {
            'found': bool,
            'context': str,
            'items': list of dict with relevant items
        }
    """
    results = {
        'found': False,
        'context': "",
        'items': []
    }

    # Check cache first
    cache_key = f"{property_id}:{query_text}"
    if cache_key in rag_cache:
        logger.info(f"Using cached RAG results for query: {query_text[:50]}...")
        return rag_cache[cache_key]

    try:
        # Find similar knowledge items in Firestore
        similar_items = find_similar_knowledge_items(query_text, property_id, limit)

        if not similar_items:
            logger.info(f"No relevant context found for property: {property_id}")
            # Cache the empty results too to avoid redundant queries
            rag_cache[cache_key] = results
            return results

        # Process results
        context_parts = []
        items = []

        for item in similar_items:
            # Skip items with low similarity
            similarity = item.get('similarity', 0)
            if similarity < threshold:
                continue

            # Create item dictionary
            item_dict = {
                'id': item.get('id', 'unknown'),
                'text': item.get('content', ''),
                'similarity': similarity
            }
            items.append(item_dict)
            context_parts.append(item.get('content', ''))

        # If no items passed the threshold, return empty results
        if not items:
            logger.info(f"No items passed the similarity threshold ({threshold})")
            rag_cache[cache_key] = results
            return results

        # Join context parts
        context = "\n\n".join(context_parts)

        results['found'] = True
        results['context'] = context
        results['items'] = items

        # Cache the successful results
        rag_cache[cache_key] = results

        return results

    except Exception as e:
        logger.error(f"Error retrieving relevant context: {e}")
        traceback.print_exc()
        return results

def create_base_prompt(property_context: Dict) -> str:
    """
    Create a base prompt with property information.

    Args:
        property_context: Dictionary containing property information

    Returns:
        str: The base prompt
    """
    if not property_context:
        return "You are Guestrix, an AI assistant for vacation rental guests."

    property_name = property_context.get('name', 'this property')
    host_name = property_context.get('hostName', 'the host')

    prompt = f"""You are Guestrix, an AI assistant for guests staying at {property_name}.
Your role is to provide helpful, accurate information about the property and assist guests during their stay.

PROPERTY INFORMATION:
- Property Name: {property_name}
- Host: {host_name}
"""

    # Add address if available
    if 'address' in property_context and property_context['address']:
        prompt += f"- Address: {property_context['address']}\n"

    # Add description if available
    if 'description' in property_context and property_context['description']:
        prompt += f"\nPROPERTY DESCRIPTION:\n{property_context['description']}\n"

    prompt += "\nGUIDELINES:\n"
    prompt += "- Be friendly, helpful, and concise in your responses.\n"
    prompt += "- If you don't know the answer to a question, politely say so and suggest contacting the host.\n"
    prompt += "- Never make up information about the property.\n"
    prompt += "- Prioritize information from the property knowledge base over general knowledge.\n"

    return prompt

def format_prompt_with_rag(user_query: str, property_context: Dict, rag_results: Dict, conversation_history: List = None) -> str:
    """
    Format a prompt with RAG results and conversation history.

    Args:
        user_query: The user's query or message
        property_context: Dictionary containing property information
        rag_results: Results from get_relevant_context
        conversation_history: Previous conversation messages

    Returns:
        str: The formatted prompt
    """
    # Start with a base prompt with property information
    prompt_parts = [create_base_prompt(property_context)]

    # Add RAG context if available
    if rag_results and rag_results.get('found', False) and rag_results.get('items', []):
        rag_context = "PROPERTY KNOWLEDGE:\n\n"
        for item in rag_results.get('items', []):
            text = item.get('text', '').strip()
            # Skip empty items
            if not text:
                continue
            rag_context += f"{text}\n\n"
        prompt_parts.append(rag_context)
    else:
        prompt_parts.append("I don't have specific information about this property. If I can't answer a question, I'll let you know.")

    # Add conversation history if available
    if conversation_history and len(conversation_history) > 0:
        history_text = "CONVERSATION HISTORY:\n"
        for entry in conversation_history:
            role = "Guest" if entry.get('role') == 'user' else "Assistant"
            history_text += f"{role}: {entry.get('text', '')}\n\n"
        prompt_parts.append(history_text)

    # Add user query
    prompt_parts.append(f"GUEST QUERY: {user_query}")

    # Return the formatted prompt
    return "\n\n".join(prompt_parts)

def process_query_with_rag(user_query: str, property_id: str, property_context: Dict = None, conversation_history: List = None) -> Dict:
    """
    Process a user query with RAG and return a response from Gemini.

    Args:
        user_query: The user's query or message
        property_id: The property ID
        property_context: Context about the property
        conversation_history: Previous conversation messages

    Returns:
        dict: {
            'response': str,
            'has_context': bool,
            'context_used': list of context items used (if any)
        }
    """
    result = {
        'response': '',
        'has_context': False,
        'context_used': []
    }

    try:
        # Configure Gemini if not already configured
        if not genai:
            logger.error("Gemini API not available")
            result['response'] = "I'm sorry, I'm having trouble connecting to my knowledge base. Please try again later."
            return result

        if not configure_gemini():
            logger.error("Failed to configure Gemini")
            result['response'] = "I'm sorry, I'm having trouble connecting to my knowledge base. Please try again later."
            return result

        # Get relevant context from Firestore
        logger.info(f"Fetching context from Firestore for property_id: {property_id}")
        rag_results = get_relevant_context(user_query, property_id)

        # Log results of context lookup
        if rag_results.get('found'):
            logger.info(f"Found {len(rag_results.get('items', []))} relevant items in knowledge base")
            for idx, item in enumerate(rag_results.get('items', [])):
                logger.info(f"Context item {idx+1}: similarity={item.get('similarity', 0):.4f}, text='{item.get('text', '')[:100]}...'")
        else:
            logger.warning(f"No relevant context found in knowledge base for property_id: {property_id}")

        # Format prompt with RAG context and conversation history
        prompt = format_prompt_with_rag(
            user_query=user_query,
            property_context=property_context,
            rag_results=rag_results,
            conversation_history=conversation_history
        )

        # Generate response with Gemini
        try:
            # Create Google Search tool
            search_tool = {
                "name": "googleSearch",
                "description": "Search Google for information about places, events, facts, or anything else that might require up-to-date or general knowledge.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query"
                        }
                    },
                    "required": ["query"]
                }
            }

            # Configure Gemini model
            generation_config = {
                "temperature": 0.2,
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 1024,
            }

            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            ]

            # Initialize Gemini model
            model = genai.GenerativeModel(
                model_name="gemini-1.5-pro",
                generation_config=generation_config,
                safety_settings=safety_settings,
                tools=[search_tool]
            )

            # Generate response
            response = model.generate_content(prompt)

            # Extract response text
            if hasattr(response, 'text'):
                response_text = response.text
            else:
                response_text = str(response)

            # Set result values
            result['response'] = response_text
            result['has_context'] = rag_results.get('found', False)
            result['context_used'] = rag_results.get('items', [])

            return result

        except Exception as e:
            logger.error(f"Error generating response with Gemini: {e}")
            traceback.print_exc()
            result['response'] = "I'm sorry, I encountered an error while processing your request. Please try again later."
            return result

    except Exception as e:
        logger.error(f"Error in process_query_with_rag: {e}")
        traceback.print_exc()
        result['response'] = "I'm sorry, I encountered an error while processing your request. Please try again later."
        return result

def process_query_with_tools(user_query: str, property_id: str, property_context: Dict = None, conversation_history: List = None) -> Dict:
    """
    Process a user query with RAG and tools, returning a response from Gemini.
    This version supports function calling with tools.

    Args:
        user_query: The user's query or message
        property_id: The property ID
        property_context: Context about the property
        conversation_history: Previous conversation messages

    Returns:
        dict: {
            'response': str,
            'has_context': bool,
            'context_used': list of context items used (if any),
            'tool_calls': list of tool calls made (if any)
        }
    """
    result = {
        'response': '',
        'has_context': False,
        'context_used': [],
        'tool_calls': []
    }

    try:
        # Configure Gemini if not already configured
        if not genai:
            logger.error("Gemini API not available")
            result['response'] = "I'm sorry, I'm having trouble connecting to my knowledge base. Please try again later."
            return result

        if not configure_gemini():
            logger.error("Failed to configure Gemini")
            result['response'] = "I'm sorry, I'm having trouble connecting to my knowledge base. Please try again later."
            return result

        # Get relevant context from Firestore
        logger.info(f"Fetching context from Firestore for property_id: {property_id}")
        rag_results = get_relevant_context(user_query, property_id)

        # Format prompt with RAG context and conversation history
        prompt = format_prompt_with_rag(
            user_query=user_query,
            property_context=property_context,
            rag_results=rag_results,
            conversation_history=conversation_history
        )

        # Define RAG tool
        rag_tool = {
            "name": "retrievePropertyInfo",
            "description": "Retrieve specific information about the property from the knowledge base.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The specific information to retrieve about the property"
                    }
                },
                "required": ["query"]
            }
        }

        # Define Google Search tool
        search_tool = {
            "name": "googleSearch",
            "description": "Search Google for information about places, events, facts, or anything else that might require up-to-date or general knowledge.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query"
                    }
                },
                "required": ["query"]
            }
        }

        # Configure Gemini model
        generation_config = {
            "temperature": 0.2,
            "top_p": 0.95,
            "top_k": 40,
            "max_output_tokens": 1024,
        }

        safety_settings = [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
        ]

        # Initialize Gemini model with tools
        model = genai.GenerativeModel(
            model_name="gemini-1.5-pro",
            generation_config=generation_config,
            safety_settings=safety_settings,
            tools=[rag_tool, search_tool]
        )

        # Create chat session
        chat = model.start_chat(history=[])

        # Handle tool calls
        tool_calls = []

        # Function to handle the RAG tool
        def handle_rag_tool(query):
            rag_results = get_relevant_context(query, property_id)
            if rag_results.get('found', False):
                return rag_results.get('context', '')
            else:
                return "No specific information found about that in the property knowledge base."

        # Function to handle Google Search tool (mock implementation)
        def handle_google_search(query):
            return f"Search results for: {query}\n\nThis is a placeholder for Google Search results. In a real implementation, this would return actual search results."

        # Send message and handle any tool calls
        response = chat.send_message(prompt)

        # Process tool calls if any
        if hasattr(response, 'candidates') and response.candidates:
            for candidate in response.candidates:
                if hasattr(candidate, 'content') and candidate.content:
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call'):
                            function_call = part.function_call
                            tool_name = function_call.name
                            tool_args = json.loads(function_call.args)

                            # Record the tool call
                            tool_calls.append({
                                'name': tool_name,
                                'args': tool_args
                            })

                            # Handle the tool call
                            if tool_name == 'retrievePropertyInfo':
                                tool_result = handle_rag_tool(tool_args.get('query', ''))
                            elif tool_name == 'googleSearch':
                                tool_result = handle_google_search(tool_args.get('query', ''))
                            else:
                                tool_result = f"Unknown tool: {tool_name}"

                            # Send the tool result back to the model
                            response = chat.send_message(
                                genai.types.FunctionResponse(
                                    name=tool_name,
                                    response=json.dumps({'result': tool_result})
                                )
                            )

        # Extract final response text
        if hasattr(response, 'text'):
            response_text = response.text
        else:
            response_text = str(response)

        # Set result values
        result['response'] = response_text
        result['has_context'] = rag_results.get('found', False)
        result['context_used'] = rag_results.get('items', [])
        result['tool_calls'] = tool_calls

        return result

    except Exception as e:
        logger.error(f"Error in process_query_with_tools: {e}")
        traceback.print_exc()
        result['response'] = "I'm sorry, I encountered an error while processing your request. Please try again later."
        return result
