<!DOCTYPE html>
<html>
<head>
    <title>Debug Appliance Frontend Logic</title>
    <script>
        // Copy the exact logic from property-setup-modal.js
        function testApplianceLogic() {
            console.log("🧪 Testing Frontend Appliance Logic");
            
            // Kitchen appliances that should have location pre-populated (comprehensive list)
            const kitchenAppliances = [
                'Microwave', 'Dishwasher', 'Refrigerator', 'Fridge', 'Oven',
                'Stove', 'Cooktop', 'Toaster', 'Coffee maker', 'Coffee machine',
                'Espresso machine', 'Freezer', 'Blender', 'Food processor',
                'Electric kettle', 'Rice cooker', 'Slow cooker', 'Air fryer',
                'Stand mixer', 'Ice maker', 'Wine fridge', 'Range', 'Stovetop',
                'Garbage disposal', 'Can opener', 'Mixer', 'Juicer'
            ];

            // Appliances that should NOT get kitchen location (even if they're appliances)
            const nonKitchenAppliances = [
                'Washer', 'Washing machine', 'Dryer', 'Hair dryer', 'TV', 'Television',
                'Air conditioning', 'Heating', 'Vacuum', 'Iron', 'Fan'
            ];

            const testAppliances = [
                { name: "Dishwasher", location: "" },
                { name: "dishwasher", location: "" },
                { name: "Built-in dishwasher", location: "" },
                { name: "Microwave", location: "" },
                { name: "TV", location: "" },
                { name: "Hair dryer", location: "" }
            ];

            testAppliances.forEach(appliance => {
                console.log(`\n🔍 Testing: "${appliance.name}"`);
                
                const itemLower = appliance.name.toLowerCase();
                let isKitchenAppliance = false;

                // Check if it's explicitly a non-kitchen appliance first
                let isNonKitchenAppliance = false;
                for (const keyword of nonKitchenAppliances) {
                    const keywordLower = keyword.toLowerCase();
                    if (itemLower.includes(keywordLower)) {
                        isNonKitchenAppliance = true;
                        break;
                    }
                }

                // Check if it's a kitchen appliance (only if not non-kitchen)
                if (!isNonKitchenAppliance) {
                    for (const keyword of kitchenAppliances) {
                        const keywordLower = keyword.toLowerCase();
                        if (itemLower.includes(keywordLower) ||
                            itemLower.split(' ').some(word => keywordLower.includes(word)) ||
                            keywordLower.split(' ').some(word => itemLower.includes(word))) {
                            isKitchenAppliance = true;
                            break;
                        }
                    }
                }

                // Normalize location for kitchen appliances (but not non-kitchen appliances)
                if (isKitchenAppliance && !isNonKitchenAppliance) {
                    const currentLocation = (appliance.location || '').toLowerCase().trim();
                    const kitchenVariants = ['unit', 'in unit', 'kitchen', ''];

                    if (kitchenVariants.includes(currentLocation)) {
                        appliance.location = 'Kitchen';
                        console.log(`   ✅ Normalized "${appliance.name}" location to Kitchen (was: "${currentLocation}")`);
                    }
                } else {
                    console.log(`   ❌ "${appliance.name}" not identified as kitchen appliance`);
                    console.log(`      isKitchenAppliance: ${isKitchenAppliance}`);
                    console.log(`      isNonKitchenAppliance: ${isNonKitchenAppliance}`);
                }
            });
        }

        // Run test when page loads
        window.onload = function() {
            testApplianceLogic();
        };
    </script>
</head>
<body>
    <h1>Debug Appliance Frontend Logic</h1>
    <p>Check the browser console for test results.</p>
</body>
</html>
