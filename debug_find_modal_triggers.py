#!/usr/bin/env python3
"""
Debug script to find all possible modal triggers on the Airbnb listing page.
"""

import sys
import os
import time

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper
from selenium.webdriver.common.by import By

def debug_find_modal_triggers():
    """Find all possible modal triggers."""
    
    test_url = "https://www.airbnb.com/rooms/973815691982105805"
    
    print(f"🔍 DEBUGGING MODAL TRIGGERS")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=False)  # Non-headless for debugging
    
    try:
        print("📄 Step 1: Loading page...")
        scraper.driver.get(test_url)
        time.sleep(5)  # Wait for page to load
        
        # Expand "Things to know" section first
        print("\n🔍 Step 2: Expanding 'Things to know' section...")
        try:
            expandable_elements = scraper.driver.find_elements(By.XPATH, "//*[contains(text(), 'Things to know')]")
            for element in expandable_elements:
                try:
                    scraper.driver.execute_script("arguments[0].click();", element)
                    print(f"  ✅ Clicked: {element.text[:50]}...")
                    time.sleep(2)
                except:
                    pass
        except:
            pass
        
        print("\n🔍 Step 3: Looking for ALL clickable elements with rule-related text...")
        
        # Search for any element containing rule-related keywords
        rule_keywords = [
            'house rules', 'rules', 'policies', 'things to know',
            'checking in', 'during your stay', 'before you leave',
            'check-in', 'checkout', 'guests maximum', 'no pets',
            'quiet hours', 'no smoking', 'no parties'
        ]
        
        all_clickable_elements = []
        
        for keyword in rule_keywords:
            print(f"\n  🔍 Searching for: '{keyword}'")
            
            # Find all elements containing this keyword
            xpath_patterns = [
                f"//*[contains(text(), '{keyword}')]",
                f"//*[contains(@aria-label, '{keyword}')]",
                f"//*[contains(@title, '{keyword}')]"
            ]
            
            for xpath in xpath_patterns:
                try:
                    elements = scraper.driver.find_elements(By.XPATH, xpath)
                    print(f"    Found {len(elements)} elements with xpath: {xpath}")
                    
                    for element in elements:
                        try:
                            # Check if element or its ancestors are clickable
                            clickable_ancestor = element
                            for _ in range(5):  # Check up to 5 levels up
                                if (clickable_ancestor.tag_name in ['button', 'a'] or 
                                    clickable_ancestor.get_attribute('role') == 'button' or
                                    clickable_ancestor.get_attribute('onclick') or
                                    'button' in clickable_ancestor.get_attribute('class') or ''):
                                    
                                    element_info = {
                                        'element': clickable_ancestor,
                                        'text': clickable_ancestor.text[:100] if clickable_ancestor.text else 'No text',
                                        'tag': clickable_ancestor.tag_name,
                                        'class': clickable_ancestor.get_attribute('class') or 'No class',
                                        'role': clickable_ancestor.get_attribute('role') or 'No role',
                                        'aria_label': clickable_ancestor.get_attribute('aria-label') or 'No aria-label',
                                        'keyword': keyword
                                    }
                                    
                                    # Avoid duplicates
                                    if not any(existing['element'] == clickable_ancestor for existing in all_clickable_elements):
                                        all_clickable_elements.append(element_info)
                                        print(f"      ✅ Found clickable: {element_info['text'][:50]}... (tag: {element_info['tag']})")
                                    break
                                
                                # Move to parent
                                try:
                                    clickable_ancestor = clickable_ancestor.find_element(By.XPATH, '..')
                                except:
                                    break
                                    
                        except Exception as e:
                            pass
                            
                except Exception as e:
                    print(f"    ❌ Error with xpath {xpath}: {e}")
        
        print(f"\n📊 SUMMARY: Found {len(all_clickable_elements)} unique clickable elements")
        
        if all_clickable_elements:
            print(f"\n🎯 TESTING CLICKABLE ELEMENTS:")
            
            for i, element_info in enumerate(all_clickable_elements):
                print(f"\n  {i+1}. Testing element:")
                print(f"     Text: {element_info['text']}")
                print(f"     Tag: {element_info['tag']}")
                print(f"     Class: {element_info['class']}")
                print(f"     Role: {element_info['role']}")
                print(f"     Keyword: {element_info['keyword']}")
                
                try:
                    # Try clicking the element
                    element = element_info['element']
                    if element.is_enabled() and element.is_displayed():
                        print(f"     🔄 Clicking element...")
                        element.click()
                        time.sleep(3)  # Wait for potential modal
                        
                        # Check if modal opened by looking for modal content
                        modal_indicators = [
                            "Checking in and out",
                            "During your stay", 
                            "Before you leave",
                            "5 guests maximum",
                            "No pets",
                            "Quiet hours",
                            "No smoking"
                        ]
                        
                        modal_found = False
                        for indicator in modal_indicators:
                            modal_elements = scraper.driver.find_elements(By.XPATH, f"//*[contains(text(), '{indicator}')]")
                            if modal_elements:
                                print(f"     🎉 MODAL OPENED! Found indicator: '{indicator}'")
                                modal_found = True
                                break
                        
                        if modal_found:
                            print(f"     🎉 SUCCESS! This element opens the house rules modal!")
                            
                            # Save page source for analysis
                            with open('modal_opened_page_source.html', 'w', encoding='utf-8') as f:
                                f.write(scraper.driver.page_source)
                            print(f"     💾 Saved modal page source to modal_opened_page_source.html")
                            
                            # Keep browser open for inspection
                            print(f"\n🔍 Modal opened! Browser kept open for inspection...")
                            print(f"Press Enter to continue...")
                            input()
                            return True
                        else:
                            print(f"     ❌ No modal detected after clicking")
                    else:
                        print(f"     ❌ Element not clickable (enabled: {element.is_enabled()}, displayed: {element.is_displayed()})")
                        
                except Exception as e:
                    print(f"     ❌ Error clicking element: {e}")
        
        else:
            print(f"\n❌ No clickable elements found with rule-related keywords")
        
        print(f"\n🔍 Browser kept open for manual inspection...")
        print(f"Press Enter to close...")
        input()
        
        return False
        
    except Exception as e:
        print(f"❌ Error during modal trigger debugging: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    debug_find_modal_triggers()
