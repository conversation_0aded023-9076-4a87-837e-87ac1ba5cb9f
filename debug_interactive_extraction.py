#!/usr/bin/env python3
"""
Debug script with enhanced Selenium interaction to find house rules.
"""

import sys
import os
import time
import re

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

def debug_interactive_extraction():
    """Debug with enhanced Selenium interaction."""
    
    test_url = "https://www.airbnb.com/rooms/1126993904928991778"
    
    print(f"🔍 DEBUGGING WITH ENHANCED SELENIUM INTERACTION")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=False)  # Non-headless for debugging
    
    try:
        print("📄 Step 1: Loading page with extended wait...")
        scraper.driver.get(test_url)
        
        # Wait for page to fully load
        wait = WebDriverWait(scraper.driver, 15)
        
        # Wait for main content to load
        try:
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "main")))
            print("✅ Main content loaded")
        except TimeoutException:
            print("⚠️  Main content timeout, continuing...")
        
        # Additional wait for dynamic content
        time.sleep(5)
        
        print("\n🔍 Step 2: Looking for expandable sections...")
        
        # Look for buttons or elements that might expand house rules
        expandable_selectors = [
            'button[data-testid*="house-rules"]',
            'button[data-testid*="rules"]',
            'button[data-testid*="policies"]',
            'button[data-testid*="things-to-know"]',
            'button:contains("House rules")',
            'button:contains("Things to know")',
            'button:contains("Show more")',
            '[data-section-id*="HOUSE_RULES"]',
            '[data-section-id*="POLICIES"]'
        ]
        
        for selector in expandable_selectors:
            try:
                elements = scraper.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ Found expandable element: {selector} ({len(elements)} elements)")
                    for element in elements:
                        try:
                            # Try to click to expand
                            scraper.driver.execute_script("arguments[0].click();", element)
                            print(f"    📝 Clicked element: {element.text[:50]}...")
                            time.sleep(2)  # Wait for expansion
                        except Exception as e:
                            print(f"    ❌ Could not click element: {e}")
                else:
                    print(f"  ❌ No elements found for: {selector}")
            except Exception as e:
                print(f"  ❌ Error with selector {selector}: {e}")
        
        print("\n🔍 Step 3: Searching page source after interactions...")
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(scraper.driver.page_source, 'html.parser')
        all_text = soup.get_text().lower()
        
        # Search for the specific rules you mentioned
        expected_rules = [
            'check-in after 3:00 pm',
            'checkout before 11:00 am', 
            'self check-in with smart lock',
            '5 guests maximum',
            'no pets',
            'quiet hours',
            '10:00 pm - 7:00 am',
            'no parties or events',
            'no commercial photography',
            'no smoking',
            'throw trash away',
            'turn things off',
            'return keys',
            'lock up'
        ]
        
        print(f"🔍 SEARCHING FOR EXPECTED RULES:")
        found_rules = []
        missing_rules = []
        
        for rule in expected_rules:
            # Create flexible pattern
            pattern = rule.replace(' ', r'\s+').replace(':', r'\s*:?\s*')
            if re.search(pattern, all_text, re.IGNORECASE):
                found_rules.append(rule)
                print(f"  ✅ FOUND: {rule}")
            else:
                missing_rules.append(rule)
                print(f"  ❌ MISSING: {rule}")
        
        print(f"\n📊 SUMMARY:")
        print(f"  - Found rules: {len(found_rules)}/{len(expected_rules)}")
        print(f"  - Missing rules: {len(missing_rules)}")
        
        if found_rules:
            print(f"\n✅ FOUND RULES:")
            for rule in found_rules:
                print(f"    - {rule}")
        
        if missing_rules:
            print(f"\n❌ MISSING RULES:")
            for rule in missing_rules:
                print(f"    - {rule}")
        
        # Look for section headings
        print(f"\n🔍 Step 4: Looking for section headings...")
        section_headings = [
            'checking in and out',
            'during your stay', 
            'before you leave',
            'house rules',
            'things to know'
        ]
        
        for heading in section_headings:
            pattern = heading.replace(' ', r'\s+')
            if re.search(pattern, all_text, re.IGNORECASE):
                print(f"  ✅ FOUND HEADING: {heading}")
                
                # Try to find content after this heading
                heading_pattern = rf'{pattern}(.*?)(?=\n\n|\n[A-Z]|$)'
                matches = re.findall(heading_pattern, all_text, re.IGNORECASE | re.DOTALL)
                if matches:
                    content = matches[0][:200]  # First 200 chars
                    print(f"    📝 Content after heading: {content}...")
            else:
                print(f"  ❌ MISSING HEADING: {heading}")
        
        # Save page source for manual inspection
        print(f"\n💾 Step 5: Saving page source for manual inspection...")
        with open('debug_page_source.html', 'w', encoding='utf-8') as f:
            f.write(scraper.driver.page_source)
        print(f"  ✅ Page source saved to debug_page_source.html")
        
        # Keep browser open for manual inspection
        print(f"\n🔍 Browser kept open for manual inspection...")
        print(f"Press Enter to continue and close browser...")
        input()
        
        return len(found_rules), len(missing_rules)
        
    except Exception as e:
        print(f"❌ Error during interactive debugging: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    debug_interactive_extraction()
