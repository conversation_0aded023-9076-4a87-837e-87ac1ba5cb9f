#!/usr/bin/env python3
"""
Debug script to see what content is actually on the Airbnb listing.
"""

import sys
import os
import re

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def debug_listing_content():
    """Debug what content is actually on the listing."""
    
    test_url = "https://www.airbnb.com/rooms/1126993904928991778"
    
    print(f"🔍 DEBUGGING LISTING CONTENT")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        # Get the page content
        print("📄 Step 1: Loading page content...")
        scraper.driver.get(test_url)
        scraper.driver.implicitly_wait(5)
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(scraper.driver.page_source, 'html.parser')
        
        # Search for pet-related content
        print("\n🐕 SEARCHING FOR PET-RELATED CONTENT:")
        all_text = soup.get_text().lower()
        
        pet_patterns = [
            r'pets?\s+(allowed|permitted|welcome|ok)',
            r'no\s+pets?',
            r'pet\s+friendly',
            r'pet\s+policy',
            r'animals?\s+(allowed|permitted|welcome)',
            r'dogs?\s+(allowed|permitted|welcome)',
            r'cats?\s+(allowed|permitted|welcome)'
        ]
        
        pet_matches = []
        for pattern in pet_patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                pet_matches.extend(matches)
        
        if pet_matches:
            print(f"  ✅ Found pet-related content: {pet_matches}")
        else:
            print(f"  ❌ No pet-related content found")
            
        # Search for specific pet text
        pet_keywords = ['pet', 'dog', 'cat', 'animal']
        for keyword in pet_keywords:
            if keyword in all_text:
                # Find context around the keyword
                context_pattern = rf'.{{0,50}}{keyword}.{{0,50}}'
                context_matches = re.findall(context_pattern, all_text, re.IGNORECASE)
                if context_matches:
                    print(f"  📝 Context for '{keyword}': {context_matches[:3]}")  # Show first 3 matches
        
        # Search for guest count content
        print("\n👥 SEARCHING FOR GUEST COUNT CONTENT:")
        guest_patterns = [
            r'\d+\s+guests?\s+(maximum|max|allowed|limit)',
            r'maximum\s+\d+\s+guests?',
            r'accommodates\s+\d+',
            r'sleeps\s+\d+',
            r'up\s+to\s+\d+\s+guests?'
        ]
        
        guest_matches = []
        for pattern in guest_patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                guest_matches.extend(matches)
        
        if guest_matches:
            print(f"  ✅ Found guest count content: {guest_matches}")
        else:
            print(f"  ❌ No guest count content found")
            
        # Search for numbers that might be guest counts
        number_patterns = [r'\b[1-9]\s+guests?\b', r'\baccommodates\s+[1-9]\b', r'\bsleeps\s+[1-9]\b']
        for pattern in number_patterns:
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            if matches:
                print(f"  📝 Number patterns: {matches}")
        
        # Search for house rules sections
        print("\n📜 SEARCHING FOR HOUSE RULES SECTIONS:")
        
        # Find headings
        headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        rule_headings = []
        
        for heading in headings:
            heading_text = heading.get_text(strip=True).lower()
            rule_keywords = [
                'house rules', 'rules', 'policies', 'guidelines',
                'during your stay', 'before you leave', 'checkout instructions',
                'things to know', 'important information'
            ]
            
            if any(keyword in heading_text for keyword in rule_keywords):
                rule_headings.append(heading_text)
                print(f"  ✅ Found heading: '{heading_text}'")
                
                # Get content after this heading
                next_content = []
                current = heading.next_sibling
                for _ in range(10):  # Look at next 10 siblings
                    if current:
                        if hasattr(current, 'get_text'):
                            text = current.get_text(strip=True)
                            if text and len(text) > 5:
                                next_content.append(text)
                        current = current.next_sibling
                    else:
                        break
                
                if next_content:
                    print(f"    📝 Content after heading:")
                    for content in next_content[:5]:  # Show first 5 items
                        print(f"      - {content[:100]}...")
        
        if not rule_headings:
            print(f"  ❌ No house rules headings found")
        
        # Search for specific rule text patterns in the entire page
        print("\n🔍 SEARCHING FOR SPECIFIC RULE PATTERNS:")
        
        rule_searches = [
            ('pets allowed', r'pets?\s+(allowed|permitted|welcome)'),
            ('no pets', r'no\s+pets?'),
            ('guest maximum', r'\d+\s+guests?\s+(maximum|max)'),
            ('quiet hours', r'quiet\s+hours?'),
            ('no smoking', r'no\s+smoking'),
            ('no parties', r'no\s+parties?'),
            ('no commercial photography', r'no\s+commercial\s+photography'),
            ('throw trash away', r'throw\s+trash\s+away'),
            ('turn things off', r'turn\s+things\s+off'),
            ('return keys', r'return\s+keys'),
            ('lock up', r'lock\s+up'),
            ('self check-in', r'self\s+check.?in'),
            ('smart lock', r'smart\s+lock'),
            ('during your stay', r'during\s+your\s+stay'),
            ('before you leave', r'before\s+you\s+leave'),
            ('checking in and out', r'checking\s+in\s+and\s+out')
        ]
        
        for search_name, pattern in rule_searches:
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            if matches:
                print(f"  ✅ {search_name}: {matches}")
            else:
                print(f"  ❌ {search_name}: Not found")
        
        # Look for any text containing "4" or "5" near "guest"
        print("\n🔢 SEARCHING FOR GUEST NUMBERS:")
        guest_number_pattern = r'.{0,20}[45].{0,20}guests?.{0,20}'
        matches = re.findall(guest_number_pattern, all_text, re.IGNORECASE)
        if matches:
            print(f"  📝 Text with 4 or 5 near 'guest': {matches[:5]}")
        else:
            print(f"  ❌ No text with 4 or 5 near 'guest' found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    debug_listing_content()
