#!/usr/bin/env python3
"""
Run Telnyx Bidirectional Streaming Server

This script runs the Telnyx Bidirectional Streaming server with Gemini Live API.
"""

import asyncio
import logging
import argparse
import sys
import os

# Add the current directory to the path so we can import the websocket package
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main module
from websocket.telnyx_bidirectional_streaming import start_servers

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run Telnyx Bidirectional Streaming Server")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    return parser.parse_args()

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()
    
    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("telnyx_bidirectional.log")
        ]
    )
    
    # Run the server
    try:
        asyncio.run(start_servers())
    except KeyboardInterrupt:
        logging.info("Server stopped by user")
    except Exception as e:
        logging.error(f"Error running server: {e}", exc_info=True)
