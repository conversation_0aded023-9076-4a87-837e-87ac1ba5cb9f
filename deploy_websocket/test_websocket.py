#!/usr/bin/env python3
"""
Test WebSocket Connection

This script tests the WebSocket connection to the Telnyx Bidirectional Streaming server.
"""

import asyncio
import websockets
import json
import argparse
import base64
import sys

async def test_websocket(url):
    """Test the WebSocket connection."""
    print(f"Connecting to {url}...")

    try:
        async with websockets.connect(url) as websocket:
            print("Connected to WebSocket server.")

            # Wait for the connected event
            response = await websocket.recv()
            data = json.loads(response)
            print(f"Received: {data}")

            if data.get('event') == 'connected':
                print("Successfully received connected event.")

                # Send a test start event
                start_event = {
                    "event": "start",
                    "stream_id": "test-stream-123",
                    "start": {
                        "call_control_id": "test-call-123",
                        "from": "+15551234567",
                        "to": "+15557654321"
                    }
                }

                print(f"Sending start event: {start_event}")
                await websocket.send(json.dumps(start_event))

                # Wait for a response
                print("Waiting for response...")
                response = await websocket.recv()
                data = json.loads(response)
                print(f"Received: {data}")

                # Send a test media event with some audio data
                test_audio = b"This is a test audio message."
                encoded_audio = base64.b64encode(test_audio).decode('utf-8')

                media_event = {
                    "event": "media",
                    "stream_id": "test-stream-123",
                    "media": {
                        "payload": encoded_audio
                    }
                }

                print(f"Sending media event with test audio...")
                await websocket.send(json.dumps(media_event))

                # Wait for a response (may be audio or text)
                print("Waiting for response...")
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(response)
                    print(f"Received: {data}")

                    if 'media' in data and 'payload' in data['media']:
                        # Decode the audio payload
                        audio_payload = data['media']['payload']
                        decoded_audio = base64.b64decode(audio_payload)
                        print(f"Received {len(decoded_audio)} bytes of audio data.")
                except asyncio.TimeoutError:
                    print("No response received within timeout period.")

                # Send a stop event
                stop_event = {
                    "event": "stop",
                    "stream_id": "test-stream-123"
                }

                print(f"Sending stop event: {stop_event}")
                await websocket.send(json.dumps(stop_event))

                print("Test completed successfully.")
            else:
                print("Did not receive expected connected event.")

    except websockets.exceptions.ConnectionClosed as e:
        print(f"WebSocket connection closed: {e}")
    except Exception as e:
        print(f"Error: {e}")
        return False

    return True

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test WebSocket Connection")
    parser.add_argument("--url", default="wss://voice.guestrix.ai/ws/", help="WebSocket URL to connect to")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()

    # Run the test
    result = asyncio.run(test_websocket(args.url))

    # Exit with appropriate status code
    sys.exit(0 if result else 1)
