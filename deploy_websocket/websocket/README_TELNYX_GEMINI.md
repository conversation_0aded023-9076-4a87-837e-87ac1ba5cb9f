# Telnyx Bidirectional Streaming with Gemini Live API

This project implements bidirectional audio streaming between Telnyx phone calls and Google's Gemini Live API. It allows callers to have real-time voice conversations with Gemini AI through a phone call.

## Overview

The system consists of several components:

1. **WebSocket Server**: Handles WebSocket connections from Telnyx for bidirectional audio streaming
2. **HTTP Webhook Handler**: Processes Telnyx webhooks for call events
3. **Gemini Live Client**: Connects to Gemini Live API for bidirectional audio streaming
4. **Audio Processor**: Handles audio format conversion between services
5. **Call Manager**: Tracks active calls and their states

## Architecture

```
┌─────────────┐         ┌─────────────┐         ┌─────────────┐
│             │         │             │         │             │
│   Caller    │◄───────►│   Telnyx    │◄───────►│  WebSocket  │
│             │         │             │         │   Server    │
└─────────────┘         └─────────────┘         └──────┬──────┘
                                                       │
                                                       │
                                                       ▼
                                               ┌───────────────┐
                                               │               │
                                               │ Audio Process │
                                               │               │
                                               └───────┬───────┘
                                                       │
                                                       │
                                                       ▼
┌─────────────┐         ┌─────────────┐         ┌─────────────┐
│             │         │             │         │             │
│   Gemini    │◄───────►│  Gemini API │◄───────►│ Gemini Live │
│   Model     │         │             │         │   Client    │
└─────────────┘         └─────────────┘         └─────────────┘
```

## Files

- `websocket/telnyx_bidirectional_streaming.py`: Main application entry point
- `websocket/gemini_live_client.py`: Gemini Live API client
- `websocket/audio_processor.py`: Audio processing utilities
- `websocket/call_manager.py`: Call state management
- `websocket/utils.py`: Helper functions
- `websocket/__init__.py`: Package initialization
- `run_telnyx_websocket.py`: Entry point script to run the server

## Requirements

- Python 3.8+
- aiohttp
- websockets
- google-generativeai
- python-dotenv

## Environment Variables

Create a `.env` file with the following variables:

```
# Telnyx settings
TELNYX_API_KEY=your_telnyx_api_key
TELNYX_WEBHOOK_URL=https://your-domain.com/telnyx/webhook
WEBSOCKET_URL=wss://your-domain.com:8083

# Google settings
GEMINI_API_KEY=your_gemini_api_key
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set up environment variables in `.env`
4. Run the server:
   ```
   python run_telnyx_websocket.py
   ```

You can enable debug logging with:
```
python run_telnyx_websocket.py --debug
```

## Deployment

### EC2 Deployment

1. Launch an EC2 instance (t2.micro or larger)
2. Install dependencies:
   ```
   sudo apt update
   sudo apt install -y python3-pip python3-venv
   ```
3. Clone the repository and set up a virtual environment:
   ```
   git clone https://github.com/yourusername/telnyx-gemini.git
   cd telnyx-gemini
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```
4. Create a `.env` file with your API keys
5. Set up a systemd service to run the application:
   ```
   sudo nano /etc/systemd/system/telnyx-gemini.service
   ```

   Add the following content:
   ```
   [Unit]
   Description=Telnyx Gemini Bidirectional Streaming
   After=network.target

   [Service]
   User=ubuntu
   WorkingDirectory=/home/<USER>/telnyx-gemini
   ExecStart=/home/<USER>/telnyx-gemini/venv/bin/python run_telnyx_websocket.py
   Restart=always
   RestartSec=10
   Environment=PATH=/home/<USER>/telnyx-gemini/venv/bin
   Environment=PYTHONUNBUFFERED=1

   [Install]
   WantedBy=multi-user.target
   ```
6. Enable and start the service:
   ```
   sudo systemctl enable telnyx-gemini
   sudo systemctl start telnyx-gemini
   ```

### Setting Up Telnyx

1. Create a Telnyx account and get an API key
2. Set up a SIP trunk or phone number
3. Configure the webhook URL to point to your server:
   ```
   https://your-domain.com/telnyx/webhook
   ```
4. Configure the WebSocket URL for bidirectional streaming:
   ```
   wss://your-domain.com:8083
   ```

## Usage

1. Call your Telnyx phone number
2. The system will answer the call and connect you to Gemini Live API
3. Speak naturally and Gemini will respond in real-time

## Troubleshooting

### Common Issues

1. **WebSocket connection fails**:
   - Check that your server is accessible from the internet
   - Verify that port 8083 is open in your firewall
   - Check the WebSocket URL in your Telnyx configuration

2. **Audio quality issues**:
   - Adjust the sample rate and codec settings
   - Check network bandwidth and latency

3. **Gemini API errors**:
   - Verify your API key is correct
   - Check that you have access to the Gemini Live API
   - Ensure you're using a supported model

### Logs

Check the logs for detailed error messages:
```
tail -f telnyx_bidirectional.log
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Telnyx API Documentation](https://developers.telnyx.com/docs/voice/programmable-voice/media-streaming)
- [Google Gemini Live API Documentation](https://ai.google.dev/docs/gemini_api_overview)
- [Telnyx WebSocket Demo](https://github.com/team-telnyx/demo-node-telnyx/tree/master/websocket-demos/websoket-openai-demo)
