#!/usr/bin/env python3
"""
Telnyx Bidirectional Streaming with Gemini Live API

This module implements bidirectional audio streaming between Telnyx phone calls
and Google's Gemini Live API. It handles WebSocket connections from Telnyx,
processes the audio, and streams it to Gemini Live API, then returns Gemini's
responses back to the caller.

Based on the Telnyx WebSocket Media Streaming API:
https://developers.telnyx.com/docs/voice/programmable-voice/media-streaming
"""

import os
import json
import base64
import logging
import asyncio
import argparse
import aiohttp
import websockets
from aiohttp import web
from dotenv import load_dotenv

# Import our modules
from .gemini_live_client import GeminiLiveClient
from .audio_processor import resample_audio, encode_audio, decode_audio
from .call_manager import CallManager
from .utils import setup_logging, load_config, parse_arguments, mask_api_key

# Load environment variables
load_dotenv()

# Configure logging
logger = setup_logging()

# Get API keys from environment variables
TELNYX_API_KEY = os.getenv("TELNYX_API_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

# Log the API keys (masked for security)
logger.info(f"Telnyx API key loaded: {mask_api_key(TELNYX_API_KEY)}")
logger.info(f"Gemini API key loaded: {mask_api_key(GEMINI_API_KEY)}")

# Constants
TELNYX_SAMPLE_RATE = 16000  # Telnyx expects audio at 16kHz
GEMINI_SAMPLE_RATE = 24000  # Gemini provides audio at 24kHz
AUDIO_CHANNELS = 1  # Mono audio
GEMINI_LIVE_MODEL = "gemini-2.0-flash-live-001"  # Model ID for voice calls

# Initialize call manager
call_manager = CallManager()

# Import Google Generative AI
try:
    import google.generativeai as genai
    logger.info("Google Generative AI imported successfully")
except ImportError:
    logger.warning("Failed to import Google Generative AI. Make sure it's installed.")

# Import audio processing libraries
try:
    import audioop
    logger.info("Audio processing libraries imported successfully")
except ImportError:
    logger.warning("Failed to import audio processing libraries. Make sure they're installed.")

async def handle_websocket(websocket, path):
    """
    Handle WebSocket connections from Telnyx.

    This function processes incoming WebSocket connections from Telnyx,
    extracts audio data, sends it to Gemini Live API, and returns
    Gemini's responses back to the caller.
    """
    remote_address = websocket.remote_address if hasattr(websocket, 'remote_address') else 'unknown'
    logger.info(f"New Telnyx connection from {remote_address}. Path: {path}")

    # Extract caller number from path if available
    caller_number = None
    if '?' in path:
        query_string = path.split('?', 1)[1]
        params = query_string.split('&')
        for param in params:
            if '=' in param:
                key, value = param.split('=', 1)
                if key == 'caller_number':
                    caller_number = value
                    logger.info(f"Extracted caller number: {caller_number}")

    # Variables to track the WebSocket connection
    stream_id = None
    gemini_client = None

    try:
        # Send connected event
        connected_event = {
            "event": "connected",
            "version": "1.0.0"
        }
        await websocket.send(json.dumps(connected_event))
        logger.info(f"Sent connected event: {connected_event}")

        # Process incoming messages
        async for message in websocket:
            try:
                data = json.loads(message)
                event_type = data.get('event')

                # Log the event (excluding media payloads for brevity)
                if event_type != 'media':
                    logger.info(f"Received WebSocket message: {data}")
                else:
                    logger.debug(f"Received media event for stream ID: {data.get('stream_id')}")

                # Handle different event types
                if event_type == 'start':
                    # Extract stream ID and call details
                    stream_id = data.get('stream_id')
                    start_data = data.get('start', {})
                    call_control_id = start_data.get('call_control_id')
                    from_number = start_data.get('from')
                    to_number = start_data.get('to')

                    logger.info(f"Call started - Stream ID: {stream_id}, From: {from_number}, To: {to_number}")

                    # Initialize Gemini Live client for this call
                    gemini_client = GeminiLiveClient(
                        api_key=GEMINI_API_KEY,
                        model=GEMINI_LIVE_MODEL,
                        stream_id=stream_id
                    )

                    # Store call information
                    call_manager.add_call(
                        stream_id=stream_id,
                        call_control_id=call_control_id,
                        from_number=from_number,
                        to_number=to_number,
                        telnyx_ws=websocket,
                        gemini_client=gemini_client
                    )

                    # Connect to Gemini Live API
                    welcome_message = await gemini_client.connect()

                    # Send welcome message
                    welcome_media = {
                        "event": "media",
                        "media": {
                            "payload": base64.b64encode(welcome_message.encode()).decode()
                        }
                    }
                    await websocket.send(json.dumps(welcome_media))
                    logger.info(f"Sent welcome message to stream ID: {stream_id}")

                    # Start audio forwarding task
                    asyncio.create_task(forward_gemini_audio(stream_id, websocket))

                    # Start transcription handling task
                    asyncio.create_task(handle_transcriptions(stream_id, caller_number))

                elif event_type == 'media' and stream_id:
                    # Process media event
                    media = data.get('media', {})
                    payload = media.get('payload', '')

                    # Get call state
                    call_state = call_manager.get_call(stream_id)
                    if not call_state:
                        logger.warning(f"Received media for unknown stream ID: {stream_id}")
                        continue

                    # Update call state
                    call_manager.update_call_activity(stream_id)
                    call_manager.increment_media_packets(stream_id, "received")

                    # Decode the base64 audio data
                    audio_data = base64.b64decode(payload)

                    # Send the audio to Gemini Live API
                    gemini_client = call_state.get("gemini_client")
                    if gemini_client:
                        # Resample the audio to Gemini's sample rate
                        resampled_audio = resample_audio(audio_data, TELNYX_SAMPLE_RATE, GEMINI_SAMPLE_RATE)
                        await gemini_client.send_audio(resampled_audio)

                elif event_type == 'stop' and stream_id:
                    # Handle call end
                    logger.info(f"Call ended for stream ID: {stream_id}")

                    # Clean up resources
                    await call_manager.remove_call(stream_id)

                elif event_type == 'dtmf':
                    # Handle DTMF event
                    dtmf = data.get('dtmf', {})
                    digit = dtmf.get('digit')
                    logger.info(f"Received DTMF event for stream ID: {stream_id}, digit: {digit}")

                    # Send a response to the DTMF event
                    dtmf_response = {
                        "event": "media",
                        "media": {
                            "payload": base64.b64encode(f"You pressed {digit}".encode()).decode()
                        }
                    }
                    await websocket.send(json.dumps(dtmf_response))
                    logger.info(f"Sent DTMF response to stream ID: {stream_id}")

                else:
                    # Handle unknown event type
                    logger.warning(f"Received unknown event type: {event_type}")
                    logger.warning(f"Full message: {data}")

            except json.JSONDecodeError:
                logger.error(f"Error decoding JSON: {message}")
            except Exception as e:
                logger.error(f"Error handling message: {e}", exc_info=True)

    except websockets.exceptions.ConnectionClosed as e:
        logger.info(f"WebSocket connection closed: {e}")
    except Exception as e:
        logger.error(f"Error in WebSocket handler: {e}", exc_info=True)
    finally:
        # Clean up resources when the connection is closed
        if stream_id and call_manager.call_exists(stream_id):
            await call_manager.remove_call(stream_id)
        logger.info(f"WebSocket connection from {remote_address} closed")

async def handle_http_request(request):
    """Handle HTTP webhook requests from Telnyx."""
    try:
        # Parse the request body
        body = await request.json()

        # Extract event data
        event_data = body.get('data', {})
        event_type = event_data.get('event_type')
        payload = event_data.get('payload', {})
        call_control_id = payload.get('call_control_id')

        logger.info(f"Received webhook: {event_type} for call control ID: {call_control_id}")

        # Process different event types
        if event_type == 'call.initiated':
            # Handle new call
            logger.info(f"Call initiated: {call_control_id}")

            # Extract caller information
            caller_number = payload.get('from')
            to_number = payload.get('to')

            if caller_number:
                logger.info(f"Call from {caller_number} to {to_number}")

                # Answer the call
                result = await answer_call(call_control_id)
                logger.info(f"Answer call result: {result}")

        elif event_type == 'call.answered':
            # Handle call answered event
            logger.info(f"Call answered: {call_control_id}")

            # Start bidirectional streaming
            result = await start_bidirectional_streaming(call_control_id)
            logger.info(f"Start bidirectional streaming result: {result}")

        elif event_type == 'streaming.started':
            # Handle streaming started
            logger.info(f"Streaming started for call: {call_control_id}")

        elif event_type == 'streaming.stopped':
            # Handle streaming stopped
            logger.info(f"Streaming stopped for call: {call_control_id}")

        elif event_type == 'call.hangup':
            # Handle call hangup
            logger.info(f"Call hangup: {call_control_id}")

        # Return success response
        return web.json_response({"message": "Webhook received"})

    except Exception as e:
        logger.error(f"Error handling webhook: {e}", exc_info=True)
        return web.json_response({"error": str(e)}, status=500)

async def answer_call(call_control_id):
    """
    Answer a call using the Telnyx API.
    """
    try:
        # Create the answer command
        url = f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/answer"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {TELNYX_API_KEY}"
        }
        data = {
            "client_state": "aGF2ZSBhIG5pY2UgZGF5ID1d"
        }

        # Send the answer command
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                result = await response.json()
                logger.info(f"Answer call result: {result}")
                return result

    except Exception as e:
        logger.error(f"Error answering call: {e}", exc_info=True)
        return None

async def start_bidirectional_streaming(call_control_id):
    """
    Start bidirectional streaming for a call using the Telnyx API.

    This function configures the call to stream audio to our WebSocket server
    and enables bidirectional streaming so we can send audio back to the caller.
    """
    try:
        # Get the WebSocket URL
        websocket_url = os.getenv("WEBSOCKET_URL", "wss://voice.guestrix.ai/ws/")

        # Create the streaming command
        url = f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/streaming_start"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {TELNYX_API_KEY}"
        }
        data = {
            "stream_url": websocket_url,
            "stream_track": "both_tracks",
            "bidirectional": True,
            "stream_bidirectional_codec": "OPUS",
            "stream_bidirectional_sampling_rate": 16000
        }

        # Send the streaming command
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                result = await response.json()
                logger.info(f"Start bidirectional streaming result: {result}")
                return result

    except Exception as e:
        logger.error(f"Error starting bidirectional streaming: {e}", exc_info=True)
        return None

async def get_call_details(call_control_id):
    """
    Get call details from Telnyx API.
    """
    try:
        url = f"https://api.telnyx.com/v2/calls/{call_control_id}"
        headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {TELNYX_API_KEY}"
        }

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                result = await response.json()
                logger.info(f"Call details result: {result}")
                return result

    except Exception as e:
        logger.error(f"Error getting call details: {e}", exc_info=True)
        return None

async def send_text_to_telnyx(call_control_id, text):
    """
    Send text to Telnyx using the speak command.

    This function uses the Telnyx API to convert text to speech and play it to the caller.
    It uses the preferred voice settings for better audio quality.
    """
    try:
        # Create the speak command
        url = f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/speak"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {TELNYX_API_KEY}"
        }

        # Use Azure neural voice for better quality
        data = {
            "payload": text,
            "voice": "Azure.en-US-Ava:DragonHDLatestNeural",
            "language": "en-US",
            "payload_type": "text"
        }

        # Send the speak command
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                result = await response.json()
                logger.info(f"Speak result: {result}")
                return result

    except Exception as e:
        logger.error(f"Error sending text to Telnyx: {e}", exc_info=True)
        return None

async def handle_transcriptions(stream_id, caller_number):
    """
    Handle transcriptions from Gemini Live API and store them in DynamoDB.

    This function continuously checks for transcriptions from Gemini Live API
    and stores them in the conversation database.
    """
    logger.info(f"Starting transcription handler for {stream_id}")

    try:
        # Import DynamoDB client for storing conversations
        import sys
        import os

        # Add the parent directory to the path to import from concierge
        parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        concierge_dir = os.path.join(parent_dir, 'concierge')
        if concierge_dir not in sys.path:
            sys.path.append(concierge_dir)

        from utils.dynamodb_client import create_conversation_session, add_message_to_conversation

        # Create a conversation session for this voice call
        conversation_id = None

        while call_manager.call_exists(stream_id):
            call_state = call_manager.get_call(stream_id)
            if not call_state:
                logger.warning(f"Call state not found for {stream_id}")
                break

            gemini_client = call_state.get("gemini_client")
            if not gemini_client:
                logger.warning(f"Gemini client not found for {stream_id}")
                break

            # Get transcription from Gemini
            transcription = await gemini_client.get_transcription()
            if transcription:
                role = transcription.get('role')
                text = transcription.get('text')

                if role and text:
                    logger.info(f"Transcription for {stream_id} - {role}: {text}")

                    # Create conversation session if not exists
                    if not conversation_id:
                        try:
                            # For voice calls, we'll use a generic property and user ID
                            # In a real implementation, you'd look up the property based on the phone number
                            conversation_id = create_conversation_session(
                                property_id="voice-call-property",  # Placeholder
                                user_id=stream_id,  # Use stream_id as user_id for voice calls
                                guest_name="Voice Caller",
                                phone_number=caller_number,
                                channel='voice_call'
                            )
                            logger.info(f"Created conversation session {conversation_id} for voice call {stream_id}")
                        except Exception as e:
                            logger.error(f"Error creating conversation session: {e}")
                            continue

                    # Store the transcription
                    try:
                        add_message_to_conversation(conversation_id, role, text)
                        logger.info(f"Stored transcription in conversation {conversation_id}")
                    except Exception as e:
                        logger.error(f"Error storing transcription: {e}")

            # Small delay to prevent CPU overuse
            await asyncio.sleep(0.1)

    except Exception as e:
        logger.error(f"Error in transcription handler: {e}", exc_info=True)
    finally:
        logger.info(f"Stopped transcription handler for {stream_id}")

async def forward_gemini_audio(stream_id, websocket):
    """
    Forward audio from Gemini Live API to Telnyx.

    This function continuously checks for audio from Gemini Live API
    and forwards it to Telnyx via the WebSocket connection.
    """
    logger.info(f"Starting Gemini->Telnyx audio forwarder for {stream_id}")

    try:
        while call_manager.call_exists(stream_id):
            call_state = call_manager.get_call(stream_id)
            if not call_state:
                logger.warning(f"Call state not found for {stream_id}")
                break

            gemini_client = call_state.get("gemini_client")
            if not gemini_client:
                logger.warning(f"Gemini client not found for {stream_id}")
                break

            # Get audio from Gemini
            audio_chunk = await gemini_client.get_audio()
            if audio_chunk:
                # Resample audio to Telnyx's sample rate
                resampled_audio = resample_audio(audio_chunk, GEMINI_SAMPLE_RATE, TELNYX_SAMPLE_RATE)

                # Encode as base64
                encoded_audio = base64.b64encode(resampled_audio).decode('utf-8')

                # Send to Telnyx
                media_event = {
                    "event": "media",
                    "media": {
                        "payload": encoded_audio
                    }
                }

                try:
                    if not websocket.closed:
                        await websocket.send(json.dumps(media_event))
                        logger.debug(f"Sent {len(resampled_audio)} bytes of audio to Telnyx for {stream_id}")
                        call_manager.increment_media_packets(stream_id, "sent")
                except Exception as e:
                    logger.error(f"Error sending audio to Telnyx: {e}")
                    break

            # Small delay to prevent CPU overuse
            await asyncio.sleep(0.01)
    except Exception as e:
        logger.error(f"Error in audio forwarder: {e}", exc_info=True)
    finally:
        logger.info(f"Stopped audio forwarder for {stream_id}")

async def start_servers():
    """
    Start both HTTP and WebSocket servers.
    """
    # Start the call manager cleanup task
    call_manager.start_cleanup_task()

    # Create the HTTP app
    app = web.Application()
    app.router.add_post('/telnyx/', handle_http_request)

    # Start the HTTP server
    runner = web.AppRunner(app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 8082)
    await site.start()
    logger.info("HTTP server started on http://0.0.0.0:8082/telnyx/")

    # Start the WebSocket server
    await websockets.serve(handle_websocket, '0.0.0.0', 8083)
    logger.info("WebSocket server started on ws://0.0.0.0:8083")

    # Keep the servers running
    await asyncio.Future()

if __name__ == "__main__":
    # Parse command line arguments
    args = parse_arguments()

    # Set log level based on arguments
    if args.debug:
        logger.setLevel(logging.DEBUG)

    # Run the server
    asyncio.run(start_servers())
