services:
  redis:
    image: redis:alpine
    ports:
      - "6380:6379"
    restart: unless-stopped

  livekit:
    image: livekit/livekit-server
    ports:
      - "7880:7880"
      - "7881:7881/tcp"
      - "7882:7882/udp"
      - "50000-50100:50000-50100/udp"
    volumes:
      - ./config:/config
    command: --config /config/livekit.yaml
    restart: unless-stopped

  livekit-sip:
    image: livekit/sip
    ports:
      - "8080:8080/udp"
      - "8080:8080/tcp"
      - "10000-10100:10000-10100/udp"
    environment:
      - API_KEY=guestrix_key_local_testing_only_not_for_production
      - API_SECRET=guestrix_secret_local_testing_only_not_for_production_32chars
      - WS_URL=ws://livekit:7880
      - REDIS_ADDRESS=redis:6379
    depends_on:
      - redis
      - livekit
    restart: unless-stopped
