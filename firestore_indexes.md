# Required Firestore Indexes for Concierge Application

## Overview
The Concierge application requires specific compound indexes in Firestore to support cleanup operations and complex queries. This document provides step-by-step instructions for creating these indexes.

## Required Indexes

### 1. Magic Links Collection Index
**Purpose**: Support cleanup of expired magic links
**Collection**: `magic_links`
**Fields**: 
- `expires_at` (Ascending)
- `is_active` (Ascending)
**Query scope**: Collection

### 2. Users Collection Index  
**Purpose**: Support cleanup of expired temporary users
**Collection**: `users`
**Fields**:
- `isTemporary` (Ascending) 
- `expiresAt` (Ascending)
**Query scope**: Collection

## Setup Methods

### Method 1: Automatic Index Creation (Recommended)
The indexes will be automatically created when you first run the cleanup functions. Follow these steps:

1. **Trigger the cleanup functions** by starting your application:
   ```bash
   python concierge/app.py
   ```

2. **Wait for 24 hours** for the scheduled cleanup to run, OR manually trigger cleanup:
   ```bash
   curl -X POST "http://localhost:8085/api/system/cleanup"
   ```

3. **Check the logs** for index creation URLs. You'll see messages like:
   ```
   google.api_core.exceptions.FailedPrecondition: 
   The query requires an index. You can create it here: 
   https://console.firebase.google.com/project/YOUR_PROJECT/firestore/indexes?create_composite=...
   ```

4. **Click the provided URLs** to automatically create the indexes in Firebase Console.

### Method 2: Manual Creation via Firebase Console

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project** (clean-art-454915-d9)
3. **Navigate to Firestore Database > Indexes**
4. **Click "Create Index"**

**For Magic Links Index:**
- Collection ID: `magic_links`
- Add Field: `expires_at` → Ascending
- Add Field: `is_active` → Ascending  
- Query scope: Collection
- Click "Create"

**For Users Index:**
- Collection ID: `users`
- Add Field: `isTemporary` → Ascending
- Add Field: `expiresAt` → Ascending
- Query scope: Collection  
- Click "Create"

### Method 3: Using Firebase CLI

```bash
# Install Firebase CLI if not already installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Create firestore.indexes.json file
cat > firestore.indexes.json << 'EOF'
{
  "indexes": [
    {
      "collectionGroup": "magic_links",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "expires_at",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "is_active", 
          "order": "ASCENDING"
        }
      ]
    },
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION", 
      "fields": [
        {
          "fieldPath": "isTemporary",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "expiresAt",
          "order": "ASCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
}
EOF

# Deploy the indexes
firebase deploy --only firestore:indexes
```

## Verification

After creating the indexes, verify they're working:

1. **Check Firebase Console**: Go to Firestore > Indexes and confirm both indexes show as "Enabled"

2. **Test cleanup functions**:
   ```bash
   # In Python shell or script:
   from concierge.utils.firestore_client import expire_old_magic_links, cleanup_expired_temporary_users
   
   # These should run without errors:
   expired_links = expire_old_magic_links()
   expired_users = cleanup_expired_temporary_users()
   print(f"Expired {expired_links} links, {expired_users} users")
   ```

3. **Check application logs** for any remaining index-related errors.

## Performance Impact

- **Index build time**: 1-5 minutes for small datasets, longer for large datasets
- **Storage overhead**: Minimal (~5-10% increase) 
- **Query performance**: 10-100x faster for cleanup operations
- **Cost impact**: Negligible for typical usage patterns

## Troubleshooting

### Error: "Index creation failed"
- Check that you have Firestore Admin permissions
- Verify your Firebase project is correctly selected
- Try the automatic method first (recommended)

### Error: "Query still requires an index"
- Wait 2-5 minutes after index creation for propagation
- Verify field names exactly match (case-sensitive)
- Check that the order (ASCENDING) matches

### Error: "Missing permissions"
- Ensure you're logged in: `firebase login`
- Check IAM permissions in Google Cloud Console
- You need "Cloud Firestore Admin" role

## Related Code Locations

The queries requiring these indexes are located in:
- `concierge/utils/firestore_client.py:2020` - expire_old_magic_links()
- `concierge/utils/firestore_client.py:2053` - cleanup_expired_temporary_users()
- `concierge/api/routes.py:2155` - system cleanup endpoint
- `concierge/api/routes.py:2159` - system cleanup endpoint

## Notes

- Indexes are shared across all environments (dev/staging/prod) within the same Firebase project
- Creating indexes is a one-time setup per project
- Future schema changes may require additional indexes
- Monitor index usage in Firebase Console to optimize performance 