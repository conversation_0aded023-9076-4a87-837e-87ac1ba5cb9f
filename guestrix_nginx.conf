server {
    listen 80;
    server_name app.guestrix.ai;  # Replace with your actual domain

    # Logging configuration
    access_log /var/log/nginx/guestrix_access.log;
    error_log /var/log/nginx/guestrix_error.log;

    # Flask Web App - Main application
    location / {
        proxy_pass http://127.0.0.1:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket support for Flask-SocketIO
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 86400;
    }

    # Telnyx WebSocket Server
    location /telnyx/ {
        proxy_pass http://127.0.0.1:8080/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 86400;
    }

    # Static files (optional, if you want to serve static files directly)
    location /static/ {
        alias /app/flask/concierge/static/;
        expires 30d;
    }
}
