"""
Gunicorn configuration file.
Optimized for Google Cloud micro instances with threading compatibility.
"""

# The socket to bind
bind = "0.0.0.0:8080"

# Single worker for micro instance (limited CPU and memory)
workers = 1

# Use gevent worker class - compatible with threading mode and efficient
worker_class = "gevent"

# Reduce threads for limited resources
threads = 2

# The maximum number of simultaneous clients (reduced for micro instance)
worker_connections = 300

# The maximum number of requests a worker will process before restarting
max_requests = 500

# The maximum number of requests a worker will process before restarting (jitter)
max_requests_jitter = 50

# Increase timeout for micro instance (can be slower)
timeout = 180

# The timeout for worker processes to gracefully shutdown
graceful_timeout = 60

# The number of seconds to wait for requests on a Keep-Alive connection
keepalive = 5

# Memory optimization for micro instances
preload_app = True
max_worker_memory = 150  # MB - reduced for micro instance

# The path to the error log file
errorlog = "/app/dashboard/error.log"

# The path to the access log file
accesslog = "/app/dashboard/access.log"

# The log level
loglevel = "info"

# Additional settings for micro instances
worker_tmp_dir = "/dev/shm"  # Use tmpfs for better performance

# SocketIO compatibility settings
worker_class = "gevent"  # Explicit setting for clarity
