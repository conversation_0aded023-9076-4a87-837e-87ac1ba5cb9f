# LiveKit Integration with Twilio and Gemini Live

This project implements a local prototype for integrating Twilio SIP trunking with Google's Gemini Live API using LiveKit. It allows callers to have real-time voice conversations with Gemini AI through a phone call.

## Overview

The system consists of several components:

1. **LiveKit Server**: Manages the voice agent and telephony integration
2. **Gemini Live Agent**: Connects to Gemini Live API for bidirectional audio streaming
3. **Twilio SIP Trunk**: Handles phone call routing and audio streaming
4. **Flask Server**: Processes Twilio webhooks for call events

## Setup

### Prerequisites

- Python 3.8+
- Twilio account with SIP trunk
- Google Gemini API key
- LiveKit Cloud account (optional, for production)

### Installation

1. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   The `.env` file should contain:
   ```
   GEMINI_API_KEY=your_gemini_api_key
   TWILIO_ACCOUNT_SID=your_twilio_account_sid
   TWILIO_AUTH_TOKEN=your_twilio_auth_token
   TWILIO_PHONE_NUMBER=your_twilio_phone_number
   ```

### Twilio SIP Trunk Configuration

You can configure the Twilio SIP trunk using either the Twilio CLI or the Twilio Console UI.

#### Using Twilio CLI (Recommended)

1. Install the Twilio CLI:
   ```
   npm install -g twilio-cli
   ```

2. Log in to your Twilio account:
   ```
   twilio login
   ```

3. Edit the `configure_twilio_trunk.sh` script to set your LiveKit SIP URI.

4. Run the script to configure the trunk:
   ```
   chmod +x configure_twilio_trunk.sh
   ./configure_twilio_trunk.sh
   ```

5. Edit the `associate_phone_number.sh` script to set your Twilio phone number.

6. Run the script to associate the phone number with the trunk:
   ```
   chmod +x associate_phone_number.sh
   ./associate_phone_number.sh
   ```

#### Using Twilio Console UI

1. Create a SIP trunk in Twilio:
   - Go to Elastic SIP Trunking > Trunks
   - Create a new trunk with a domain name ending in `pstn.twilio.com`

2. Configure the trunk for inbound calls:
   - Select the trunk you created
   - Go to Origination > Add new Origination URI
   - Set the URI to `sip:<your LiveKit SIP URI>`
   - Set Priority and Weight to 1, and enable it

3. Associate a phone number with the trunk:
   - Go to Numbers
   - Select "Add a phone number"
   - Choose an existing phone number or buy a new one
   - Associate it with the trunk

## Usage

1. Start the server:
   ```
   python server.py
   ```

2. Call your Twilio phone number
3. The system will answer the call and connect you to Gemini Live API
4. Speak naturally and Gemini will respond in real-time

## Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Caller     │────▶│  Twilio     │────▶│  LiveKit    │
│  (Phone)    │◀────│  SIP Trunk  │◀────│  Server     │
└─────────────┘     └─────────────┘     └──────┬──────┘
                                               │
                                               │
                                        ┌──────▼──────┐
                                        │  Gemini     │
                                        │  Live API   │
                                        └─────────────┘
```

### LiveKit SIP Integration

LiveKit provides SIP support for telephony integration, allowing you to connect phone calls to your voice AI agents. Here's how it works:

1. **SIP Trunk**: A SIP trunk connects your phone system (Twilio) to LiveKit.

2. **Inbound Calls**: When someone calls your Twilio phone number, the call is routed through the SIP trunk to LiveKit.

3. **Voice Agent**: LiveKit connects the call to a voice agent powered by Gemini Live API.

4. **Bidirectional Audio**: Audio from the caller is sent to Gemini Live API, and responses are sent back to the caller.

#### LiveKit Deployment Options

You have several options for deploying LiveKit:

1. **LiveKit Cloud** (Managed Service):
   - Sign up at [cloud.livekit.io](https://cloud.livekit.io)
   - Create a SIP trunk in the dashboard
   - Use the provided SIP URI (e.g., `your-sip-id.sip.livekit.cloud`)
   - Configure your Twilio SIP trunk to point to this URI

2. **Self-hosted on AWS EC2** (alongside existing websocket server):
   - Use the provided scripts to install LiveKit Server on your EC2 instance:
     ```
     # Deploy the setup files to the server
     ./livekit/deploy_livekit.sh

     # SSH into the server and complete the installation
     ssh -i ./concierge/infra/guestrix-key-pair.pem <EMAIL>
     cd /home/<USER>/livekit && ./install_livekit.sh
     ```
   - The scripts will:
     - Install LiveKit Server in `/home/<USER>/livekit`
     - Configure Nginx to add LiveKit endpoints to `voice.guestrix.ai`
     - Set up a systemd service to run LiveKit as a background service
     - Use the existing SSL certificates for `voice.guestrix.ai`
   - Your LiveKit SIP URI will be `voice.guestrix.ai`
   - Configure your Twilio SIP trunk to use `sip:voice.guestrix.ai`

3. **Local Testing with Docker**:
   - Use the provided Docker setup:
     ```
     # Set up the Docker environment
     ./setup_livekit_sip_docker.sh

     # Start the Docker containers
     cd ./docker && docker-compose up -d

     # Test the SIP connection
     ../test_sip_connection.sh
     ```
   - For local testing, you'll need to expose your local server to the internet (e.g., using ngrok)
   - The Docker setup includes:
     - LiveKit Server
     - LiveKit SIP Server
     - Redis Server

For more details, see the [LiveKit SIP Documentation](https://docs.livekit.io/sip/).

## Troubleshooting

### Common Issues

1. **SIP trunk connection fails**:
   - Check that your LiveKit SIP URI is correctly configured in Twilio
   - Verify that your Twilio credentials are correct
   - Ensure the SIP ports (5060 UDP/TCP) are open and accessible

2. **Gemini Live API connection fails**:
   - Check that your Gemini API key is valid
   - Verify that you have the correct permissions for the Gemini Live API

3. **Audio quality issues**:
   - Check the audio format and sample rate settings
   - Adjust the buffer size for smoother audio

4. **Docker setup issues**:
   - If you encounter port conflicts, modify the port ranges in the configuration files
   - Check Docker logs with `docker-compose logs -f`

5. **EC2 deployment issues**:
   - If you encounter disk space issues, run `./clean_disk_space.sh`
   - Check the service status with `./check_livekit_sip_status.sh`

## Resources

- [LiveKit SIP Documentation](https://docs.livekit.io/sip/)
- [Twilio SIP Trunk Documentation](https://docs.livekit.io/sip/quickstarts/configuring-twilio-trunk/)
- [Gemini Live API Documentation](https://docs.livekit.io/agents/integrations/realtime/gemini/)
