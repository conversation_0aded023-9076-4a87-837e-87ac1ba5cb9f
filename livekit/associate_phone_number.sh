#!/bin/bash
# Script to associate a phone number with a Twilio SIP trunk

# Set variables
TRUNK_NAME="guestrix"
PHONE_NUMBER=""  # Set this to your Twilio phone number (e.g., +1234567890)

# Check if Twilio CLI is installed
if ! command -v twilio &> /dev/null; then
    echo "Twilio CLI is not installed. Please install it first."
    echo "Visit: https://www.twilio.com/docs/twilio-cli/getting-started/install"
    exit 1
fi

# Check if a Twilio profile is configured
if ! twilio profiles:list | grep -q "active"; then
    echo "No active Twilio profile found. Please configure one first."
    echo "Run: twilio login"
    exit 1
fi

# Check if phone number is provided
if [ -z "$PHONE_NUMBER" ]; then
    echo "Please edit this script to set your Twilio phone number."
    exit 1
fi

# Get the trunk SID
echo "Getting SID for trunk: $TRUNK_NAME"
TRUNK_SID=$(twilio api trunking v1 trunks list --friendly-name "$TRUNK_NAME" -o json | jq -r '.[0].sid')

if [ -z "$TRUNK_SID" ] || [ "$TRUNK_SID" == "null" ]; then
    echo "Trunk not found. Please run configure_twilio_trunk.sh first."
    exit 1
else
    echo "Found trunk with SID: $TRUNK_SID"
fi

# Get the phone number SID
echo "Looking up phone number: $PHONE_NUMBER"
PHONE_NUMBER_SID=$(twilio phone-numbers list --phone-number "$PHONE_NUMBER" -o json | jq -r '.[0].sid')

if [ -z "$PHONE_NUMBER_SID" ] || [ "$PHONE_NUMBER_SID" == "null" ]; then
    echo "Phone number not found. Please check the number and try again."
    exit 1
else
    echo "Found phone number with SID: $PHONE_NUMBER_SID"
fi

# Associate the phone number with the trunk
echo "Associating phone number with trunk..."
twilio api trunking v1 trunks phone-numbers create \
    --trunk-sid "$TRUNK_SID" \
    --phone-number-sid "$PHONE_NUMBER_SID"

echo "Phone number association complete!"
echo "Your Twilio phone number is now configured to use the SIP trunk."
