#!/bin/bash
# <PERSON>ript to check AWS EC2 instance status

# Set variables
INSTANCE_ID="i-0123456789abcdef0"  # Replace with your actual instance ID
INSTANCE_IP="************"
INSTANCE_DNS="ec2-3-130-141-92.us-east-2.compute.amazonaws.com"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    print_message "You can install it using: pip install awscli"
    exit 1
fi

# Check if AWS CLI is configured
if ! aws configure list &> /dev/null; then
    print_warning "AWS CLI is not configured. You may need to run 'aws configure' first."
    print_message "Continuing with basic checks..."
fi

# Try to ping the instance
print_message "Trying to ping the EC2 instance at $INSTANCE_IP..."
ping -c 3 $INSTANCE_IP

# Try to ping the instance DNS
print_message "Trying to ping the EC2 instance at $INSTANCE_DNS..."
ping -c 3 $INSTANCE_DNS

# Try to check the instance status using AWS CLI
if aws configure list &> /dev/null; then
    print_message "Checking EC2 instance status using AWS CLI..."
    aws ec2 describe-instance-status --instance-ids $INSTANCE_ID
    
    print_message "Checking EC2 instance details..."
    aws ec2 describe-instances --instance-ids $INSTANCE_ID
else
    print_warning "Skipping AWS CLI checks as it's not configured."
fi

print_message "Instance check completed."
