#!/bin/bash
# Script to check EC2 connection

# Set variables
EC2_HOST="<EMAIL>"
SSH_KEY="./concierge/infra/guestrix-key-pair.pem"
INFRA_KEY="./infra/guestrix-key-pair.pem"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Try the first SSH key
if [ -f "$SSH_KEY" ]; then
    print_message "Found SSH key at $SSH_KEY"
    chmod 600 "$SSH_KEY"
    
    print_message "Testing SSH connection with $SSH_KEY..."
    ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" 2>&1
    
    if [ $? -eq 0 ]; then
        print_message "SSH connection successful with $SSH_KEY."
        exit 0
    else
        print_warning "Failed to connect with $SSH_KEY."
    fi
else
    print_warning "SSH key not found at $SSH_KEY"
fi

# Try the second SSH key
if [ -f "$INFRA_KEY" ]; then
    print_message "Found SSH key at $INFRA_KEY"
    chmod 600 "$INFRA_KEY"
    
    print_message "Testing SSH connection with $INFRA_KEY..."
    ssh -i "$INFRA_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" 2>&1
    
    if [ $? -eq 0 ]; then
        print_message "SSH connection successful with $INFRA_KEY."
        exit 0
    else
        print_warning "Failed to connect with $INFRA_KEY."
    fi
else
    print_warning "SSH key not found at $INFRA_KEY"
fi

# Try to ping the host
print_message "Trying to ping the EC2 instance..."
ping -c 3 ec2-3-130-141-92.us-east-2.compute.amazonaws.com

print_error "Failed to connect to the EC2 instance. The instance might be down or unreachable."
exit 1
