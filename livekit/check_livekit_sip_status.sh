#!/bin/bash
# Script to check the status of LiveKit SIP Server on the remote server

# Set variables
EC2_HOST="<EMAIL>"
SSH_KEY="./concierge/infra/guestrix-key-pair.pem"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    print_error "SSH key not found: $SSH_KEY"
    print_message "Checking for alternative key locations..."

    # Try to find the key in the infra directory
    INFRA_KEY="./infra/guestrix-key-pair.pem"
    if [ -f "$INFRA_KEY" ]; then
        print_message "Found key at $INFRA_KEY"
        SSH_KEY="$INFRA_KEY"
    else
        print_error "No SSH key found. Please provide the correct path to the SSH key."
        exit 1
    fi
fi

# Make sure the SSH key has the right permissions
chmod 600 "$SSH_KEY"

# Test SSH connection
print_message "Testing SSH connection to $EC2_HOST..."
ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    print_error "Failed to connect to $EC2_HOST. Please check your SSH key and connection."
    exit 1
fi

print_message "SSH connection successful."

# Check LiveKit SIP service status
print_message "Checking LiveKit SIP service status..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo systemctl status livekit-sip"

# Check LiveKit service status
print_message "Checking LiveKit service status..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo systemctl status livekit"

# Check Redis service status
print_message "Checking Redis service status..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo systemctl status redis-server"

# Check if the LiveKit SIP binary exists
print_message "Checking if LiveKit SIP binary exists..."
ssh -i "$SSH_KEY" "$EC2_HOST" "ls -la /home/<USER>/livekit/livekit-sip"

# Check if the LiveKit SIP config exists
print_message "Checking if LiveKit SIP config exists..."
ssh -i "$SSH_KEY" "$EC2_HOST" "ls -la /home/<USER>/livekit/sip/config/config.yaml"

# Check if the ports are open
print_message "Checking if SIP ports are open..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo ufw status | grep 5060"

print_message "Status check completed."
