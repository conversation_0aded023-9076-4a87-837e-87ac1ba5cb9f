#!/bin/bash
# Script to clean up disk space on the remote server

# Set variables
EC2_HOST="<EMAIL>"
SSH_KEY="./concierge/infra/guestrix-key-pair.pem"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    print_error "SSH key not found: $SSH_KEY"
    print_message "Checking for alternative key locations..."

    # Try to find the key in the infra directory
    INFRA_KEY="./infra/guestrix-key-pair.pem"
    if [ -f "$INFRA_KEY" ]; then
        print_message "Found key at $INFRA_KEY"
        SSH_KEY="$INFRA_KEY"
    else
        print_error "No SSH key found. Please provide the correct path to the SSH key."
        exit 1
    fi
fi

# Make sure the SSH key has the right permissions
chmod 600 "$SSH_KEY"

# Test SSH connection
print_message "Testing SSH connection to $EC2_HOST..."
ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    print_error "Failed to connect to $EC2_HOST. Please check your SSH key and connection."
    exit 1
fi

print_message "SSH connection successful."

# Check disk space before cleanup
print_message "Checking disk space before cleanup..."
ssh -i "$SSH_KEY" "$EC2_HOST" "df -h /"

# Clean up log files
print_message "Cleaning up log files..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo find /var/log -type f -name '*.log' -exec truncate -s 0 {} \;"
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo find /var/log -type f -name '*.gz' -delete"
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo find /var/log -type f -name '*.1' -delete"
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo find /var/log -type f -name '*.old' -delete"

# Clean up apt cache
print_message "Cleaning up apt cache..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo apt-get clean"
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo apt-get autoremove -y"

# Clean up pip cache
print_message "Cleaning up pip cache..."
ssh -i "$SSH_KEY" "$EC2_HOST" "rm -rf ~/.cache/pip"

# Clean up telnyx log files
print_message "Cleaning up telnyx log files..."
ssh -i "$SSH_KEY" "$EC2_HOST" "truncate -s 0 /home/<USER>/telnyx_websocket/telnyx_bidirectional.log"

# Clean up temporary files
print_message "Cleaning up temporary files..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo rm -rf /tmp/*"

# Clean up old kernels
print_message "Cleaning up old kernels..."
ssh -i "$SSH_KEY" "$EC2_HOST" "sudo apt-get purge -y $(dpkg -l 'linux-image-*' | grep -v \$(uname -r) | grep -v 'linux-image-generic' | awk '{if(NR>1)print \$2}')"

# Check disk space after cleanup
print_message "Checking disk space after cleanup..."
ssh -i "$SSH_KEY" "$EC2_HOST" "df -h /"

print_message "Disk space cleanup completed."
