#!/usr/bin/env python3
"""
Complete example of using LiveKit Agents with Gemini Live API.

This script demonstrates a more complete example of using LiveKit Agents
with Gemini Live API, including audio file handling and conversation flow.
"""

import os
import asyncio
import logging
import argparse
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("livekit-complete-example")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="LiveKit Agents with Gemini Live API example")
    parser.add_argument("--audio-file", help="Path to an audio file to send to the agent")
    parser.add_argument("--text", help="Text to send to the agent")
    parser.add_argument("--output-dir", default="output", help="Directory to save output audio files")
    return parser.parse_args()

async def main():
    """Main function."""
    # Parse arguments
    args = parse_arguments()
    
    # Load environment variables
    load_dotenv()
    
    # Get API key from environment
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("GEMINI_API_KEY not found in environment variables")
        return
    
    # Create output directory if it doesn't exist
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Import LiveKit Agents
    try:
        from livekit.agents import AgentSession
        from livekit.plugins.google.beta.realtime import RealtimeModel
    except ImportError:
        logger.error("LiveKit Agents not found. Please install with: pip install livekit-agents livekit-plugins-google")
        return
    
    logger.info("Creating Gemini Live agent session")
    
    # Create the agent session
    session = AgentSession(
        llm=RealtimeModel(
            model="gemini-2.0-flash-exp",
            voice="Puck",
            temperature=0.8,
            instructions="""
            You are a helpful AI assistant speaking with a caller on the phone.
            Be concise, conversational, and helpful.
            Respond to the caller's questions and requests in a natural way.
            If you don't know the answer to a question, say so.
            """,
            api_key=api_key,
            modalities=["AUDIO", "TEXT"]  # Use both audio and text modalities
        ),
    )
    
    logger.info("Gemini Live agent session created successfully")
    
    try:
        # Start the conversation
        logger.info("Starting conversation")
        
        # Send initial message
        initial_message = "Hello, I'm calling to test the Gemini Live API integration with LiveKit."
        logger.info(f"Sending initial message: {initial_message}")
        await session.send_text(initial_message)
        
        # Wait for response
        logger.info("Waiting for response...")
        response_text = await session.receive_text()
        logger.info(f"Received text response: {response_text}")
        
        # Get audio response
        audio_data = await session.receive_audio()
        if audio_data:
            logger.info(f"Received {len(audio_data)} bytes of audio")
            
            # Save audio to file
            audio_file = os.path.join(args.output_dir, "response_1.wav")
            with open(audio_file, "wb") as f:
                f.write(audio_data)
            logger.info(f"Saved audio response to {audio_file}")
        
        # If audio file is provided, send it to the agent
        if args.audio_file and os.path.exists(args.audio_file):
            logger.info(f"Sending audio file: {args.audio_file}")
            
            # Read audio file
            with open(args.audio_file, "rb") as f:
                audio_data = f.read()
            
            # Send audio to agent
            await session.send_audio(audio_data)
            
            # Wait for response
            logger.info("Waiting for response to audio...")
            response_text = await session.receive_text()
            logger.info(f"Received text response: {response_text}")
            
            # Get audio response
            audio_data = await session.receive_audio()
            if audio_data:
                logger.info(f"Received {len(audio_data)} bytes of audio")
                
                # Save audio to file
                audio_file = os.path.join(args.output_dir, "response_2.wav")
                with open(audio_file, "wb") as f:
                    f.write(audio_data)
                logger.info(f"Saved audio response to {audio_file}")
        
        # If text is provided, send it to the agent
        if args.text:
            logger.info(f"Sending text: {args.text}")
            
            # Send text to agent
            await session.send_text(args.text)
            
            # Wait for response
            logger.info("Waiting for response to text...")
            response_text = await session.receive_text()
            logger.info(f"Received text response: {response_text}")
            
            # Get audio response
            audio_data = await session.receive_audio()
            if audio_data:
                logger.info(f"Received {len(audio_data)} bytes of audio")
                
                # Save audio to file
                audio_file = os.path.join(args.output_dir, "response_3.wav")
                with open(audio_file, "wb") as f:
                    f.write(audio_data)
                logger.info(f"Saved audio response to {audio_file}")
        
        # Send goodbye message
        logger.info("Sending goodbye message")
        await session.send_text("Thank you for your help. Goodbye!")
        
        # Wait for response
        logger.info("Waiting for final response...")
        response_text = await session.receive_text()
        logger.info(f"Received final text response: {response_text}")
        
        # Get audio response
        audio_data = await session.receive_audio()
        if audio_data:
            logger.info(f"Received {len(audio_data)} bytes of audio")
            
            # Save audio to file
            audio_file = os.path.join(args.output_dir, "response_final.wav")
            with open(audio_file, "wb") as f:
                f.write(audio_data)
            logger.info(f"Saved final audio response to {audio_file}")
        
        # Close the session
        logger.info("Closing session")
        await session.close()
        
    except Exception as e:
        logger.error(f"Error in conversation: {e}")
        if session:
            await session.close()
    
    logger.info("Example completed")

if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
