#!/usr/bin/env python3
"""
Configure the Guestrix Twilio SIP trunk.

This script configures the existing 'guestrix' Twilio SIP trunk with a LiveKit SIP URI.
It uses the Twilio Python SDK instead of the CLI for better error handling.
"""

import os
import sys
import logging
import argparse
from dotenv import load_dotenv
from twilio.rest import Client
from twilio.base.exceptions import TwilioRestException

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("configure-guestrix-trunk")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Configure the Guestrix Twilio SIP trunk")
    parser.add_argument("--livekit-sip-uri", default="voice.guestrix.ai", help="LiveKit SIP URI (default: voice.guestrix.ai)")
    parser.add_argument("--trunk-name", default="guestrix", help="Name of the Twilio SIP trunk")
    return parser.parse_args()

def main():
    """Main function."""
    # Load environment variables
    load_dotenv()

    # Parse arguments
    args = parse_arguments()

    # Get Twilio credentials from environment
    account_sid = os.getenv("TWILIO_ACCOUNT_SID")
    auth_token = os.getenv("TWILIO_AUTH_TOKEN")

    if not account_sid or not auth_token:
        logger.error("TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN must be set in environment variables")
        sys.exit(1)

    # Initialize Twilio client
    client = Client(account_sid, auth_token)

    # Get the trunk SID
    logger.info(f"Looking for trunk: {args.trunk_name}")
    try:
        # Get all trunks and filter by friendly_name
        all_trunks = client.trunking.v1.trunks.list()
        trunks = [t for t in all_trunks if t.friendly_name == args.trunk_name]

        if not trunks:
            logger.error(f"Trunk '{args.trunk_name}' not found")
            sys.exit(1)

        trunk = trunks[0]
        logger.info(f"Found trunk: {trunk.friendly_name} (SID: {trunk.sid})")

        # Check if origination URL already exists
        origination_urls = trunk.origination_urls.list()

        if origination_urls:
            # Update existing origination URL
            origination_url = origination_urls[0]
            logger.info(f"Updating existing origination URL: {origination_url.sip_url} (SID: {origination_url.sid})")

            origination_url = trunk.origination_urls(origination_url.sid).update(
                friendly_name="LiveKit SIP URI",
                sip_url=f"sip:{args.livekit_sip_uri}",
                priority=1,
                weight=1,
                enabled=True
            )

            logger.info(f"Updated origination URL to: {origination_url.sip_url}")
        else:
            # Create new origination URL
            logger.info(f"Creating new origination URL: sip:{args.livekit_sip_uri}")

            origination_url = trunk.origination_urls.create(
                friendly_name="LiveKit SIP URI",
                sip_url=f"sip:{args.livekit_sip_uri}",
                priority=1,
                weight=1,
                enabled=True
            )

            logger.info(f"Created origination URL: {origination_url.sip_url} (SID: {origination_url.sid})")

        # List phone numbers associated with the trunk
        phone_numbers = trunk.phone_numbers.list()

        if phone_numbers:
            logger.info("Phone numbers associated with this trunk:")
            for phone_number in phone_numbers:
                logger.info(f"  - {phone_number.phone_number_sid}")
        else:
            logger.warning("No phone numbers associated with this trunk")
            logger.info("You should associate a phone number with this trunk")

        logger.info("Trunk configuration complete!")
        logger.info(f"Trunk '{args.trunk_name}' is now configured to use LiveKit SIP URI: sip:{args.livekit_sip_uri}")

    except TwilioRestException as e:
        logger.error(f"Twilio API error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
