#!/usr/bin/env python3
"""
Script to configure a LiveKit SIP trunk.

This script demonstrates how to create and configure a SIP trunk in LiveKit
using the LiveKit Server SDK.
"""

import os
import logging
import argparse
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("livekit-sip-config")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Configure a LiveKit SIP trunk")
    parser.add_argument("--name", default="twilio-trunk", help="Name of the SIP trunk")
    parser.add_argument("--inbound", action="store_true", help="Configure for inbound calls")
    parser.add_argument("--outbound", action="store_true", help="Configure for outbound calls")
    parser.add_argument("--twilio-domain", help="Twilio domain name (e.g., my-trunk.pstn.twilio.com)")
    parser.add_argument("--twilio-username", help="Twilio username for authentication")
    parser.add_argument("--twilio-password", help="Twilio password for authentication")
    return parser.parse_args()

def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    # Parse arguments
    args = parse_arguments()
    
    # Get LiveKit API key and secret from environment
    api_key = os.getenv("LIVEKIT_API_KEY")
    api_secret = os.getenv("LIVEKIT_API_SECRET")
    
    if not api_key or not api_secret:
        logger.error("LIVEKIT_API_KEY and LIVEKIT_API_SECRET must be set in environment variables")
        return
    
    logger.info(f"Configuring LiveKit SIP trunk: {args.name}")
    
    # In a real implementation, you would use the LiveKit Server SDK to:
    # 1. Create a SIP trunk
    # 2. Configure it for inbound and/or outbound calls
    # 3. Set up authentication
    
    # For now, we'll just print the configuration
    logger.info("SIP trunk configuration:")
    logger.info(f"  Name: {args.name}")
    logger.info(f"  Inbound: {args.inbound}")
    logger.info(f"  Outbound: {args.outbound}")
    
    if args.inbound:
        logger.info("Inbound configuration:")
        # In a real implementation, you would get the SIP URI from LiveKit
        sip_uri = f"{args.name}.sip.livekit.cloud"
        logger.info(f"  SIP URI: {sip_uri}")
        logger.info("To configure Twilio for inbound calls:")
        logger.info(f"  1. Set the Origination URI to: sip:{sip_uri}")
    
    if args.outbound and args.twilio_domain:
        logger.info("Outbound configuration:")
        logger.info(f"  Twilio domain: {args.twilio_domain}")
        if args.twilio_username and args.twilio_password:
            logger.info("  Authentication: Configured")
        else:
            logger.info("  Authentication: Not configured")
    
    logger.info("Configuration complete")
    logger.info("Note: This is a demonstration script. In a real implementation, you would use the LiveKit Server SDK.")

if __name__ == "__main__":
    main()
