#!/bin/bash
# Script to configure Nginx for LiveKit

# Exit on error
set -e

# Set variables
NGINX_CONF="/etc/nginx/sites-available/voice.guestrix.ai"
NGINX_CONF_BACKUP="${NGINX_CONF}.bak.$(date +%Y%m%d%H%M%S)"

echo "Configuring Nginx for LiveKit..."

# Create a backup of the original configuration
sudo cp $NGINX_CONF $NGINX_CONF_BACKUP
echo "Created backup of Nginx configuration at $NGINX_CONF_BACKUP"

# Create a temporary file for the new configuration
TEMP_CONF=$(mktemp)

# Find the last closing brace in the file
LAST_BRACE_LINE=$(sudo grep -n "}" $NGINX_CONF | tail -1 | cut -d: -f1)

if [ -n "$LAST_BRACE_LINE" ]; then
    # Extract the content before the last closing brace
    sudo head -n $(($LAST_BRACE_LINE - 1)) $NGINX_CONF > $TEMP_CONF
    
    # Append the LiveKit locations
    cat >> $TEMP_CONF << EOF
    # LiveKit HTTP API
    location /livekit/ {
        proxy_pass http://localhost:7880/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # LiveKit WebRTC
    location /livekit/rtc/ {
        proxy_pass http://localhost:7881/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    # Replace the original file
    sudo mv $TEMP_CONF $NGINX_CONF
    echo "Added LiveKit locations to Nginx configuration"
else
    echo "Error: Could not find the closing brace in the Nginx configuration."
    exit 1
fi

# Test Nginx configuration
echo "Testing Nginx configuration..."
sudo nginx -t

# Reload Nginx if the test is successful
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "Nginx configuration updated successfully."
else
    echo "Nginx configuration test failed. Please check the configuration."
    echo "Restoring backup..."
    sudo cp $NGINX_CONF_BACKUP $NGINX_CONF
    sudo systemctl reload nginx
    exit 1
fi

echo "Nginx configuration for LiveKit completed."
echo "LiveKit is now accessible at:"
echo "  - HTTP API: https://voice.guestrix.ai/livekit/"
echo "  - WebRTC: https://voice.guestrix.ai/livekit/rtc/"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:voice.guestrix.ai"
