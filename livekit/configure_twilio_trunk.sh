#!/bin/bash
# Script to configure Twilio SIP trunk for LiveKit integration

# Set variables
TRUNK_NAME="guestrix"
LIVEKIT_SIP_URI="your_livekit_sip_uri"  # Replace with your actual LiveKit SIP URI

# Check if Twilio CLI is installed
if ! command -v twilio &> /dev/null; then
    echo "Twilio CLI is not installed. Please install it first."
    echo "Visit: https://www.twilio.com/docs/twilio-cli/getting-started/install"
    exit 1
fi

# Check if a Twilio profile is configured
if ! twilio profiles:list | grep -q "active"; then
    echo "No active Twilio profile found. Please configure one first."
    echo "Run: twilio login"
    exit 1
fi

# Check if LiveKit SIP URI is set
if [ "$LIVEKIT_SIP_URI" == "your_livekit_sip_uri" ]; then
    echo "Please edit this script to set your actual LiveKit SIP URI."
    echo "Example: LIVEKIT_SIP_URI=\"your-sip-id.sip.livekit.cloud\""
    exit 1
fi

# Get the trunk SID
echo "Getting SID for trunk: $TRUNK_NAME"
TRUNK_SID=$(twilio api trunking v1 trunks list --friendly-name "$TRUNK_NAME" -o json | jq -r '.[0].sid')

if [ -z "$TRUNK_SID" ] || [ "$TRUNK_SID" == "null" ]; then
    echo "Trunk not found. Creating a new trunk..."
    DOMAIN_NAME="${TRUNK_NAME}.pstn.twilio.com"
    TRUNK_RESPONSE=$(twilio api trunking v1 trunks create \
        --friendly-name "$TRUNK_NAME" \
        --domain-name "$DOMAIN_NAME")

    TRUNK_SID=$(echo "$TRUNK_RESPONSE" | grep "SID" | awk '{print $2}')
    echo "Created trunk with SID: $TRUNK_SID"
else
    echo "Found existing trunk with SID: $TRUNK_SID"
fi

# Configure the trunk for inbound calls
echo "Configuring trunk for inbound calls..."
echo "Setting origination URI to: sip:$LIVEKIT_SIP_URI"

# Check if origination URL already exists
ORIGINATION_URLS=$(twilio api trunking v1 trunks origination-urls list --trunk-sid "$TRUNK_SID" -o json)
ORIGINATION_COUNT=$(echo "$ORIGINATION_URLS" | jq 'length')

if [ "$ORIGINATION_COUNT" -gt 0 ]; then
    echo "Existing origination URLs found. Updating..."

    # Get the first origination URL SID
    ORIGINATION_URL_SID=$(echo "$ORIGINATION_URLS" | jq -r '.[0].sid')

    # Update the existing origination URL
    twilio api trunking v1 trunks origination-urls update \
        --trunk-sid "$TRUNK_SID" \
        --sid "$ORIGINATION_URL_SID" \
        --friendly-name "LiveKit SIP URI" \
        --sip-url "sip:$LIVEKIT_SIP_URI" \
        --weight 1 --priority 1 --enabled

    echo "Updated origination URL with SID: $ORIGINATION_URL_SID"
else
    # Create a new origination URL
    ORIGINATION_RESPONSE=$(twilio api trunking v1 trunks origination-urls create \
        --trunk-sid "$TRUNK_SID" \
        --friendly-name "LiveKit SIP URI" \
        --sip-url "sip:$LIVEKIT_SIP_URI" \
        --weight 1 --priority 1 --enabled)

    ORIGINATION_URL_SID=$(echo "$ORIGINATION_RESPONSE" | grep "SID" | awk '{print $2}')
    echo "Created origination URL with SID: $ORIGINATION_URL_SID"
fi

echo "Trunk configuration complete!"
echo "Next steps:"
echo "1. Associate a phone number with the trunk"
echo "2. Configure LiveKit to accept calls from this trunk"
