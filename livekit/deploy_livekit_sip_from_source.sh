#!/bin/bash
# Deployment script for LiveKit SIP Server from source

# Set variables
EC2_HOST="<EMAIL>"
SSH_KEY="./concierge/infra/guestrix-key-pair.pem"
REMOTE_DIR="/home/<USER>/livekit"
LOCAL_FIX_SCRIPT="./livekit/fix_livekit_config.sh"
LOCAL_SIP_SCRIPT="./livekit/install_livekit_sip_from_source.sh"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[DEPLOY] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    print_error "SSH key not found: $SSH_KEY"
    print_message "Checking for alternative key locations..."

    # Try to find the key in the infra directory
    INFRA_KEY="./infra/guestrix-key-pair.pem"
    if [ -f "$INFRA_KEY" ]; then
        print_message "Found key at $INFRA_KEY"
        SSH_KEY="$INFRA_KEY"
    else
        print_error "No SSH key found. Please provide the correct path to the SSH key."
        exit 1
    fi
fi

# Make sure the SSH key has the right permissions
chmod 600 "$SSH_KEY"

# Test SSH connection
print_message "Testing SSH connection to $EC2_HOST..."
ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    print_error "Failed to connect to $EC2_HOST. Please check your SSH key and connection."
    exit 1
fi

print_message "SSH connection successful."

# Create remote directory if it doesn't exist
print_message "Creating remote directory if it doesn't exist..."
ssh -i "$SSH_KEY" "$EC2_HOST" "mkdir -p $REMOTE_DIR"

# Transfer the fix script
print_message "Transferring LiveKit Server fix script..."
scp -i "$SSH_KEY" "$LOCAL_FIX_SCRIPT" "$EC2_HOST:$REMOTE_DIR/fix_livekit_config.sh"

# Make the script executable on the server
print_message "Making the fix script executable..."
ssh -i "$SSH_KEY" "$EC2_HOST" "chmod +x $REMOTE_DIR/fix_livekit_config.sh"

# Execute the fix script
print_message "Executing LiveKit Server fix script on the server..."
ssh -i "$SSH_KEY" "$EC2_HOST" "cd $REMOTE_DIR && sudo ./fix_livekit_config.sh"

# Transfer the SIP installation script
print_message "Transferring LiveKit SIP Server installation script..."
scp -i "$SSH_KEY" "$LOCAL_SIP_SCRIPT" "$EC2_HOST:$REMOTE_DIR/install_livekit_sip_from_source.sh"

# Make the script executable on the server
print_message "Making the SIP installation script executable..."
ssh -i "$SSH_KEY" "$EC2_HOST" "chmod +x $REMOTE_DIR/install_livekit_sip_from_source.sh"

# Execute the SIP installation script
print_message "Executing LiveKit SIP Server installation script on the server..."
ssh -i "$SSH_KEY" "$EC2_HOST" "cd $REMOTE_DIR && sudo ./install_livekit_sip_from_source.sh"

print_message "Deployment completed."
