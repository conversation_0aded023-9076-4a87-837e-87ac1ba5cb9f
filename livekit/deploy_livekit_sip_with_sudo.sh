#!/bin/bash
# Deployment script for LiveKit SIP Server with sudo

# Set variables
EC2_HOST="<EMAIL>"
SSH_KEY="./concierge/infra/guestrix-key-pair.pem"
REMOTE_DIR="/home/<USER>"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[DEPLOY] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    print_error "SSH key not found: $SSH_KEY"
    print_message "Checking for alternative key locations..."

    # Try to find the key in the infra directory
    INFRA_KEY="./infra/guestrix-key-pair.pem"
    if [ -f "$INFRA_KEY" ]; then
        print_message "Found key at $INFRA_KEY"
        SSH_KEY="$INFRA_KEY"
    else
        print_error "No SSH key found. Please provide the correct path to the SSH key."
        exit 1
    fi
fi

# Make sure the SSH key has the right permissions
chmod 600 "$SSH_KEY"

# Test SSH connection
print_message "Testing SSH connection to $EC2_HOST..."
ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    print_error "Failed to connect to $EC2_HOST. Please check your SSH key and connection."
    exit 1
fi

print_message "SSH connection successful."

# Create installation script directly on the server
print_message "Creating installation script directly on the server..."
ssh -i "$SSH_KEY" "$EC2_HOST" "cat > ${REMOTE_DIR}/install_livekit_sip.sh" << 'EOF'
#!/bin/bash
# Script to install LiveKit SIP Server

# Exit on error
set -e

# Set variables
DOMAIN="voice.guestrix.ai"  # Using the existing domain
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
LIVEKIT_DIR="/home/<USER>/livekit"
SIP_DIR="${LIVEKIT_DIR}/sip"
CONFIG_DIR="${SIP_DIR}/config"
SYSTEMD_DIR="/etc/systemd/system"

echo "Setting up LiveKit SIP Server on $(hostname)"
echo "Domain: ${DOMAIN}"

# Create directories
sudo mkdir -p ${SIP_DIR}
sudo mkdir -p ${CONFIG_DIR}
sudo chown -R ubuntu:ubuntu ${LIVEKIT_DIR}

# Install dependencies
echo "Installing dependencies..."
sudo apt-get update
sudo apt-get install -y redis-server pkg-config libopus-dev libopusfile-dev libsoxr-dev git wget

# Install Go 1.22
echo "Installing Go 1.22..."
wget https://go.dev/dl/go1.22.1.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.22.1.linux-amd64.tar.gz
rm go1.22.1.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
export PATH=$PATH:/usr/local/go/bin

# Enable and start Redis
echo "Enabling and starting Redis..."
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Clone or update the LiveKit SIP repository
echo "Cloning or updating LiveKit SIP repository..."
if [ -d "${SIP_DIR}/repo" ]; then
    echo "Repository already exists, updating..."
    cd ${SIP_DIR}/repo
    git fetch
    git reset --hard origin/main
else
    echo "Cloning repository..."
    git clone https://github.com/livekit/sip.git ${SIP_DIR}/repo
fi

# Build the LiveKit SIP Server
echo "Building LiveKit SIP Server..."
cd ${SIP_DIR}/repo
export PATH=$PATH:/usr/local/go/bin
/usr/local/go/bin/go build -o ${SIP_DIR}/livekit-sip ./cmd/livekit-sip

# Make the binary executable
chmod +x ${SIP_DIR}/livekit-sip

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/config.yaml << EOC
api_key: ${API_KEY}
api_secret: ${API_SECRET}
ws_url: ws://localhost:7880
redis:
  address: localhost:6379
sip_port: 5060
rtp_port: 10000-20000
use_external_ip: true
logging:
  level: info
EOC

# Create systemd service
echo "Creating systemd service..."
cat > /tmp/livekit-sip.service << EOC
[Unit]
Description=LiveKit SIP Server
After=network.target redis-server.service livekit.service

[Service]
Type=simple
User=ubuntu
ExecStart=${SIP_DIR}/livekit-sip --config=${CONFIG_DIR}/config.yaml
Restart=always
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOC

# Copy systemd service
echo "Copying systemd service..."
sudo cp /tmp/livekit-sip.service /etc/systemd/system/

# Open firewall ports for SIP
echo "Opening firewall ports for SIP..."
sudo ufw allow 5060/udp
sudo ufw allow 5060/tcp
sudo ufw allow 10000:20000/udp

# Enable and start the LiveKit SIP service
echo "Enabling and starting LiveKit SIP service..."
sudo systemctl daemon-reload
sudo systemctl enable livekit-sip
sudo systemctl start livekit-sip

# Check status
echo "Checking LiveKit SIP service status..."
sudo systemctl status livekit-sip

echo "LiveKit SIP Server installation complete!"
echo "SIP URI: ${DOMAIN}:5060"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:${DOMAIN}:5060"
echo ""
echo "To check the logs:"
echo "sudo journalctl -u livekit-sip -f"
EOF

# Make the script executable
print_message "Making the installation script executable..."
ssh -i "$SSH_KEY" "$EC2_HOST" "chmod +x ${REMOTE_DIR}/install_livekit_sip.sh"

# Execute the installation script
print_message "Executing the installation script on the server..."
ssh -i "$SSH_KEY" "$EC2_HOST" "cd ${REMOTE_DIR} && ./install_livekit_sip.sh"

print_message "Deployment completed."
