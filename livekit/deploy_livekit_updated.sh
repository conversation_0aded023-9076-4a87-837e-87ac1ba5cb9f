#!/bin/bash
# Updated deployment script for LiveKit server setup
# This script creates an installation script locally and transfers it to the EC2 server

# Set variables
EC2_HOST="<EMAIL>"
SSH_KEY="./concierge/infra/guestrix-key-pair.pem"
REMOTE_DIR="/home/<USER>/livekit"
LOCAL_DIR="./livekit"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[DEPLOY] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if SSH key exists
if [ ! -f "$SSH_KEY" ]; then
    print_error "SSH key not found: $SSH_KEY"
    print_message "Checking for alternative key locations..."

    # Try to find the key in the infra directory
    INFRA_KEY="./infra/guestrix-key-pair.pem"
    if [ -f "$INFRA_KEY" ]; then
        print_message "Found key at $INFRA_KEY"
        SSH_KEY="$INFRA_KEY"
    else
        print_error "No SSH key found. Please provide the correct path to the SSH key."
        exit 1
    fi
fi

# Make sure the SSH key has the right permissions
chmod 600 "$SSH_KEY"

# Test SSH connection
print_message "Testing SSH connection to $EC2_HOST..."
ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o BatchMode=yes -o StrictHostKeyChecking=accept-new "$EC2_HOST" "echo Connection successful" > /dev/null 2>&1

if [ $? -ne 0 ]; then
    print_error "Failed to connect to $EC2_HOST. Please check your SSH key and connection."
    exit 1
fi

print_message "SSH connection successful."

# Create the installation script locally
print_message "Creating installation script..."
cat > ${LOCAL_DIR}/install_livekit_updated.sh << 'EOFSCRIPT'
#!/bin/bash
# Script to install LiveKit on the server

# Exit on error
set -e

# Set variables
LIVEKIT_VERSION="1.8.4"  # Latest version as of May 2025
DOMAIN="voice.guestrix.ai"  # Using the existing domain
EMAIL="<EMAIL>"  # Update this to your email
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
LIVEKIT_DIR="/home/<USER>/livekit"
CONFIG_DIR="${LIVEKIT_DIR}/config"
SYSTEMD_DIR="/etc/systemd/system"
NGINX_DIR="/etc/nginx/sites-available"

echo "Setting up LiveKit Server v${LIVEKIT_VERSION} on $(hostname)"
echo "Domain: ${DOMAIN}"
echo "Email: ${EMAIL}"

# Create directories
mkdir -p ${CONFIG_DIR}

# Download LiveKit Server directly
echo "Downloading LiveKit Server..."
wget -O ${LIVEKIT_DIR}/livekit-server.tar.gz https://github.com/livekit/livekit-server/releases/download/v${LIVEKIT_VERSION}/livekit-server-v${LIVEKIT_VERSION}-linux-amd64.tar.gz
mkdir -p ${LIVEKIT_DIR}/tmp
tar -xzf ${LIVEKIT_DIR}/livekit-server.tar.gz -C ${LIVEKIT_DIR}/tmp
# Find the livekit-server binary in the extracted files
find ${LIVEKIT_DIR}/tmp -name "livekit-server" -type f -exec cp {} ${LIVEKIT_DIR}/ \;
rm -rf ${LIVEKIT_DIR}/tmp
rm ${LIVEKIT_DIR}/livekit-server.tar.gz
chmod +x ${LIVEKIT_DIR}/livekit-server

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: true
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
  json: true
sip:
  enabled: true
  domain: ${DOMAIN}
  inbound:
    enabled: true
    auth:
      enabled: false
  outbound:
    enabled: true
    auth:
      enabled: false
EOF

# Create systemd service
echo "Creating systemd service..."
cat > ${LIVEKIT_DIR}/livekit.service << EOF
[Unit]
Description=LiveKit Server
After=network.target

[Service]
Type=simple
User=ubuntu
ExecStart=${LIVEKIT_DIR}/livekit-server --config ${CONFIG_DIR}/livekit.yaml
Restart=always
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# Create Nginx configuration update
echo "Creating Nginx configuration update..."
cat > ${LIVEKIT_DIR}/livekit-nginx.conf << EOF
# LiveKit configuration for voice.guestrix.ai
# Add this to the existing server block

    # LiveKit HTTP API
    location /livekit/ {
        proxy_pass http://localhost:7880/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # LiveKit WebRTC
    location /livekit/rtc/ {
        proxy_pass http://localhost:7881/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
EOF

# Copy systemd service
echo "Copying systemd service..."
sudo cp ${LIVEKIT_DIR}/livekit.service /etc/systemd/system/

# Update Nginx configuration
echo "Updating Nginx configuration..."
NGINX_CONF="/etc/nginx/sites-available/voice.guestrix.ai"
TEMP_CONF="$(mktemp)"

# Create a backup of the original configuration
sudo cp $NGINX_CONF ${NGINX_CONF}.bak.$(date +%Y%m%d%H%M%S)

# Extract the server block content
sudo grep -v "}" $NGINX_CONF > $TEMP_CONF

# Append the LiveKit configuration
cat ${LIVEKIT_DIR}/livekit-nginx.conf >> $TEMP_CONF

# Close the server block
echo "}" >> $TEMP_CONF

# Replace the original configuration
sudo mv $TEMP_CONF $NGINX_CONF

# Test Nginx configuration
echo "Testing Nginx configuration..."
sudo nginx -t

# Reload Nginx if the test is successful
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "Nginx configuration updated successfully."
else
    echo "Nginx configuration test failed. Please check the configuration."
    exit 1
fi

# Enable and start the LiveKit service
echo "Enabling and starting LiveKit service..."
sudo systemctl daemon-reload
sudo systemctl enable livekit
sudo systemctl start livekit

# Check status
echo "Checking LiveKit service status..."
sudo systemctl status livekit

echo "LiveKit Server installation complete!"
echo "API Key: ${API_KEY}"
echo "API Secret: ${API_SECRET}"
echo "SIP URI: ${DOMAIN}"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:${DOMAIN}"
echo ""
echo "To check the logs:"
echo "sudo journalctl -u livekit -f"
EOFSCRIPT

# Make the script executable
chmod +x ${LOCAL_DIR}/install_livekit_updated.sh

# Create remote directory if it doesn't exist
print_message "Creating remote directory if it doesn't exist..."
ssh -i "$SSH_KEY" "$EC2_HOST" "mkdir -p $REMOTE_DIR"

# Transfer the installation script
print_message "Transferring installation script..."
scp -i "$SSH_KEY" "${LOCAL_DIR}/install_livekit_updated.sh" "$EC2_HOST:$REMOTE_DIR/install_livekit.sh"

# Make the script executable on the server
print_message "Making the script executable..."
ssh -i "$SSH_KEY" "$EC2_HOST" "chmod +x $REMOTE_DIR/install_livekit.sh"

# Execute the installation script
print_message "Executing installation script on the server..."
ssh -i "$SSH_KEY" "$EC2_HOST" "cd $REMOTE_DIR && ./install_livekit.sh"

print_message "Deployment completed."
