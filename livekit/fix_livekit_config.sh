#!/bin/bash
# Script to fix the LiveKit Server configuration

# Exit on error
set -e

# Set variables
LIVEKIT_DIR="/home/<USER>/livekit"
CONFIG_DIR="${LIVEKIT_DIR}/config"

echo "Fixing LiveKit Server configuration..."

# Create a new configuration file
cat > /tmp/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: true
keys:
  guestrix_key: guestrix_secret
logging:
  level: info
  json: true
EOF

# Replace the original file
sudo mv /tmp/livekit.yaml ${CONFIG_DIR}/livekit.yaml
echo "Updated LiveKit Server configuration"

# Restart the LiveKit service
echo "Restarting LiveKit service..."
sudo systemctl restart livekit

# Check status
echo "Checking LiveKit service status..."
sudo systemctl status livekit

echo "LiveKit Server configuration fixed."
