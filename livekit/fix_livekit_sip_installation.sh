#!/bin/bash
# Script to fix LiveKit SIP Server installation

# Exit on error
set -e

# Set variables
DOMAIN="voice.guestrix.ai"  # Using the existing domain
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
LIVEKIT_DIR="/home/<USER>/livekit"
SIP_DIR="${LIVEKIT_DIR}/sip"
CONFIG_DIR="${SIP_DIR}/config"
SYSTEMD_DIR="/etc/systemd/system"

echo "Fixing LiveKit SIP Server installation on $(hostname)"
echo "Domain: ${DOMAIN}"

# Create directories
mkdir -p ${SIP_DIR}
mkdir -p ${CONFIG_DIR}

# Check Go installation
if [ ! -d "/usr/local/go" ]; then
    echo "Installing Go 1.22..."
    wget https://go.dev/dl/go1.22.1.linux-amd64.tar.gz
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf go1.22.1.linux-amd64.tar.gz
    rm go1.22.1.linux-amd64.tar.gz
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
    export PATH=$PATH:/usr/local/go/bin
fi

# Clone or update the LiveKit SIP repository
echo "Cloning or updating LiveKit SIP repository..."
if [ -d "${SIP_DIR}/repo" ]; then
    echo "Repository already exists, updating..."
    cd ${SIP_DIR}/repo
    git fetch
    git reset --hard origin/main
else
    echo "Cloning repository..."
    git clone https://github.com/livekit/sip.git ${SIP_DIR}/repo
fi

# Build the LiveKit SIP Server
echo "Building LiveKit SIP Server..."
cd ${SIP_DIR}/repo
export PATH=$PATH:/usr/local/go/bin
/usr/local/go/bin/go build -o ${SIP_DIR}/livekit-sip ./cmd/livekit-sip

# Make the binary executable
chmod +x ${SIP_DIR}/livekit-sip

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/config.yaml << EOF
api_key: ${API_KEY}
api_secret: ${API_SECRET}
ws_url: ws://localhost:7880
redis:
  address: localhost:6379
sip_port: 5060
rtp_port: 10000-20000
use_external_ip: true
logging:
  level: info
EOF

# Create systemd service
echo "Creating systemd service..."
cat > ${SIP_DIR}/livekit-sip.service << EOF
[Unit]
Description=LiveKit SIP Server
After=network.target redis-server.service livekit.service

[Service]
Type=simple
User=ubuntu
ExecStart=${SIP_DIR}/livekit-sip --config=${CONFIG_DIR}/config.yaml
Restart=always
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# Copy systemd service
echo "Copying systemd service..."
sudo cp ${SIP_DIR}/livekit-sip.service /etc/systemd/system/

# Open firewall ports for SIP
echo "Opening firewall ports for SIP..."
sudo ufw allow 5060/udp
sudo ufw allow 5060/tcp
sudo ufw allow 10000:20000/udp

# Enable and start the LiveKit SIP service
echo "Enabling and starting LiveKit SIP service..."
sudo systemctl daemon-reload
sudo systemctl enable livekit-sip
sudo systemctl start livekit-sip

# Check status
echo "Checking LiveKit SIP service status..."
sudo systemctl status livekit-sip

echo "LiveKit SIP Server installation fixed!"
echo "SIP URI: ${DOMAIN}:5060"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:${DOMAIN}:5060"
echo ""
echo "To check the logs:"
echo "sudo journalctl -u livekit-sip -f"
