#!/bin/bash
# Script to fix the Nginx configuration

# Exit on error
set -e

# Set variables
NGINX_CONF="/etc/nginx/sites-available/voice.guestrix.ai"
NGINX_CONF_BACKUP="${NGINX_CONF}.bak.$(date +%Y%m%d%H%M%S)"

echo "Fixing Nginx configuration..."

# Create a backup of the original configuration
sudo cp $NGINX_CONF $NGINX_CONF_BACKUP
echo "Created backup of Nginx configuration at $NGINX_CONF_BACKUP"

# Create a new configuration file
cat > /tmp/voice.guestrix.ai << EOF
server {
    listen 80;
    server_name voice.guestrix.ai;

    location /telnyx/ {
        proxy_pass http://localhost:8082;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # WebSocket endpoint
    location /ws/ {
        proxy_pass http://localhost:8083;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # LiveKit HTTP API
    location /livekit/ {
        proxy_pass http://localhost:7880/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # LiveKit WebRTC
    location /livekit/rtc/ {
        proxy_pass http://localhost:7881/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location / {
        return 404;
    }
}
EOF

# Replace the original file
sudo mv /tmp/voice.guestrix.ai $NGINX_CONF
echo "Created new Nginx configuration"

# Test Nginx configuration
echo "Testing Nginx configuration..."
sudo nginx -t

# Reload Nginx if the test is successful
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "Nginx configuration updated successfully."
else
    echo "Nginx configuration test failed. Please check the configuration."
    echo "Restoring backup..."
    sudo cp $NGINX_CONF_BACKUP $NGINX_CONF
    sudo systemctl reload nginx
    exit 1
fi

# Check if there's a disabled configuration
if [ -f "/etc/nginx/sites-enabled/voice.guestrix.ai.disabled" ]; then
    echo "Removing disabled configuration..."
    sudo rm /etc/nginx/sites-enabled/voice.guestrix.ai.disabled
fi

echo "Nginx configuration fixed."
echo "LiveKit is now accessible at:"
echo "  - HTTP API: https://voice.guestrix.ai/livekit/"
echo "  - WebRTC: https://voice.guestrix.ai/livekit/rtc/"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:voice.guestrix.ai"
