#!/usr/bin/env python3
"""
Gemini Live API integration for LiveKit.

This module provides a class for integrating Google's Gemini Live API
with LiveKit for real-time voice conversations.
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional, List

from livekit.agents import AgentSession
from livekit.plugins.google.beta.realtime import RealtimeModel

from .utils import mask_api_key

# Configure logging
logger = logging.getLogger("livekit.gemini")

class GeminiAgent:
    """
    Gemini Live API integration for LiveKit.

    This class provides methods for creating and managing a Gemini Live API
    agent session using LiveKit.
    """

    def __init__(
        self,
        api_key: str,
        model: str = "gemini-2.0-flash-exp",
        voice: str = "Puck",
        temperature: float = 0.8,
        instructions: Optional[str] = None
    ):
        """
        Initialize the Gemini agent.

        Args:
            api_key: The Gemini API key
            model: The Gemini model to use
            voice: The voice to use
            temperature: The temperature parameter for generation
            instructions: System instructions for the agent
        """
        self.api_key = api_key
        self.model = model
        self.voice = voice
        self.temperature = temperature
        self.instructions = instructions or self._default_instructions()

        # Set environment variable for authentication
        os.environ["GOOGLE_API_KEY"] = api_key

        # Initialize session
        self.session = None

        logger.info(f"Initialized Gemini agent with API key {mask_api_key(api_key)}")
        logger.info(f"Using model {model} with voice {voice}")

    def _default_instructions(self) -> str:
        """
        Get default system instructions for the agent.

        Returns:
            The default system instructions
        """
        return """
        You are a helpful AI assistant speaking with a caller on the phone.
        Be concise, conversational, and helpful.
        Respond to the caller's questions and requests in a natural way.
        If you don't know the answer to a question, say so.
        """

    async def create_session(self) -> AgentSession:
        """
        Create a new Gemini agent session.

        Returns:
            The agent session
        """
        try:
            logger.info("Creating Gemini agent session")

            # Create the session
            self.session = AgentSession(
                llm=RealtimeModel(
                    model=self.model,
                    voice=self.voice,
                    temperature=self.temperature,
                    instructions=self.instructions,
                    api_key=self.api_key,
                    modalities=["AUDIO"]  # Only use audio modality
                ),
            )

            logger.info("Gemini agent session created successfully")
            return self.session

        except Exception as e:
            logger.error(f"Error creating Gemini agent session: {e}")
            raise

    async def start_conversation(self, initial_message: Optional[str] = None) -> None:
        """
        Start a conversation with the agent.

        Args:
            initial_message: An optional initial message to send to the agent
        """
        if not self.session:
            await self.create_session()

        try:
            logger.info("Starting conversation with Gemini agent")

            # Start the conversation
            if initial_message:
                logger.info(f"Sending initial message: {initial_message}")
                await self.session.send_text(initial_message)

            logger.info("Conversation started successfully")

        except Exception as e:
            logger.error(f"Error starting conversation: {e}")
            raise

    async def send_audio(self, audio_data: bytes) -> None:
        """
        Send audio data to the agent.

        Args:
            audio_data: The audio data to send
        """
        if not self.session:
            await self.create_session()

        try:
            # Send audio data to the agent
            await self.session.send_audio(audio_data)
            logger.debug(f"Sent {len(audio_data)} bytes of audio to Gemini")

        except Exception as e:
            logger.error(f"Error sending audio to Gemini: {e}")
            raise

    async def receive_audio(self) -> Optional[bytes]:
        """
        Receive audio data from the agent.

        Returns:
            The audio data received from the agent, or None if no data is available
        """
        if not self.session:
            await self.create_session()

        try:
            # Receive audio data from the agent
            audio_data = await self.session.receive_audio()

            if audio_data:
                logger.debug(f"Received {len(audio_data)} bytes of audio from Gemini")
                return audio_data

            return None

        except Exception as e:
            logger.error(f"Error receiving audio from Gemini: {e}")
            return None

    async def close(self) -> None:
        """
        Close the agent session.
        """
        if self.session:
            try:
                logger.info("Closing Gemini agent session")
                await self.session.close()
                self.session = None
                logger.info("Gemini agent session closed successfully")

            except Exception as e:
                logger.error(f"Error closing Gemini agent session: {e}")
                raise
