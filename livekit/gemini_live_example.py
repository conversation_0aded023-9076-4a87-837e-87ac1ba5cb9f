#!/usr/bin/env python3
"""
Example script demonstrating how to use LiveKit Agents with Gemini Live API.

This script creates a simple voice agent using the Gemini Live API and
demonstrates how to send and receive audio.
"""

import os
import asyncio
import logging
from dotenv import load_dotenv

# Import LiveKit Agents
try:
    from livekit.agents import AgentSession
    from livekit.plugins.google.beta.realtime import RealtimeModel
except ImportError:
    print("LiveKit Agents not found. Please install with: pip install livekit-agents livekit-plugins-google")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("gemini-live-example")

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    # Get API key from environment
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("GEMINI_API_KEY not found in environment variables")
        return
    
    logger.info("Creating Gemini Live agent session")
    
    # Create the agent session
    session = AgentSession(
        llm=RealtimeModel(
            model="gemini-2.0-flash-exp",
            voice="Puck",
            temperature=0.8,
            instructions="""
            You are a helpful AI assistant speaking with a caller on the phone.
            Be concise, conversational, and helpful.
            Respond to the caller's questions and requests in a natural way.
            If you don't know the answer to a question, say so.
            """,
            api_key=api_key,
            modalities=["AUDIO"]  # Only use audio modality
        ),
    )
    
    logger.info("Gemini Live agent session created successfully")
    
    try:
        # Start the conversation
        logger.info("Starting conversation")
        await session.send_text("Hello, how can I help you today?")
        
        # In a real application, you would:
        # 1. Capture audio from the caller
        # 2. Send it to the agent using session.send_audio(audio_data)
        # 3. Receive audio from the agent using session.receive_audio()
        # 4. Play the audio back to the caller
        
        # For this example, we'll just wait for a bit
        logger.info("Waiting for 5 seconds...")
        await asyncio.sleep(5)
        
        # Close the session
        logger.info("Closing session")
        await session.close()
        
    except Exception as e:
        logger.error(f"Error in conversation: {e}")
    
    logger.info("Example completed")

if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
