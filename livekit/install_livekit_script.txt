#!/bin/bash
# Script to install LiveKit on the server

# Exit on error
set -e

# Set variables
LIVEKIT_VERSION="1.8.4"  # Latest version as of May 2025
DOMAIN="voice.guestrix.ai"  # Using the existing domain
EMAIL="<EMAIL>"  # Update this to your email
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
LIVEKIT_DIR="/home/<USER>/livekit"
CONFIG_DIR="${LIVEKIT_DIR}/config"
SYSTEMD_DIR="/etc/systemd/system"
NGINX_DIR="/etc/nginx/sites-available"

echo "Setting up LiveKit Server v${LIVEKIT_VERSION} on $(hostname)"
echo "Domain: ${DOMAIN}"
echo "Email: ${EMAIL}"

# Create directories
mkdir -p ${CONFIG_DIR}

# Download LiveKit Server using the official installer
echo "Downloading LiveKit Server..."
curl -s https://get.livekit.io | bash
# Copy the binary to our directory
sudo cp /usr/local/bin/livekit-server ${LIVEKIT_DIR}/
chmod +x ${LIVEKIT_DIR}/livekit-server

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: true
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
  json: true
sip:
  enabled: true
  domain: ${DOMAIN}
  inbound:
    enabled: true
    auth:
      enabled: false
  outbound:
    enabled: true
    auth:
      enabled: false
EOF

# Create systemd service
echo "Creating systemd service..."
cat > ${LIVEKIT_DIR}/livekit.service << EOF
[Unit]
Description=LiveKit Server
After=network.target

[Service]
Type=simple
User=ubuntu
ExecStart=${LIVEKIT_DIR}/livekit-server --config ${CONFIG_DIR}/livekit.yaml
Restart=always
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# Create Nginx configuration update
echo "Creating Nginx configuration update..."
cat > ${LIVEKIT_DIR}/livekit-nginx.conf << EOF
# LiveKit configuration for voice.guestrix.ai
# Add this to the existing server block

    # LiveKit HTTP API
    location /livekit/ {
        proxy_pass http://localhost:7880/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # LiveKit WebRTC
    location /livekit/rtc/ {
        proxy_pass http://localhost:7881/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
EOF

# Copy systemd service
echo "Copying systemd service..."
sudo cp ${LIVEKIT_DIR}/livekit.service /etc/systemd/system/

# Update Nginx configuration
echo "Updating Nginx configuration..."
NGINX_CONF="/etc/nginx/sites-available/voice.guestrix.ai"
TEMP_CONF="$(mktemp)"

# Create a backup of the original configuration
sudo cp $NGINX_CONF ${NGINX_CONF}.bak.$(date +%Y%m%d%H%M%S)

# Extract the server block content
sudo grep -v "}" $NGINX_CONF > $TEMP_CONF

# Append the LiveKit configuration
cat ${LIVEKIT_DIR}/livekit-nginx.conf >> $TEMP_CONF

# Close the server block
echo "}" >> $TEMP_CONF

# Replace the original configuration
sudo mv $TEMP_CONF $NGINX_CONF

# Test Nginx configuration
echo "Testing Nginx configuration..."
sudo nginx -t

# Reload Nginx if the test is successful
if [ $? -eq 0 ]; then
    sudo systemctl reload nginx
    echo "Nginx configuration updated successfully."
else
    echo "Nginx configuration test failed. Please check the configuration."
    exit 1
fi

# Enable and start the LiveKit service
echo "Enabling and starting LiveKit service..."
sudo systemctl daemon-reload
sudo systemctl enable livekit
sudo systemctl start livekit

# Check status
echo "Checking LiveKit service status..."
sudo systemctl status livekit

echo "LiveKit Server installation complete!"
echo "API Key: ${API_KEY}"
echo "API Secret: ${API_SECRET}"
echo "SIP URI: ${DOMAIN}"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:${DOMAIN}"
echo ""
echo "To check the logs:"
echo "sudo journalctl -u livekit -f"
