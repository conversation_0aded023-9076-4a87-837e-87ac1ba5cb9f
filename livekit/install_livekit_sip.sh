#!/bin/bash
# Script to install LiveKit SIP Server

# Exit on error
set -e

# Set variables
LIVEKIT_VERSION="1.8.4"  # Latest version as of May 2025
DOMAIN="voice.guestrix.ai"  # Using the existing domain
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
LIVEKIT_DIR="/home/<USER>/livekit"
SIP_DIR="${LIVEKIT_DIR}/sip"
CONFIG_DIR="${SIP_DIR}/config"
SYSTEMD_DIR="/etc/systemd/system"

echo "Setting up LiveKit SIP Server on $(hostname)"
echo "Domain: ${DOMAIN}"

# Create directories
mkdir -p ${SIP_DIR}
mkdir -p ${CONFIG_DIR}

# Install Redis (required for SIP Server)
echo "Installing Redis..."
sudo apt-get update
sudo apt-get install -y redis-server

# Enable and start Redis
echo "Enabling and starting Redis..."
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Download LiveKit SIP Server
echo "Downloading LiveKit SIP Server..."
wget -O ${SIP_DIR}/livekit-sip.tar.gz https://github.com/livekit/sip/releases/download/v0.1.0/livekit-sip_0.1.0_linux_amd64.tar.gz
mkdir -p ${SIP_DIR}/tmp
tar -xzf ${SIP_DIR}/livekit-sip.tar.gz -C ${SIP_DIR}/tmp

# Find the livekit-sip binary in the extracted files
if find ${SIP_DIR}/tmp -name "livekit-sip" -type f -exec cp {} ${SIP_DIR}/ \; | grep -q .; then
    echo "Found and copied livekit-sip binary"
else
    # If livekit-sip not found, try to find any executable that might be the server
    echo "livekit-sip binary not found, looking for alternatives..."
    find ${SIP_DIR}/tmp -type f -executable -exec cp {} ${SIP_DIR}/livekit-sip \; -quit
    if [ -f "${SIP_DIR}/livekit-sip" ]; then
        echo "Found and copied an executable as livekit-sip"
    else
        echo "No executable found. Trying to find any file with 'sip' in the name..."
        find ${SIP_DIR}/tmp -name "*sip*" -type f -exec cp {} ${SIP_DIR}/livekit-sip \; -quit
        if [ -f "${SIP_DIR}/livekit-sip" ]; then
            echo "Found and copied a file with 'sip' in the name as livekit-sip"
        else
            echo "Error: Could not find any suitable file to use as livekit-sip"
            exit 1
        fi
    fi
fi

rm -rf ${SIP_DIR}/tmp
rm ${SIP_DIR}/livekit-sip.tar.gz
chmod +x ${SIP_DIR}/livekit-sip

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/config.yaml << EOF
api_key: ${API_KEY}
api_secret: ${API_SECRET}
ws_url: ws://localhost:7880
redis:
  address: localhost:6379
sip_port: 5060
rtp_port: 10000-20000
use_external_ip: true
logging:
  level: info
EOF

# Create systemd service
echo "Creating systemd service..."
cat > ${SIP_DIR}/livekit-sip.service << EOF
[Unit]
Description=LiveKit SIP Server
After=network.target redis-server.service livekit.service

[Service]
Type=simple
User=ubuntu
ExecStart=${SIP_DIR}/livekit-sip --config=${CONFIG_DIR}/config.yaml
Restart=always
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# Copy systemd service
echo "Copying systemd service..."
sudo cp ${SIP_DIR}/livekit-sip.service /etc/systemd/system/

# Open firewall ports for SIP
echo "Opening firewall ports for SIP..."
sudo ufw allow 5060/udp
sudo ufw allow 5060/tcp
sudo ufw allow 10000:20000/udp

# Enable and start the LiveKit SIP service
echo "Enabling and starting LiveKit SIP service..."
sudo systemctl daemon-reload
sudo systemctl enable livekit-sip
sudo systemctl start livekit-sip

# Check status
echo "Checking LiveKit SIP service status..."
sudo systemctl status livekit-sip

echo "LiveKit SIP Server installation complete!"
echo "SIP URI: ${DOMAIN}:5060"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:${DOMAIN}:5060"
echo ""
echo "To check the logs:"
echo "sudo journalctl -u livekit-sip -f"
