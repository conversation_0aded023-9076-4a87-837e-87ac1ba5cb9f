#!/bin/bash
# Script to install LiveKit on the server

# Exit on error
set -e

# Set variables
LIVEKIT_VERSION="1.8.4"  # Latest version as of May 2025
DOMAIN="voice.guestrix.ai"  # Using the existing domain
EMAIL="<EMAIL>"  # Update this to your email
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
LIVEKIT_DIR="/home/<USER>/livekit"
CONFIG_DIR="${LIVEKIT_DIR}/config"
SYSTEMD_DIR="/etc/systemd/system"
NGINX_DIR="/etc/nginx/sites-available"

echo "Setting up LiveKit Server v${LIVEKIT_VERSION} on $(hostname)"
echo "Domain: ${DOMAIN}"
echo "Email: ${EMAIL}"

# Create directories
mkdir -p ${CONFIG_DIR}

# Download LiveKit Server directly
echo "Downloading LiveKit Server..."
wget -O ${LIVEKIT_DIR}/livekit-server.tar.gz https://github.com/livekit/livekit-server/releases/download/v${LIVEKIT_VERSION}/livekit-server-v${LIVEKIT_VERSION}-linux-amd64.tar.gz
mkdir -p ${LIVEKIT_DIR}/tmp
tar -xzf ${LIVEKIT_DIR}/livekit-server.tar.gz -C ${LIVEKIT_DIR}/tmp
# Find the livekit-server binary in the extracted files
find ${LIVEKIT_DIR}/tmp -name "livekit-server" -type f -exec cp {} ${LIVEKIT_DIR}/ \;
rm -rf ${LIVEKIT_DIR}/tmp
rm ${LIVEKIT_DIR}/livekit-server.tar.gz
chmod +x ${LIVEKIT_DIR}/livekit-server

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: true
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
  json: true
sip:
  enabled: true
  domain: ${DOMAIN}
  inbound:
    enabled: true
    auth:
      enabled: false
  outbound:
    enabled: true
    auth:
      enabled: false
