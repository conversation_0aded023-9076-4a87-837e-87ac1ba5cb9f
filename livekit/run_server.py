#!/usr/bin/env python3
"""
Entry point script to run the LiveKit server.
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
load_dotenv()

# Import the server module
from livekit.server import run_server

if __name__ == "__main__":
    # Run the server
    run_server()
