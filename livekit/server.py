#!/usr/bin/env python3
"""
LiveKit server for integrating Twilio and Gemini Live.

This module provides a server for integrating Twilio SIP trunking with
Google's Gemini Live API using LiveKit.
"""

import os
import json
import logging
import asyncio
from typing import Dict, Any, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS
from twilio.twiml.voice_response import VoiceResponse

from livekit.agents import AgentSession
from livekit.plugins.google.beta.realtime import RealtimeModel

from .utils import setup_logging, load_config, log_config
from .gemini_agent import GeminiAgent
from .twilio_integration import TwilioIntegration
from .audio_processor import resample_audio

# Configure logging
logger = setup_logging()

# Load configuration
config = load_config()
log_config(config)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize Gemini agent
gemini_agent = GeminiAgent(
    api_key=config["gemini"]["api_key"],
    model=config["gemini"]["model"],
    voice=config["gemini"]["voice"],
    temperature=0.8
)

# Initialize Twilio integration
twilio_integration = TwilioIntegration(
    account_sid=config["twilio"]["account_sid"],
    auth_token=config["twilio"]["auth_token"],
    phone_number=config["twilio"]["phone_number"]
)

# Active calls
active_calls = {}

@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok"})

@app.route("/twilio/voice", methods=["POST"])
def twilio_voice_webhook():
    """
    Handle Twilio voice webhooks.

    This endpoint is called by Twilio when a call is received.
    """
    try:
        # Get request data
        request_data = request.form.to_dict()

        # Log the request
        logger.info(f"Received Twilio voice webhook: {json.dumps(request_data)}")

        # Handle the incoming call
        twiml = twilio_integration.handle_incoming_call(request_data)

        # Return the TwiML response
        return twiml

    except Exception as e:
        logger.error(f"Error handling Twilio voice webhook: {e}")

        # Return a simple TwiML response
        response = VoiceResponse()
        response.say("Sorry, there was an error processing your call.")
        response.hangup()

        return str(response)

@app.route("/livekit/sip", methods=["POST"])
def livekit_sip_webhook():
    """
    Handle LiveKit SIP webhooks.

    This endpoint is called by LiveKit when a SIP event occurs.
    """
    try:
        # Get request data
        request_data = request.json

        # Log the request
        logger.info(f"Received LiveKit SIP webhook: {json.dumps(request_data)}")

        # Handle the event
        event_type = request_data.get("type")

        if event_type == "participant_joined":
            # A participant joined the call
            participant_id = request_data.get("participant", {}).get("id")
            room_name = request_data.get("room", {}).get("name")

            logger.info(f"Participant {participant_id} joined room {room_name}")

            # Start a new Gemini agent session for this call
            asyncio.create_task(start_call_session(participant_id, room_name))

        elif event_type == "participant_left":
            # A participant left the call
            participant_id = request_data.get("participant", {}).get("id")

            logger.info(f"Participant {participant_id} left the call")

            # End the Gemini agent session for this call
            asyncio.create_task(end_call_session(participant_id))

        # Return a success response
        return jsonify({"status": "ok"})

    except Exception as e:
        logger.error(f"Error handling LiveKit SIP webhook: {e}")
        return jsonify({"status": "error", "message": str(e)}), 500

async def start_call_session(participant_id: str, room_name: str) -> None:
    """
    Start a new call session.

    Args:
        participant_id: The participant ID
        room_name: The room name
    """
    try:
        logger.info(f"Starting call session for participant {participant_id} in room {room_name}")

        # Create a new Gemini agent session
        session = await gemini_agent.create_session()

        # Store the session
        active_calls[participant_id] = {
            "session": session,
            "room_name": room_name
        }

        # Start the conversation
        await gemini_agent.start_conversation("Hello, how can I help you today?")

        logger.info(f"Call session started for participant {participant_id}")

    except Exception as e:
        logger.error(f"Error starting call session: {e}")

async def end_call_session(participant_id: str) -> None:
    """
    End a call session.

    Args:
        participant_id: The participant ID
    """
    try:
        logger.info(f"Ending call session for participant {participant_id}")

        # Get the session
        call_data = active_calls.get(participant_id)

        if call_data:
            # Close the session
            await gemini_agent.close()

            # Remove the session
            del active_calls[participant_id]

            logger.info(f"Call session ended for participant {participant_id}")
        else:
            logger.warning(f"No active call session found for participant {participant_id}")

    except Exception as e:
        logger.error(f"Error ending call session: {e}")

async def process_audio(participant_id: str, audio_data: bytes) -> None:
    """
    Process audio data from a participant.

    Args:
        participant_id: The participant ID
        audio_data: The audio data
    """
    try:
        # Get the call data
        call_data = active_calls.get(participant_id)

        if not call_data:
            logger.warning(f"No active call session found for participant {participant_id}")
            return

        # Resample the audio if needed
        resampled_audio = resample_audio(
            audio_data,
            config["twilio"]["sample_rate"],
            config["gemini"]["sample_rate"]
        )

        # Send the audio to Gemini
        await gemini_agent.send_audio(resampled_audio)

        # Receive audio from Gemini
        response_audio = await gemini_agent.receive_audio()

        if response_audio:
            # Resample the response audio if needed
            resampled_response = resample_audio(
                response_audio,
                config["gemini"]["sample_rate"],
                config["twilio"]["sample_rate"]
            )

            # Send the response audio back to the participant
            # This would be handled by LiveKit's SIP integration
            pass

    except Exception as e:
        logger.error(f"Error processing audio: {e}")

def run_server():
    """Run the server."""
    host = config["server"]["host"]
    port = config["server"]["port"]

    logger.info(f"Starting server on {host}:{port}")
    app.run(host=host, port=port, debug=False)

if __name__ == "__main__":
    run_server()
