#!/bin/bash
# Script to set up LiveKit Server on an EC2 instance

# Exit on error
set -e

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo"
  exit 1
fi

# Set variables
LIVEKIT_VERSION="1.5.3"  # Update this to the latest version
DOMAIN="voice.guestrix.ai"  # Update this to your domain
EMAIL="<EMAIL>"  # Update this to your email
API_KEY="guestrix_key"  # Update this or generate a random key
API_SECRET="guestrix_secret"  # Update this or generate a random secret
CONFIG_DIR="/etc/livekit"
SYSTEMD_DIR="/etc/systemd/system"

echo "Setting up LiveKit Server v${LIVEKIT_VERSION} on $(hostname)"
echo "Domain: ${DOMAIN}"
echo "Email: ${EMAIL}"

# Install dependencies
echo "Installing dependencies..."
apt-get update
apt-get install -y wget curl jq certbot nginx

# Create directories
mkdir -p ${CONFIG_DIR}

# Download LiveKit Server
echo "Downloading LiveKit Server..."
wget -O /usr/local/bin/livekit-server https://github.com/livekit/livekit/releases/download/v${LIVEKIT_VERSION}/livekit-server-linux-amd64
chmod +x /usr/local/bin/livekit-server

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: true
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
  json: true
redis:
  address: localhost:6379
  db: 0
sip:
  enabled: true
  domain: ${DOMAIN}
  inbound:
    enabled: true
    auth:
      enabled: false
  outbound:
    enabled: true
    auth:
      enabled: false
EOF

# Create systemd service
echo "Creating systemd service..."
cat > ${SYSTEMD_DIR}/livekit.service << EOF
[Unit]
Description=LiveKit Server
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/livekit-server --config ${CONFIG_DIR}/livekit.yaml
Restart=always
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# Set up Nginx as a reverse proxy
echo "Setting up Nginx as a reverse proxy..."
cat > /etc/nginx/sites-available/livekit << EOF
server {
    listen 80;
    server_name ${DOMAIN};

    location / {
        proxy_pass http://localhost:7880;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/livekit /etc/nginx/sites-enabled/

# Test Nginx configuration
nginx -t

# Restart Nginx
systemctl restart nginx

# Set up SSL with Let's Encrypt
echo "Setting up SSL with Let's Encrypt..."
certbot --nginx -d ${DOMAIN} --non-interactive --agree-tos -m ${EMAIL}

# Enable and start the service
echo "Enabling and starting LiveKit service..."
systemctl daemon-reload
systemctl enable livekit
systemctl start livekit

# Check status
echo "Checking LiveKit service status..."
systemctl status livekit

echo "LiveKit Server setup complete!"
echo "API Key: ${API_KEY}"
echo "API Secret: ${API_SECRET}"
echo "SIP URI: ${DOMAIN}"
echo ""
echo "To configure your Twilio SIP trunk, use:"
echo "sip:${DOMAIN}"
echo ""
echo "To check the logs:"
echo "journalctl -u livekit -f"
