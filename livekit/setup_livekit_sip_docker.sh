#!/bin/bash
# Script to set up LiveKit SIP using Docker

# Exit on error
set -e

# Set variables
API_KEY="guestrix_key"
API_SECRET="guestrix_secret"
LIVEKIT_DIR="./livekit/docker"
CONFIG_DIR="${LIVEKIT_DIR}/config"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Setting up LiveKit SIP using Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install it first."
    print_message "You can install it from https://docs.docker.com/get-docker/"
    exit 1
fi

# Create directories
print_message "Creating directories..."
mkdir -p ${LIVEKIT_DIR}
mkdir -p ${CONFIG_DIR}

# Create configuration file for LiveKit
print_message "Creating LiveKit configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: false
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
EOF

# Create configuration file for LiveKit SIP
print_message "Creating LiveKit SIP configuration file..."
cat > ${CONFIG_DIR}/sip-config.yaml << EOF
api_key: ${API_KEY}
api_secret: ${API_SECRET}
ws_url: ws://livekit:7880
redis:
  address: redis:6379
sip_port: 5060
rtp_port: 10000-20000
use_external_ip: false
logging:
  level: info
EOF

# Create docker-compose.yml
print_message "Creating docker-compose.yml..."
cat > ${LIVEKIT_DIR}/docker-compose.yml << EOF
version: '3'

services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  livekit:
    image: livekit/livekit-server
    ports:
      - "7880:7880"
      - "7881:7881/tcp"
      - "7882:7882/udp"
      - "50000-60000:50000-60000/udp"
    volumes:
      - ./config/livekit.yaml:/livekit.yaml
    command: --config /livekit.yaml
    restart: unless-stopped

  livekit-sip:
    image: livekit/sip
    ports:
      - "5060:5060/udp"
      - "5060:5060/tcp"
      - "10000-20000:10000-20000/udp"
    volumes:
      - ./config/sip-config.yaml:/config.yaml
    command: --config /config.yaml
    depends_on:
      - redis
      - livekit
    restart: unless-stopped
EOF

print_message "LiveKit SIP Docker setup completed."
print_message ""
print_message "To start the services, run:"
print_message "cd ${LIVEKIT_DIR} && docker-compose up -d"
print_message ""
print_message "To stop the services, run:"
print_message "cd ${LIVEKIT_DIR} && docker-compose down"
print_message ""
print_message "To view logs, run:"
print_message "cd ${LIVEKIT_DIR} && docker-compose logs -f"
