#!/bin/bash
# Script to set up LiveKit SIP locally for testing

# Exit on error
set -e

# Set variables
API_KEY="guestrix_key"
API_SECRET="guestrix_secret"
LIVEKIT_DIR="./livekit/local"
SIP_DIR="${LIVEKIT_DIR}/sip"
CONFIG_DIR="${LIVEKIT_DIR}/config"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Setting up LiveKit SIP locally for testing..."

# Create directories
print_message "Creating directories..."
mkdir -p ${LIVEKIT_DIR}
mkdir -p ${SIP_DIR}
mkdir -p ${CONFIG_DIR}

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed. Please install it first."
    print_message "You can install it from https://golang.org/dl/"
    exit 1
fi

# Check if Redis is installed
if ! command -v redis-server &> /dev/null; then
    print_warning "Redis is not installed. LiveKit SIP requires Redis."
    print_message "You can install it using: brew install redis"
    print_message "Continuing with setup..."
fi

# Clone or update the LiveKit SIP repository
print_message "Cloning or updating LiveKit SIP repository..."
if [ -d "${SIP_DIR}/repo" ]; then
    print_message "Repository already exists, updating..."
    cd ${SIP_DIR}/repo
    git fetch
    git reset --hard origin/main
else
    print_message "Cloning repository..."
    git clone https://github.com/livekit/sip.git ${SIP_DIR}/repo
fi

# Build the LiveKit SIP Server
print_message "Building LiveKit SIP Server..."
cd ${SIP_DIR}/repo
go build -o ${SIP_DIR}/livekit-sip ./cmd/livekit-sip

# Make the binary executable
chmod +x ${SIP_DIR}/livekit-sip

# Create configuration file
print_message "Creating configuration file..."
cat > ${CONFIG_DIR}/config.yaml << EOF
api_key: ${API_KEY}
api_secret: ${API_SECRET}
ws_url: ws://localhost:7880
redis:
  address: localhost:6379
sip_port: 5060
rtp_port: 10000-20000
use_external_ip: false
logging:
  level: info
EOF

print_message "Creating LiveKit configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
  use_external_ip: false
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
EOF

print_message "LiveKit SIP setup completed locally."
print_message ""
print_message "To start LiveKit server (if installed):"
print_message "livekit-server --config=${CONFIG_DIR}/livekit.yaml"
print_message ""
print_message "To start LiveKit SIP server:"
print_message "${SIP_DIR}/livekit-sip --config=${CONFIG_DIR}/config.yaml"
print_message ""
print_message "Note: You need to have Redis running for LiveKit SIP to work."
print_message "You can start Redis using: redis-server"
