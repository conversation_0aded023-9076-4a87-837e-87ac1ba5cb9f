#!/bin/bash
# Script to set up LiveKit Server locally for testing

# Exit on error
set -e

# Set variables
LIVEKIT_VERSION="1.5.3"  # Update this to the latest version
API_KEY="test_key"
API_SECRET="test_secret"
CONFIG_DIR="./livekit_config"
DOWNLOAD_DIR="./livekit_download"

echo "Setting up LiveKit Server v${LIVEKIT_VERSION} locally"

# Create directories
mkdir -p ${CONFIG_DIR}
mkdir -p ${DOWNLOAD_DIR}

# Detect OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    echo "Detected macOS"
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        echo "Homebrew not found. Please install Homebrew first:"
        echo "/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    
    # Install LiveKit Server using Homebrew
    echo "Installing LiveKit Server using Homebrew..."
    brew install livekit/livekit/livekit-server
    
    # Get the installed version
    LIVEKIT_VERSION=$(livekit-server --version | awk '{print $3}')
    echo "Installed LiveKit Server version: ${LIVEKIT_VERSION}"
    
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    echo "Detected Linux"
    
    # Download LiveKit Server
    echo "Downloading LiveKit Server..."
    wget -O ${DOWNLOAD_DIR}/livekit-server https://github.com/livekit/livekit/releases/download/v${LIVEKIT_VERSION}/livekit-server-linux-amd64
    chmod +x ${DOWNLOAD_DIR}/livekit-server
    
    echo "LiveKit Server downloaded to ${DOWNLOAD_DIR}/livekit-server"
    
else
    echo "Unsupported OS: $OSTYPE"
    echo "Please download LiveKit Server manually from:"
    echo "https://github.com/livekit/livekit/releases"
    exit 1
fi

# Create configuration file
echo "Creating configuration file..."
cat > ${CONFIG_DIR}/livekit.yaml << EOF
port: 7880
rtc:
  tcp_port: 7881
  port_range_start: 50000
  port_range_end: 60000
keys:
  ${API_KEY}: ${API_SECRET}
logging:
  level: info
redis:
  address: localhost:6379
  db: 0
sip:
  enabled: true
  domain: localhost
  inbound:
    enabled: true
    auth:
      enabled: false
  outbound:
    enabled: true
    auth:
      enabled: false
EOF

echo "LiveKit Server configuration created at ${CONFIG_DIR}/livekit.yaml"

# Create a script to run LiveKit Server
cat > run_livekit.sh << EOF
#!/bin/bash
# Script to run LiveKit Server locally

if [[ "\$OSTYPE" == "darwin"* ]]; then
    # macOS
    livekit-server --config ${CONFIG_DIR}/livekit.yaml
else
    # Linux
    ${DOWNLOAD_DIR}/livekit-server --config ${CONFIG_DIR}/livekit.yaml
fi
EOF

chmod +x run_livekit.sh

echo "LiveKit Server setup complete!"
echo "API Key: ${API_KEY}"
echo "API Secret: ${API_SECRET}"
echo ""
echo "To start LiveKit Server, run:"
echo "./run_livekit.sh"
echo ""
echo "For local testing, you'll need to expose your local server to the internet."
echo "You can use ngrok:"
echo "ngrok http 7880"
echo ""
echo "Then use the ngrok URL as your LiveKit SIP URI."
