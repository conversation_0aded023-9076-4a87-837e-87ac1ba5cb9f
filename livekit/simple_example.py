#!/usr/bin/env python3
"""
Simple example of integrating Twilio and Gemini Live.

This script demonstrates a simplified version of the integration between
Twilio and Gemini Live without relying on the LiveKit Agents package.
"""

import os
import asyncio
import logging
import argparse
from dotenv import load_dotenv
import google.generativeai as genai
from flask import Flask, request
from twilio.twiml.voice_response import VoiceResponse, Dial

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("simple-example")

# Initialize Flask app
app = Flask(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Simple Twilio and Gemini Live integration")
    parser.add_argument("--host", default="0.0.0.0", help="Host to run the server on")
    parser.add_argument("--port", type=int, default=5000, help="Port to run the server on")
    return parser.parse_args()

@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

@app.route("/twilio/voice", methods=["POST"])
def twilio_voice_webhook():
    """
    Handle Twilio voice webhooks.
    
    This endpoint is called by Twilio when a call is received.
    """
    try:
        # Get request data
        request_data = request.form.to_dict()
        
        # Log the request
        logger.info(f"Received Twilio voice webhook: {request_data}")
        
        # Create a TwiML response
        response = VoiceResponse()
        
        # Add a message
        response.say("Hello! This is a simple example of integrating Twilio and Gemini Live.")
        
        # Add a pause
        response.pause(length=1)
        
        # Add another message
        response.say("I'll now demonstrate how to use Gemini to generate a response.")
        
        # Generate a response from Gemini
        gemini_response = generate_gemini_response("Tell me a short joke")
        
        # Add the Gemini response
        response.say(gemini_response)
        
        # Add a goodbye message
        response.say("Thank you for calling. Goodbye!")
        
        # Hang up
        response.hangup()
        
        # Return the TwiML response
        return str(response)
    
    except Exception as e:
        logger.error(f"Error handling Twilio voice webhook: {e}")
        
        # Return a simple TwiML response
        response = VoiceResponse()
        response.say("Sorry, there was an error processing your call.")
        response.hangup()
        
        return str(response)

def generate_gemini_response(prompt):
    """
    Generate a response from Gemini.
    
    Args:
        prompt: The prompt to send to Gemini
        
    Returns:
        The generated response
    """
    try:
        # Configure Gemini
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.error("GEMINI_API_KEY not found in environment variables")
            return "I'm sorry, I couldn't generate a response at this time."
        
        # Configure the Gemini client
        genai.configure(api_key=api_key)
        
        # Create a Gemini model
        model = genai.GenerativeModel("gemini-1.5-pro")
        
        # Generate a response
        response = model.generate_content(prompt)
        
        # Return the response text
        return response.text
    
    except Exception as e:
        logger.error(f"Error generating Gemini response: {e}")
        return "I'm sorry, I couldn't generate a response at this time."

def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    # Parse arguments
    args = parse_arguments()
    
    # Get API key from environment
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("GEMINI_API_KEY not found in environment variables")
        return
    
    # Log configuration
    logger.info(f"Starting server on {args.host}:{args.port}")
    logger.info(f"Gemini API key: {api_key[:4]}...{api_key[-4:]}")
    
    # Run the Flask app
    app.run(host=args.host, port=args.port)

if __name__ == "__main__":
    main()
