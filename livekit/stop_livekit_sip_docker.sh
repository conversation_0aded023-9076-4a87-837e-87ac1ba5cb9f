#!/bin/bash
# Script to stop LiveKit SIP Docker containers

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Stopping LiveKit SIP Docker containers..."

# Check if docker-compose.yml exists
if [ ! -f "./docker/docker-compose.yml" ]; then
    print_error "docker-compose.yml not found in ./docker directory."
    print_message "Please run this script from the livekit directory."
    exit 1
fi

# Stop the Docker containers
print_message "Stopping Docker containers..."
cd ./docker && docker-compose down

print_message "LiveKit SIP Docker containers stopped."
