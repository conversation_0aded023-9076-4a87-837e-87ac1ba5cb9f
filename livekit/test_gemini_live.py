#!/usr/bin/env python3
"""
Test the Gemini Live API directly.

This script tests the Gemini Live API directly without relying on the LiveKit Agents package.
It uses the Google Generative AI SDK to interact with the Gemini Live API.
"""

import os
import asyncio
import logging
import argparse
from dotenv import load_dotenv
import google.generativeai as genai
from google.generativeai import types

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("test-gemini-live")

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test the Gemini Live API")
    parser.add_argument("--model", default="gemini-2.0-flash-live-001", help="Gemini model to use")
    parser.add_argument("--voice", default="Puck", help="Voice to use")
    parser.add_argument("--output-dir", default="output", help="Directory to save output files")
    return parser.parse_args()

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()
    
    # Parse arguments
    args = parse_arguments()
    
    # Get API key from environment
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("GEMINI_API_KEY not found in environment variables")
        return
    
    # Create output directory if it doesn't exist
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Configure the Gemini client
    genai.configure(api_key=api_key, http_options={"api_version": "v1alpha"})
    
    # Create a client
    client = genai.Client(api_key=api_key, http_options={"api_version": "v1alpha"})
    
    # Configure response modalities (audio)
    config = types.LiveConnectConfig(
        response_modalities=["audio"],
        speech_config=types.SpeechConfig(
            voice_config=types.VoiceConfig(
                prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name=args.voice)
            )
        )
    )
    
    # Create a system instruction
    system_instruction = """
    You are a helpful AI assistant speaking with a caller on the phone.
    Be concise, conversational, and helpful.
    Respond to the caller's questions and requests in a natural way.
    If you don't know the answer to a question, say so.
    """
    
    logger.info(f"Connecting to Gemini Live API with model {args.model}")
    logger.info(f"Using voice: {args.voice}")
    
    try:
        # Connect to the Gemini Live API
        session = await client.aio.live.connect(
            config={
                "model": f"models/{args.model}",
                "system_instruction": {
                    "parts": [{"text": system_instruction}]
                },
                "speech_config": {
                    "voice_config": {
                        "prebuilt_voice_config": {
                            "voice_name": args.voice
                        }
                    }
                },
                "response_modalities": ["audio", "text"]
            }
        )
        
        logger.info("Connected to Gemini Live API")
        
        # Send an initial message
        logger.info("Sending initial message")
        await session.send_message({"text": "Hello, I'm testing the Gemini Live API. Can you tell me a short joke?"})
        
        # Receive the response
        logger.info("Waiting for response...")
        response = await session.get_response()
        
        # Process the response
        if response:
            # Check for text
            text = None
            for part in response.parts:
                if hasattr(part, "text") and part.text:
                    text = part.text
                    break
            
            if text:
                logger.info(f"Received text response: {text}")
                
                # Save text to file
                text_file = os.path.join(args.output_dir, "response.txt")
                with open(text_file, "w") as f:
                    f.write(text)
                logger.info(f"Saved text response to {text_file}")
            
            # Check for audio
            audio = None
            for part in response.parts:
                if hasattr(part, "audio") and part.audio:
                    audio = part.audio
                    break
            
            if audio:
                logger.info(f"Received {len(audio)} bytes of audio")
                
                # Save audio to file
                audio_file = os.path.join(args.output_dir, "response.wav")
                with open(audio_file, "wb") as f:
                    f.write(audio)
                logger.info(f"Saved audio response to {audio_file}")
        
        # Send another message
        logger.info("Sending follow-up message")
        await session.send_message({"text": "Thank you! Can you tell me about LiveKit?"})
        
        # Receive the response
        logger.info("Waiting for response...")
        response = await session.get_response()
        
        # Process the response
        if response:
            # Check for text
            text = None
            for part in response.parts:
                if hasattr(part, "text") and part.text:
                    text = part.text
                    break
            
            if text:
                logger.info(f"Received text response: {text}")
                
                # Save text to file
                text_file = os.path.join(args.output_dir, "response2.txt")
                with open(text_file, "w") as f:
                    f.write(text)
                logger.info(f"Saved text response to {text_file}")
            
            # Check for audio
            audio = None
            for part in response.parts:
                if hasattr(part, "audio") and part.audio:
                    audio = part.audio
                    break
            
            if audio:
                logger.info(f"Received {len(audio)} bytes of audio")
                
                # Save audio to file
                audio_file = os.path.join(args.output_dir, "response2.wav")
                with open(audio_file, "wb") as f:
                    f.write(audio)
                logger.info(f"Saved audio response to {audio_file}")
        
        # Close the session
        logger.info("Closing session")
        await session.close()
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    logger.info("Test completed")

if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
