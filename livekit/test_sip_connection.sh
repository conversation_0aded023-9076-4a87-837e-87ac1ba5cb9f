#!/bin/bash
# Script to test SIP connection to LiveKit SIP Server

# Set variables
SIP_SERVER="127.0.0.1:8080"
SIP_USER="test"
SIP_DOMAIN="10a0-75-194-21-68.ngrok-free.app"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Testing SIP connection to $SIP_SERVER..."

# Check if SIPp is installed
if ! command -v sipp &> /dev/null; then
    print_warning "SIPp is not installed. It's recommended for SIP testing."
    print_message "You can install it using: brew install sipp"
    print_message "Continuing with basic checks..."
fi

# Check if the SIP port is open
print_message "Checking if SIP port is open..."
HOST=$(echo $SIP_SERVER | cut -d':' -f1)
PORT=$(echo $SIP_SERVER | cut -d':' -f2)
nc -z -v -u $HOST $PORT 2>&1

# Check Docker container status
print_message "Checking Docker container status..."
docker-compose ps

# Check LiveKit SIP logs
print_message "Checking LiveKit SIP logs..."
docker-compose logs --tail=20 livekit-sip

print_message "SIP connection test completed."
print_message ""
print_message "To configure your Twilio SIP trunk, use:"
print_message "sip:$SIP_DOMAIN:5060"
print_message ""
print_message "For Twilio SIP trunk configuration:"
print_message "1. Go to https://console.twilio.com/"
print_message "2. Navigate to Voice > SIP Domains"
print_message "3. Create a new SIP Domain or edit the existing 'guestrix' domain"
print_message "4. Set the 'Request URL' to your LiveKit SIP server: sip:$SIP_DOMAIN:5060"
print_message "5. Save the configuration"
print_message ""
print_message "Note: For production use, you'll need to ensure your SIP server is accessible from the internet"
print_message "and has proper DNS configuration for the domain $SIP_DOMAIN."
