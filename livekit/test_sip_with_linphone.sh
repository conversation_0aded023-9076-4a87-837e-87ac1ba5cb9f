#!/bin/bash
# Script to test SIP connection using Linphone

# Set variables
SIP_SERVER="127.0.0.1:8080"
SIP_USER="test"
SIP_DOMAIN="localhost"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Testing SIP connection using Linphone..."

# Check if Linphone is installed
if [ ! -d "/Applications/Linphone.app" ]; then
    print_error "Linphone is not installed. Please install it first."
    print_message "You can install it using: brew install --cask linphone"
    exit 1
fi

# Check if the SIP port is open
print_message "Checking if SIP port is open..."
HOST=$(echo $SIP_SERVER | cut -d':' -f1)
PORT=$(echo $SIP_SERVER | cut -d':' -f2)
nc -z -v -u $HOST $PORT 2>&1

# Check Docker container status
print_message "Checking Docker container status..."
docker-compose ps

# Check LiveKit SIP logs
print_message "Checking LiveKit SIP logs..."
docker-compose logs --tail=20 livekit-sip

print_message "SIP connection test completed."
print_message ""
print_message "To configure Linphone:"
print_message "1. Open Linphone"
print_message "2. Go to Preferences > Accounts"
print_message "3. Add a new account with the following settings:"
print_message "   - SIP Address: sip:$SIP_USER@$SIP_DOMAIN"
print_message "   - Transport: UDP"
print_message "   - Server Address: $SIP_SERVER"
print_message "4. Save the configuration"
print_message "5. Try making a call to sip:test@$SIP_DOMAIN"
print_message ""
print_message "Note: For production use, you'll need to deploy to EC2 and configure Twilio to use the EC2 domain."
