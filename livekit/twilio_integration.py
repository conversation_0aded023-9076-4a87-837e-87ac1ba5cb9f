#!/usr/bin/env python3
"""
Twilio SIP trunk integration for LiveKit.

This module provides classes and functions for integrating Twilio SIP trunking
with LiveKit for telephony integration.
"""

import os
import logging
from typing import Dict, Any, Optional, List, Callable
from twilio.rest import Client
from twilio.twiml.voice_response import VoiceResponse, Dial

from .utils import mask_api_key

# Configure logging
logger = logging.getLogger("livekit.twilio")

class TwilioIntegration:
    """
    Twilio SIP trunk integration for LiveKit.
    
    This class provides methods for configuring and managing a Twilio SIP trunk
    for use with LiveKit.
    """
    
    def __init__(
        self,
        account_sid: str,
        auth_token: str,
        phone_number: Optional[str] = None,
        livekit_sip_uri: Optional[str] = None
    ):
        """
        Initialize the Twilio integration.
        
        Args:
            account_sid: The Twilio account SID
            auth_token: The Twilio auth token
            phone_number: The Twilio phone number to use
            livekit_sip_uri: The LiveKit SIP URI to use
        """
        self.account_sid = account_sid
        self.auth_token = auth_token
        self.phone_number = phone_number
        self.livekit_sip_uri = livekit_sip_uri
        
        # Initialize Twilio client
        self.client = Client(account_sid, auth_token)
        
        logger.info(f"Initialized Twilio integration with account SID {mask_api_key(account_sid)}")
        if phone_number:
            logger.info(f"Using phone number {phone_number}")
        if livekit_sip_uri:
            logger.info(f"Using LiveKit SIP URI {livekit_sip_uri}")
    
    def create_sip_trunk(self, friendly_name: str) -> Dict[str, Any]:
        """
        Create a new SIP trunk.
        
        Args:
            friendly_name: A friendly name for the trunk
            
        Returns:
            The created trunk
        """
        try:
            logger.info(f"Creating SIP trunk with name {friendly_name}")
            
            # Create a domain name based on the friendly name
            domain_name = f"{friendly_name.lower().replace(' ', '-')}.pstn.twilio.com"
            
            # Create the trunk
            trunk = self.client.trunking.v1.trunks.create(
                friendly_name=friendly_name,
                domain_name=domain_name
            )
            
            logger.info(f"Created SIP trunk with SID {trunk.sid}")
            return {
                "sid": trunk.sid,
                "friendly_name": trunk.friendly_name,
                "domain_name": trunk.domain_name
            }
        
        except Exception as e:
            logger.error(f"Error creating SIP trunk: {e}")
            raise
    
    def configure_inbound_trunk(self, trunk_sid: str) -> Dict[str, Any]:
        """
        Configure a trunk for inbound calls.
        
        Args:
            trunk_sid: The SID of the trunk to configure
            
        Returns:
            The configured origination URL
        """
        if not self.livekit_sip_uri:
            raise ValueError("LiveKit SIP URI not set")
        
        try:
            logger.info(f"Configuring trunk {trunk_sid} for inbound calls")
            
            # Create the origination URL
            origination_url = self.client.trunking.v1.trunks(trunk_sid).origination_urls.create(
                friendly_name="LiveKit SIP URI",
                sip_url=f"sip:{self.livekit_sip_uri}",
                priority=1,
                weight=1,
                enabled=True
            )
            
            logger.info(f"Configured origination URL with SID {origination_url.sid}")
            return {
                "sid": origination_url.sid,
                "friendly_name": origination_url.friendly_name,
                "sip_url": origination_url.sip_url
            }
        
        except Exception as e:
            logger.error(f"Error configuring inbound trunk: {e}")
            raise
    
    def associate_phone_number(self, trunk_sid: str, phone_number_sid: Optional[str] = None) -> Dict[str, Any]:
        """
        Associate a phone number with a trunk.
        
        Args:
            trunk_sid: The SID of the trunk
            phone_number_sid: The SID of the phone number to associate
            
        Returns:
            The associated phone number
        """
        try:
            # If no phone number SID is provided, try to find one
            if not phone_number_sid and self.phone_number:
                logger.info(f"Looking up phone number {self.phone_number}")
                
                # List incoming phone numbers
                incoming_phone_numbers = self.client.incoming_phone_numbers.list(
                    phone_number=self.phone_number
                )
                
                if incoming_phone_numbers:
                    phone_number_sid = incoming_phone_numbers[0].sid
                    logger.info(f"Found phone number with SID {phone_number_sid}")
                else:
                    logger.error(f"Phone number {self.phone_number} not found")
                    raise ValueError(f"Phone number {self.phone_number} not found")
            
            if not phone_number_sid:
                raise ValueError("Phone number SID not provided")
            
            logger.info(f"Associating phone number {phone_number_sid} with trunk {trunk_sid}")
            
            # Associate the phone number with the trunk
            phone_number = self.client.trunking.v1.trunks(trunk_sid).phone_numbers.create(
                phone_number_sid=phone_number_sid
            )
            
            logger.info(f"Associated phone number with SID {phone_number.sid}")
            return {
                "sid": phone_number.sid,
                "phone_number_sid": phone_number.phone_number_sid
            }
        
        except Exception as e:
            logger.error(f"Error associating phone number: {e}")
            raise
    
    def generate_twiml_for_livekit(self, livekit_room_name: str) -> str:
        """
        Generate TwiML for connecting to a LiveKit room.
        
        Args:
            livekit_room_name: The name of the LiveKit room to connect to
            
        Returns:
            The generated TwiML
        """
        try:
            logger.info(f"Generating TwiML for LiveKit room {livekit_room_name}")
            
            # Create a new TwiML response
            response = VoiceResponse()
            
            # Add a Dial verb with a SIP endpoint
            dial = Dial()
            
            # Add the SIP endpoint
            if self.livekit_sip_uri:
                dial.sip(f"sip:{self.livekit_sip_uri}?room={livekit_room_name}")
            
            # Add the Dial verb to the response
            response.append(dial)
            
            # Convert to string
            twiml = str(response)
            
            logger.info(f"Generated TwiML: {twiml}")
            return twiml
        
        except Exception as e:
            logger.error(f"Error generating TwiML: {e}")
            raise
    
    def handle_incoming_call(self, request_data: Dict[str, Any]) -> str:
        """
        Handle an incoming call webhook from Twilio.
        
        Args:
            request_data: The webhook request data
            
        Returns:
            The TwiML response
        """
        try:
            # Get the caller's phone number
            caller = request_data.get("From", "unknown")
            called = request_data.get("To", "unknown")
            
            logger.info(f"Handling incoming call from {caller} to {called}")
            
            # Create a room name based on the caller's phone number
            room_name = f"call-{caller.replace('+', '')}"
            
            # Generate TwiML for connecting to LiveKit
            twiml = self.generate_twiml_for_livekit(room_name)
            
            return twiml
        
        except Exception as e:
            logger.error(f"Error handling incoming call: {e}")
            
            # Return a simple TwiML response
            response = VoiceResponse()
            response.say("Sorry, there was an error processing your call.")
            response.hangup()
            
            return str(response)
