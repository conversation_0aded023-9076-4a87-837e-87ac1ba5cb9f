# LiveKit + Twilio + Gemini Live Integration

This project implements a local prototype for integrating Twilio phone calls with Google's Gemini Live API using LiveKit. It allows callers to have real-time voice conversations with Gemini AI through a phone call.

## How It Works

1. LiveKit provides the infrastructure for real-time audio streaming
2. Twilio SIP trunking routes phone calls to LiveKit
3. Gemini Live API processes the audio and generates responses
4. The Flask server coordinates the communication between these components

## Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Caller     │────▶│  Twilio     │────▶│  LiveKit    │
│  (Phone)    │◀────│  SIP Trunk  │◀────│  SIP Server │
└─────────────┘     └─────────────┘     └──────┬──────┘
                                               │
                                               │
                                        ┌──────▼──────┐
                                        │  Flask      │
                                        │  Server     │
                                        └──────┬──────┘
                                               │
                                               │
                                        ┌──────▼──────┐
                                        │  Gemini     │
                                        │  Live API   │
                                        └─────────────┘
```

## Prerequisites

- Python 3.8+
- Docker and Docker Compose
- Twilio account with a phone number
- Google Gemini API key
- ngrok (for exposing local server to the internet)

## Setup Instructions

### 1. Configure Environment Variables

Copy `.env.livekit` to the `livekit_local` directory and fill in your credentials:

```bash
cp .env.livekit livekit_local/.env.livekit
nano livekit_local/.env.livekit
```

You'll need to provide:
- <PERSON><PERSON><PERSON> Account SID and Auth Token
- Twilio Phone Number
- Gemini API Key

### 2. Set Up and Run the Server

```bash
cd livekit_local
./setup_and_run.sh
```

This script will:
1. Create a virtual environment
2. Install dependencies
3. Set up the Docker environment
4. Start the LiveKit Docker containers
5. Start the Flask server

### 3. Expose Your Server with ngrok

In a new terminal:

```bash
ngrok http 5000
```

Note the ngrok URL (e.g., `https://abc123.ngrok-free.app`).

### 4. Configure Twilio

1. Go to the [Twilio Console](https://www.twilio.com/console)
2. Navigate to your phone number
3. Under "Voice & Fax", set the webhook URL for incoming calls to:
   `https://abc123.ngrok-free.app/twilio/voice` (replace with your ngrok URL)
4. Save your changes

## Testing

1. Call your Twilio phone number
2. The call should be routed to LiveKit
3. LiveKit will connect the call to the Gemini Live API
4. You should be able to have a conversation with the AI

## Troubleshooting

### Common Issues

1. **Docker containers not starting**:
   - Check Docker logs: `cd livekit_local && docker-compose logs`
   - Make sure ports are not in use by other applications

2. **Twilio not connecting to LiveKit**:
   - Verify your ngrok URL is correct in the Twilio webhook settings
   - Check the Flask server logs for any errors

3. **Gemini Live API not responding**:
   - Verify your Gemini API key is correct
   - Check the server logs for any API errors

4. **Audio quality issues**:
   - The default configuration uses 8kHz audio for Twilio and 24kHz for Gemini
   - Adjust the sample rates in `utils.py` if needed

## Next Steps

- Implement call recording and transcription
- Add support for context retrieval from your knowledge base
- Implement more advanced audio processing
- Add support for multiple concurrent calls
