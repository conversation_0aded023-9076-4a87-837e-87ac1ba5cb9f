#!/usr/bin/env python3
"""
Audio processing utilities for the LiveKit integration.

This module provides functions for audio format conversion, resampling,
and other audio processing tasks.
"""

import io
import base64
import logging
import numpy as np
from typing import Optional, Union, Tuple
from pydub import AudioSegment

# Configure logging
logger = logging.getLogger("livekit.audio")

def resample_audio(
    audio_data: Union[bytes, np.ndarray],
    source_sample_rate: int,
    target_sample_rate: int
) -> bytes:
    """
    Resample audio data from one sample rate to another.
    
    Args:
        audio_data: The audio data to resample (bytes or numpy array)
        source_sample_rate: The source sample rate
        target_sample_rate: The target sample rate
        
    Returns:
        The resampled audio data as bytes
    """
    try:
        # Convert bytes to numpy array if needed
        if isinstance(audio_data, bytes):
            # Assuming 16-bit PCM audio
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
        else:
            audio_array = audio_data
        
        # If sample rates are the same, return the original data
        if source_sample_rate == target_sample_rate:
            if isinstance(audio_data, bytes):
                return audio_data
            else:
                return audio_array.tobytes()
        
        # Create an in-memory WAV file
        audio_segment = AudioSegment(
            data=audio_array.tobytes(),
            sample_width=2,  # 16-bit
            frame_rate=source_sample_rate,
            channels=1  # Mono
        )
        
        # Resample
        resampled_segment = audio_segment.set_frame_rate(target_sample_rate)
        
        # Get raw PCM data
        resampled_data = resampled_segment.raw_data
        
        logger.debug(f"Resampled audio from {source_sample_rate}Hz to {target_sample_rate}Hz")
        return resampled_data
    
    except Exception as e:
        logger.error(f"Error resampling audio: {e}")
        # Return original data if resampling fails
        if isinstance(audio_data, bytes):
            return audio_data
        else:
            return audio_data.tobytes()

def encode_base64_audio(audio_data: Union[bytes, np.ndarray]) -> str:
    """
    Encode audio data as base64.
    
    Args:
        audio_data: The audio data to encode (bytes or numpy array)
        
    Returns:
        The base64-encoded audio data
    """
    try:
        # Convert numpy array to bytes if needed
        if isinstance(audio_data, np.ndarray):
            audio_bytes = audio_data.tobytes()
        else:
            audio_bytes = audio_data
        
        # Encode as base64
        base64_data = base64.b64encode(audio_bytes).decode("utf-8")
        
        return base64_data
    
    except Exception as e:
        logger.error(f"Error encoding audio as base64: {e}")
        return ""

def decode_base64_audio(base64_data: str) -> bytes:
    """
    Decode base64-encoded audio data.
    
    Args:
        base64_data: The base64-encoded audio data
        
    Returns:
        The decoded audio data
    """
    try:
        # Decode base64
        audio_bytes = base64.b64decode(base64_data)
        
        return audio_bytes
    
    except Exception as e:
        logger.error(f"Error decoding base64 audio: {e}")
        return b""
