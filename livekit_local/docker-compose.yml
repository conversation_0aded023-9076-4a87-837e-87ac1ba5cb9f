version: '3'

services:
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  livekit:
    image: livekit/livekit-server
    ports:
      - "7880:7880"
      - "7881:7881/tcp"
      - "7882:7882/udp"
      - "50000-50100:50000-50100/udp"
    volumes:
      - ./config/livekit.yaml:/livekit.yaml
    command: --config /livekit.yaml
    restart: unless-stopped

  livekit-sip:
    image: livekit/sip
    ports:
      - "8080:8080/udp"
      - "8080:8080/tcp"
      - "10000-10100:10000-10100/udp"
    volumes:
      - ./config/sip-config.yaml:/config.yaml
    command: --config /config.yaml
    depends_on:
      - redis
      - livekit
    restart: unless-stopped
