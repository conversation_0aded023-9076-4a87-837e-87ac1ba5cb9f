#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the Flask server on port 5001.
"""

import os
import sys

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the server module
import server

if __name__ == "__main__":
    # Get the Flask app from the server module
    app = server.app

    # Run the app on port 5001
    app.run(host="0.0.0.0", port=5001, debug=False)
