#!/bin/bash
# Script to set up and run the LiveKit local environment

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if .env.livekit file exists or if we can use the twilio-gemini .env file
if [ ! -f ".env.livekit" ] && [ ! -f "../.env.livekit" ] && [ ! -f "../twilio-gemini/.env" ]; then
    print_warning "No .env file found. Creating .env.livekit from template..."
    cat > .env.livekit << EOF
# Twilio credentials
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Gemini API key
GEMINI_API_KEY=your_gemini_api_key

# LiveKit configuration
LIVEKIT_API_KEY=guestrix_key
LIVEKIT_API_SECRET=guestrix_secret
LIVEKIT_HOST=localhost
LIVEKIT_PORT=7880

# Server configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=5000
EOF
    print_message "Please edit the .env.livekit file with your Twilio and Gemini credentials."
    print_message "You can open it with: nano .env.livekit"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install it first."
    print_message "You can install it from https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_warning "Docker Compose is not installed. Checking if it's available via docker compose..."
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install it first."
        print_message "You can install it from https://docs.docker.com/compose/install/"
        exit 1
    else
        print_message "Docker Compose is available via 'docker compose'."
        DOCKER_COMPOSE="docker compose"
    fi
else
    DOCKER_COMPOSE="docker-compose"
fi

# Check if virtualenv is installed
if ! command -v virtualenv &> /dev/null; then
    print_message "Installing virtualenv..."
    pip3 install virtualenv
fi

# Create and activate virtual environment
print_message "Creating virtual environment..."
virtualenv venv
source venv/bin/activate

# Install dependencies
print_message "Installing dependencies..."
pip install -r requirements.txt

# Set up Docker environment
print_message "Setting up Docker environment..."
./setup_docker.sh

# Start Docker containers
print_message "Starting Docker containers..."
cd .
$DOCKER_COMPOSE up -d

# Wait for containers to start
print_message "Waiting for containers to start..."
sleep 5

# Check if containers are running
if ! $DOCKER_COMPOSE ps | grep -q "Up"; then
    print_error "Docker containers failed to start. Please check the logs with: docker-compose logs"
    exit 1
fi

# Start the server
print_message "Starting the server..."
print_message "Open a new terminal and run: ngrok http 5000"
print_message "Then update your Twilio phone number's voice webhook URL with the ngrok URL + /twilio/voice"
print_message ""
print_message "Press Ctrl+C to stop the server."
python server.py
