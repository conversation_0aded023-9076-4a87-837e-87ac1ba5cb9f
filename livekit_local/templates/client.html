<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Twilio Client</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        .call-controls {
            margin-top: 20px;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .status-connecting {
            background-color: #fff3cd;
        }
        .status-connected {
            background-color: #d1e7dd;
        }
        .status-disconnected {
            background-color: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Twilio Client</h1>
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Call Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="status status-disconnected" id="status">
                            Disconnected
                        </div>
                        <div class="call-controls">
                            <button id="call-button" class="btn btn-primary">Make Call</button>
                            <button id="hangup-button" class="btn btn-danger" disabled>Hang Up</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Call Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="log" class="log" style="height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://sdk.twilio.com/js/client/releases/1.14.0/twilio.js"></script>
    <script>
        // Log messages to the console and the page
        function log(message) {
            console.log(message);
            const logDiv = document.getElementById('log');
            const messageElement = document.createElement('div');
            messageElement.textContent = message;
            logDiv.appendChild(messageElement);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Update the status display
        function updateStatus(status) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = status;
            
            // Remove all status classes
            statusElement.classList.remove('status-connecting', 'status-connected', 'status-disconnected');
            
            // Add the appropriate class
            if (status.includes('Connecting')) {
                statusElement.classList.add('status-connecting');
            } else if (status.includes('Connected')) {
                statusElement.classList.add('status-connected');
            } else {
                statusElement.classList.add('status-disconnected');
            }
        }

        // Initialize the Twilio Client
        let device;
        let call;

        // Fetch the token from the server
        async function setupClient() {
            try {
                log('Fetching token...');
                const response = await fetch('/twilio/client');
                const data = await response.json();
                
                if (data.error) {
                    log(`Error: ${data.error}`);
                    return;
                }
                
                log('Token received');
                
                // Set up the device with the token
                device = new Twilio.Device(data.token, {
                    codecPreferences: ['opus', 'pcmu'],
                    fakeLocalDTMF: true,
                    enableRingingState: true
                });
                
                // Device ready
                device.on('ready', function() {
                    log('Twilio Client ready');
                    updateStatus('Ready');
                    document.getElementById('call-button').disabled = false;
                });
                
                // Device error
                device.on('error', function(error) {
                    log(`Device error: ${error.message}`);
                    updateStatus(`Error: ${error.message}`);
                });
                
                // Incoming call
                device.on('incoming', function(incomingCall) {
                    log('Incoming call');
                    call = incomingCall;
                    
                    // Auto-accept the call
                    call.accept();
                    
                    setupCallEvents();
                });
                
                // Register the device
                await device.register();
                
            } catch (error) {
                log(`Error setting up client: ${error.message}`);
                updateStatus(`Error: ${error.message}`);
            }
        }

        // Set up event handlers for the call
        function setupCallEvents() {
            // Call accepted
            call.on('accept', function() {
                log('Call accepted');
                updateStatus('Connected');
                document.getElementById('call-button').disabled = true;
                document.getElementById('hangup-button').disabled = false;
            });
            
            // Call disconnected
            call.on('disconnect', function() {
                log('Call disconnected');
                updateStatus('Disconnected');
                document.getElementById('call-button').disabled = false;
                document.getElementById('hangup-button').disabled = true;
                call = null;
            });
            
            // Call error
            call.on('error', function(error) {
                log(`Call error: ${error.message}`);
                updateStatus(`Error: ${error.message}`);
            });
        }

        // Make a call
        function makeCall() {
            if (!device) {
                log('Device not initialized');
                return;
            }
            
            log('Making call...');
            updateStatus('Connecting...');
            
            // Make the call
            call = device.connect();
            
            // Set up call events
            setupCallEvents();
        }

        // Hang up the call
        function hangUp() {
            if (call) {
                log('Hanging up...');
                call.disconnect();
            }
        }

        // Set up event listeners
        document.getElementById('call-button').addEventListener('click', makeCall);
        document.getElementById('hangup-button').addEventListener('click', hangUp);

        // Initialize the client when the page loads
        window.addEventListener('load', setupClient);
    </script>
</body>
</html>
