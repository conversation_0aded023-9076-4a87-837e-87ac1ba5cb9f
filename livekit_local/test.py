#!/usr/bin/env python3
"""
Test Flask server.
"""

import os
from flask import Flask, render_template, jsonify, request

# Use relative path for templates
script_dir = os.path.dirname(os.path.abspath(__file__))
template_folder = os.path.join(script_dir, "templates")
app = Flask(__name__, template_folder=template_folder)

@app.route("/")
def index():
    """Index page."""
    return "Hello, World!"

@app.route("/client")
def client():
    """Client page."""
    return render_template("client.html")

@app.route("/twilio/client", methods=["GET", "POST"])
def twilio_client():
    """
    Handle Twilio Client capability token request.
    """
    try:
        print("Received Twilio Client capability token request")

        # Get the room name from the request
        room_name = request.args.get("room")
        print(f"Room name: {room_name}")

        # Generate a capability token for the client
        # This token allows the client to make and receive calls
        from twilio.jwt.client import Client<PERSON>apabilityToken

        # Create a capability token
        token = ClientCapabilityToken(
            account_sid=os.environ.get("TWILIO_ACCOUNT_SID", "**********************************"),
            auth_token=os.environ.get("TWILIO_AUTH_TOKEN", "5f63c9c9a9e9e9e9e9e9e9e9e9e9ab01")
        )

        # Allow the client to receive calls
        token.allow_client_incoming("guestrix")

        # Generate the token
        token_str = token.to_jwt().decode("utf-8")

        # Return the token as JSON
        return jsonify({"token": token_str})

    except Exception as e:
        print(f"Error handling Twilio Client capability token request: {e}")
        return jsonify({"error": str(e)}), 500

@app.route("/twilio/voice", methods=["POST"])
def twilio_voice():
    """
    Handle Twilio voice webhook.
    """
    try:
        print("Received Twilio voice webhook")

        # Get the caller's phone number
        caller = request.form.get("From", "unknown")
        called = request.form.get("To", "unknown")

        print(f"Caller: {caller}, Called: {called}")

        # Generate TwiML for the call
        from twilio.twiml.voice_response import VoiceResponse, Dial

        response = VoiceResponse()
        dial = Dial()

        # Add the client to the dial verb
        dial.client(
            "guestrix",
            url=f"https://2146-75-194-21-68.ngrok-free.app/twilio/client"
        )

        response.append(dial)

        # Return the TwiML response
        return str(response)

    except Exception as e:
        print(f"Error handling Twilio voice webhook: {e}")

        # Return a simple TwiML response
        from twilio.twiml.voice_response import VoiceResponse

        response = VoiceResponse()
        response.say("Sorry, there was an error processing your call.")
        response.hangup()

        return str(response)

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5002, debug=True)
