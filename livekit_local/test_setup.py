#!/usr/bin/env python3
"""
Test script for the LiveKit setup.

This script tests the LiveKit setup by checking if the Docker containers are running
and if the Flask server can connect to them.
"""

import os
import sys
import json
import logging
import requests
import subprocess
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("test_setup")

def check_docker_containers():
    """Check if the Docker containers are running."""
    try:
        # Run docker-compose ps
        result = subprocess.run(
            ["docker-compose", "ps", "-q"],
            capture_output=True,
            text=True,
            check=True
        )

        # Check if any containers are running
        if result.stdout.strip():
            logger.info("Docker containers are running")
            return True
        else:
            logger.error("No Docker containers are running")
            return False

    except subprocess.CalledProcessError as e:
        logger.error(f"Error checking Docker containers: {e}")
        return False

def check_livekit_server():
    """Check if the LiveKit server is running."""
    try:
        # Try to connect to the LiveKit server
        response = requests.get("http://localhost:7880/health")
        
        if response.status_code == 200:
            logger.info("LiveKit server is running")
            return True
        else:
            logger.error(f"LiveKit server returned status code {response.status_code}")
            return False

    except requests.RequestException as e:
        logger.error(f"Error connecting to LiveKit server: {e}")
        return False

def check_flask_server():
    """Check if the Flask server is running."""
    try:
        # Try to connect to the Flask server
        response = requests.get("http://localhost:5000/health")
        
        if response.status_code == 200:
            logger.info("Flask server is running")
            return True
        else:
            logger.error(f"Flask server returned status code {response.status_code}")
            return False

    except requests.RequestException as e:
        logger.error(f"Error connecting to Flask server: {e}")
        return False

def check_env_variables():
    """Check if the environment variables are set."""
    # Try to load from .env.livekit first, then fall back to the twilio-gemini .env file
    if os.path.exists(".env.livekit"):
        load_dotenv(".env.livekit")
    elif os.path.exists("../.env.livekit"):
        load_dotenv("../.env.livekit")
    elif os.path.exists("../twilio-gemini/.env"):
        load_dotenv("../twilio-gemini/.env")
    
    # Check required environment variables
    required_vars = [
        "TWILIO_ACCOUNT_SID",
        "TWILIO_AUTH_TOKEN",
        "GEMINI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        logger.info("All required environment variables are set")
        return True

def main():
    """Main function."""
    logger.info("Testing LiveKit setup...")
    
    # Check environment variables
    env_ok = check_env_variables()
    
    # Check Docker containers
    docker_ok = check_docker_containers()
    
    # Check LiveKit server
    livekit_ok = check_livekit_server() if docker_ok else False
    
    # Check Flask server
    flask_ok = check_flask_server()
    
    # Print summary
    logger.info("Test results:")
    logger.info(f"Environment variables: {'OK' if env_ok else 'FAILED'}")
    logger.info(f"Docker containers: {'OK' if docker_ok else 'FAILED'}")
    logger.info(f"LiveKit server: {'OK' if livekit_ok else 'FAILED'}")
    logger.info(f"Flask server: {'OK' if flask_ok else 'FAILED'}")
    
    # Return success if all checks passed
    if env_ok and docker_ok and livekit_ok and flask_ok:
        logger.info("All tests passed!")
        return 0
    else:
        logger.error("Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
