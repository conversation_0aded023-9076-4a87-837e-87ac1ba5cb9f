Host Dashboard Template Data:
dashboard:820 - Name: <PERSON>
dashboard:821 - Email: Not provided
dashboard:822 - Phone Number: +15551234444
dashboard:823 - User ID: L7wwaH3Mo7bsFMx5IO3QLXAdWP52
dashboard:824 - User Role: host
auth.js:290 Checking authentication state...
auth.js:10 Initializing Firebase securely for auth...
dashboard:854 Loading Firebase configuration from secure endpoint...
host_dashboard.js:15 Host Dashboard loaded
host_dashboard.js:91 Loading properties for host from session user
host_dashboard.js:91 Loading properties for host from session user
dashboard:874 Firebase configuration loaded securely
dashboard:879 Firebase initialized securely
auth.js:13 Firebase auth initialized securely
host_dashboard.js:122 API Response: {properties: Array(3), success: true}
host_dashboard.js:124 Properties from API: (3) [{…}, {…}, {…}]
host_dashboard.js:170 Creating property card for: 44a4c8a9-e0a9-4d99-8d51-b26a6c7a7d8a
host_dashboard.js:171 Property data: {address: 'West Allis, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Fri, 18 Jul 2025 13:33:46 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: 5a1a2550-daa7-46de-95d5-d6bc4a80af7a
host_dashboard.js:171 Property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: 9684d928-f1e9-41fd-875c-77fa57e837cb
host_dashboard.js:171 Property data: {address: 'Saint Francis, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 13:10:57 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:122 API Response: {properties: Array(3), success: true}
host_dashboard.js:124 Properties from API: (3) [{…}, {…}, {…}]
host_dashboard.js:170 Creating property card for: 44a4c8a9-e0a9-4d99-8d51-b26a6c7a7d8a
host_dashboard.js:171 Property data: {address: 'West Allis, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Fri, 18 Jul 2025 13:33:46 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: 5a1a2550-daa7-46de-95d5-d6bc4a80af7a
host_dashboard.js:171 Property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
host_dashboard.js:170 Creating property card for: 9684d928-f1e9-41fd-875c-77fa57e837cb
host_dashboard.js:171 Property data: {address: 'Saint Francis, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 13:10:57 GMT', …}
host_dashboard.js:172 Property.new value: true Type: boolean
auth.js:309 Auth state changed, user: signed in (L7wwaH3Mo7bsFMx5IO3QLXAdWP52)
auth.js:324 User is signed in with Firebase. Updating UI.
host_dashboard.js:1114 Starting property setup for: 5a1a2550-daa7-46de-95d5-d6bc4a80af7a
property-setup-modal.js:37 Opening Property Setup Modal
property-setup-modal.js:38 Property ID: 5a1a2550-daa7-46de-95d5-d6bc4a80af7a
property-setup-modal.js:39 Property Data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:40 Property amenities: {appliances: Array(14), basic: Array(39)}
property-setup-modal.js:682 Normalized "Refrigerator" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Microwave" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Freezer" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Stove" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Oven" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Hot water kettle" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Coffee maker" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Toaster" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Blender" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Coffee" location to Kitchen (was: "kitchen")
property-setup-modal.js:700 Enhanced categorization complete: {basicCount: 39, appliancesCount: 14, movedItems: 0}
property-setup-modal.js:304 Loading amenities section
property-setup-modal.js:305 Property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:306 Enhanced amenities data: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:307 Basic amenities count: 39
property-setup-modal.js:308 Appliances count: 14
property-setup-modal.js:304 Loading amenities section
property-setup-modal.js:305 Property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:306 Enhanced amenities data: {basic: Array(39), appliances: Array(15)}
property-setup-modal.js:307 Basic amenities count: 39
property-setup-modal.js:308 Appliances count: 15
property-setup-modal.js:716 🔍 Collecting current amenities...
property-setup-modal.js:720 Basic amenities container: <div class=​"grid grid-cols-2 md:​grid-cols-3 gap-2" id=​"basic-amenities">​…​</div>​
property-setup-modal.js:724 Found 39 basic amenity elements
property-setup-modal.js:730 Amenity 0: "Kitchen" - Checked: true
property-setup-modal.js:730 Amenity 1: "Wifi" - Checked: true
property-setup-modal.js:730 Amenity 2: "Dedicated workspace" - Checked: true
property-setup-modal.js:730 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:730 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:730 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:730 Amenity 6: "Patio or balcony" - Checked: true
property-setup-modal.js:730 Amenity 7: "Cleaning products" - Checked: true
property-setup-modal.js:730 Amenity 8: "Shampoo" - Checked: true
property-setup-modal.js:730 Amenity 9: "Body soap" - Checked: true
property-setup-modal.js:730 Amenity 10: "Bidet" - Checked: true
property-setup-modal.js:730 Amenity 11: "Hot water" - Checked: true
property-setup-modal.js:730 Amenity 12: "Shower gel" - Checked: true
property-setup-modal.js:730 Amenity 13: "Hangers" - Checked: true
property-setup-modal.js:730 Amenity 14: "Bed linens" - Checked: true
property-setup-modal.js:730 Amenity 15: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:730 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:730 Amenity 17: "Clothing storage" - Checked: true
property-setup-modal.js:730 Amenity 18: "Ethernet connection" - Checked: true
property-setup-modal.js:730 Amenity 19: "Books and reading material" - Checked: true
property-setup-modal.js:730 Amenity 20: "Board games" - Checked: true
property-setup-modal.js:730 Amenity 21: "Ceiling fan" - Checked: true
property-setup-modal.js:730 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:730 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:730 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:730 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:730 Amenity 26: "First aid kit" - Checked: true
property-setup-modal.js:730 Amenity 27: "Cooking basics" - Checked: true
property-setup-modal.js:730 Amenity 28: "Dishes and silverware" - Checked: true
property-setup-modal.js:730 Amenity 29: "Wine glasses" - Checked: true
property-setup-modal.js:730 Amenity 30: "Baking sheet" - Checked: true
property-setup-modal.js:730 Amenity 31: "Outdoor dining area" - Checked: true
property-setup-modal.js:730 Amenity 32: "Free street parking" - Checked: true
property-setup-modal.js:730 Amenity 33: "Long term stays allowed" - Checked: true
property-setup-modal.js:730 Amenity 34: "Self check-in" - Checked: true
property-setup-modal.js:730 Amenity 35: "Smart lock" - Checked: true
property-setup-modal.js:730 Amenity 36: "Exterior security cameras on property" - Checked: true
property-setup-modal.js:730 Amenity 37: "Essentials" - Checked: true
property-setup-modal.js:730 Amenity 38: "Private entrance" - Checked: true
property-setup-modal.js:742 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:746 Found 15 appliance elements
property-setup-modal.js:750 Appliance 0: Found 4 input fields
property-setup-modal.js:758 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:750 Appliance 1: Found 4 input fields
property-setup-modal.js:758 Appliance 1 data: Name="Washer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 2: Found 4 input fields
property-setup-modal.js:758 Appliance 2 data: Name="Dryer", Location="Laundry", Brand="", Model=""
property-setup-modal.js:750 Appliance 3: Found 4 input fields
property-setup-modal.js:758 Appliance 3 data: Name="Hair dryer", Location="Bathroom", Brand="", Model=""
property-setup-modal.js:750 Appliance 4: Found 4 input fields
property-setup-modal.js:758 Appliance 4 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 5: Found 4 input fields
property-setup-modal.js:758 Appliance 5 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 6: Found 4 input fields
property-setup-modal.js:758 Appliance 6 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 7: Found 4 input fields
property-setup-modal.js:758 Appliance 7 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 8: Found 4 input fields
property-setup-modal.js:758 Appliance 8 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 9: Found 4 input fields
property-setup-modal.js:758 Appliance 9 data: Name="Hot water kettle", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 10: Found 4 input fields
property-setup-modal.js:758 Appliance 10 data: Name="Coffee maker", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 11: Found 4 input fields
property-setup-modal.js:758 Appliance 11 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 12: Found 4 input fields
property-setup-modal.js:758 Appliance 12 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 13: Found 4 input fields
property-setup-modal.js:758 Appliance 13 data: Name="Coffee", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 14: Found 4 input fields
property-setup-modal.js:758 Appliance 14 data: Name="", Location="", Brand="", Model=""
property-setup-modal.js:774 Collected current amenities: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:452 Saving basic information with amenities: {name: 'Eastside Townhouse - Fun & Stylish MKE Getaway', address: 'Eastside, Milwaukee, Wisconsin, United States', description: 'East Side townhouse offers ample space with four b…unique character of this walkable, urban setting.', icalUrl: '', checkInTime: '15:00', …}
property-setup-modal.js:794 Step 1 saved successfully
property-setup-modal.js:823 Updated amenities in local property data: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:1780 🔧 Loading unified rules section...
property-setup-modal.js:1787 🔍 Debugging imported rules:
property-setup-modal.js:1788   - extractedRules: []
property-setup-modal.js:1789   - deepExtractedRules: []
property-setup-modal.js:1790   - allImportedRules: []
property-setup-modal.js:1791   - propertyData.importData: undefined
property-setup-modal.js:1792   - propertyData.importData?.rawData: undefined
property-setup-modal.js:1796 No rules in import data, checking knowledge items...
property-setup-modal.js:1806 Found rule items in knowledge: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:1810   Rule 1: {type: 'rule', content: 'Lock door', tags: Array(2)}
property-setup-modal.js:1810   Rule 2: {type: 'rule', content: 'Self check-in with smart lock', tags: Array(2)}
property-setup-modal.js:1810   Rule 3: {type: 'rule', content: 'Maximum guests', tags: Array(2)}
property-setup-modal.js:1810   Rule 4: {type: 'rule', content: 'Clean up', tags: Array(2)}
property-setup-modal.js:1810   Rule 5: {type: 'rule', content: 'No events', tags: Array(2)}
property-setup-modal.js:1810   Rule 6: {type: 'rule', content: '5 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 7: {type: 'rule', content: 'Lock up', tags: Array(2)}
property-setup-modal.js:1810   Rule 8: {type: 'rule', content: '11 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 9: {type: 'rule', content: '6 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 10: {type: 'rule', content: 'Checkout before 11:00 AM', tags: Array(2)}
property-setup-modal.js:1835 Filtered to 9 valid imported rules
property-setup-modal.js:1906 🚫 Conflict detected: Default rule "No parties or events" conflicts with imported rule "No events"
property-setup-modal.js:1906 🚫 Conflict detected: Default rule "Maximum occupancy" conflicts with imported rule "Maximum guests"
property-setup-modal.js:1841 Using 8 default rules (2 filtered out due to conflicts)
property-setup-modal.js:1849 Total unified rules: 17
property-setup-modal.js:1978 ✅ Rendered 17 unified rules (9 imported, 8 default)
property-setup-modal.js:1002 Saving unified house rules...
property-setup-modal.js:1029 House rules to save: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:794 Step 2 saved successfully
property-setup-modal.js:829 Updated house rules in local property data: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:304 Loading amenities section
property-setup-modal.js:305 Property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:306 Enhanced amenities data: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:307 Basic amenities count: 39
property-setup-modal.js:308 Appliances count: 14
property-setup-modal.js:716 🔍 Collecting current amenities...
property-setup-modal.js:720 Basic amenities container: <div class=​"grid grid-cols-2 md:​grid-cols-3 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:724 Found 39 basic amenity elements
property-setup-modal.js:730 Amenity 0: "Kitchen" - Checked: true
property-setup-modal.js:730 Amenity 1: "Wifi" - Checked: true
property-setup-modal.js:730 Amenity 2: "Dedicated workspace" - Checked: true
property-setup-modal.js:730 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:730 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:730 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:730 Amenity 6: "Patio or balcony" - Checked: true
property-setup-modal.js:730 Amenity 7: "Cleaning products" - Checked: true
property-setup-modal.js:730 Amenity 8: "Shampoo" - Checked: true
property-setup-modal.js:730 Amenity 9: "Body soap" - Checked: true
property-setup-modal.js:730 Amenity 10: "Bidet" - Checked: true
property-setup-modal.js:730 Amenity 11: "Hot water" - Checked: true
property-setup-modal.js:730 Amenity 12: "Shower gel" - Checked: true
property-setup-modal.js:730 Amenity 13: "Hangers" - Checked: true
property-setup-modal.js:730 Amenity 14: "Bed linens" - Checked: true
property-setup-modal.js:730 Amenity 15: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:730 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:730 Amenity 17: "Clothing storage" - Checked: true
property-setup-modal.js:730 Amenity 18: "Ethernet connection" - Checked: true
property-setup-modal.js:730 Amenity 19: "Books and reading material" - Checked: true
property-setup-modal.js:730 Amenity 20: "Board games" - Checked: true
property-setup-modal.js:730 Amenity 21: "Ceiling fan" - Checked: true
property-setup-modal.js:730 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:730 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:730 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:730 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:730 Amenity 26: "First aid kit" - Checked: true
property-setup-modal.js:730 Amenity 27: "Cooking basics" - Checked: true
property-setup-modal.js:730 Amenity 28: "Dishes and silverware" - Checked: true
property-setup-modal.js:730 Amenity 29: "Wine glasses" - Checked: true
property-setup-modal.js:730 Amenity 30: "Baking sheet" - Checked: true
property-setup-modal.js:730 Amenity 31: "Outdoor dining area" - Checked: true
property-setup-modal.js:730 Amenity 32: "Free street parking" - Checked: true
property-setup-modal.js:730 Amenity 33: "Long term stays allowed" - Checked: true
property-setup-modal.js:730 Amenity 34: "Self check-in" - Checked: true
property-setup-modal.js:730 Amenity 35: "Smart lock" - Checked: true
property-setup-modal.js:730 Amenity 36: "Exterior security cameras on property" - Checked: true
property-setup-modal.js:730 Amenity 37: "Essentials" - Checked: true
property-setup-modal.js:730 Amenity 38: "Private entrance" - Checked: true
property-setup-modal.js:742 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:746 Found 14 appliance elements
property-setup-modal.js:750 Appliance 0: Found 4 input fields
property-setup-modal.js:758 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:750 Appliance 1: Found 4 input fields
property-setup-modal.js:758 Appliance 1 data: Name="Washer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 2: Found 4 input fields
property-setup-modal.js:758 Appliance 2 data: Name="Dryer", Location="Laundry", Brand="", Model=""
property-setup-modal.js:750 Appliance 3: Found 4 input fields
property-setup-modal.js:758 Appliance 3 data: Name="Hair dryer", Location="Bathroom", Brand="", Model=""
property-setup-modal.js:750 Appliance 4: Found 4 input fields
property-setup-modal.js:758 Appliance 4 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 5: Found 4 input fields
property-setup-modal.js:758 Appliance 5 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 6: Found 4 input fields
property-setup-modal.js:758 Appliance 6 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 7: Found 4 input fields
property-setup-modal.js:758 Appliance 7 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 8: Found 4 input fields
property-setup-modal.js:758 Appliance 8 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 9: Found 4 input fields
property-setup-modal.js:758 Appliance 9 data: Name="Hot water kettle", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 10: Found 4 input fields
property-setup-modal.js:758 Appliance 10 data: Name="Coffee maker", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 11: Found 4 input fields
property-setup-modal.js:758 Appliance 11 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 12: Found 4 input fields
property-setup-modal.js:758 Appliance 12 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 13: Found 4 input fields
property-setup-modal.js:758 Appliance 13 data: Name="Coffee", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:774 Collected current amenities: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:452 Saving basic information with amenities: {name: 'Eastside Townhouse - Fun & Stylish MKE Getaway', address: 'Eastside, Milwaukee, Wisconsin, United States', description: 'East Side townhouse offers ample space with four b…unique character of this walkable, urban setting.', icalUrl: '', checkInTime: '15:00', …}
property-setup-modal.js:794 Step 1 saved successfully
property-setup-modal.js:823 Updated amenities in local property data: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:1780 🔧 Loading unified rules section...
property-setup-modal.js:1787 🔍 Debugging imported rules:
property-setup-modal.js:1788   - extractedRules: []
property-setup-modal.js:1789   - deepExtractedRules: []
property-setup-modal.js:1790   - allImportedRules: []
property-setup-modal.js:1791   - propertyData.importData: undefined
property-setup-modal.js:1792   - propertyData.importData?.rawData: undefined
property-setup-modal.js:1796 No rules in import data, checking knowledge items...
property-setup-modal.js:1806 Found rule items in knowledge: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:1810   Rule 1: {type: 'rule', content: 'Lock door', tags: Array(2)}
property-setup-modal.js:1810   Rule 2: {type: 'rule', content: 'Self check-in with smart lock', tags: Array(2)}
property-setup-modal.js:1810   Rule 3: {type: 'rule', content: 'Maximum guests', tags: Array(2)}
property-setup-modal.js:1810   Rule 4: {type: 'rule', content: 'Clean up', tags: Array(2)}
property-setup-modal.js:1810   Rule 5: {type: 'rule', content: 'No events', tags: Array(2)}
property-setup-modal.js:1810   Rule 6: {type: 'rule', content: '5 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 7: {type: 'rule', content: 'Lock up', tags: Array(2)}
property-setup-modal.js:1810   Rule 8: {type: 'rule', content: '11 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 9: {type: 'rule', content: '6 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 10: {type: 'rule', content: 'Checkout before 11:00 AM', tags: Array(2)}
property-setup-modal.js:1835 Filtered to 9 valid imported rules
property-setup-modal.js:1906 🚫 Conflict detected: Default rule "No parties or events" conflicts with imported rule "No events"
property-setup-modal.js:1906 🚫 Conflict detected: Default rule "Maximum occupancy" conflicts with imported rule "Maximum guests"
property-setup-modal.js:1841 Using 8 default rules (2 filtered out due to conflicts)
property-setup-modal.js:1849 Total unified rules: 17
property-setup-modal.js:1978 ✅ Rendered 17 unified rules (9 imported, 8 default)
property-setup-modal.js:1002 Saving unified house rules...
property-setup-modal.js:1029 House rules to save: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:794 Step 2 saved successfully
property-setup-modal.js:829 Updated house rules in local property data: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:1103 Loading emergency information...
property-setup-modal.js:1223 Imported safety info: []
property-setup-modal.js:1461 Adding custom emergency item...
host_dashboard.js:1114 Starting property setup for: 5a1a2550-daa7-46de-95d5-d6bc4a80af7a
property-setup-modal.js:37 Opening Property Setup Modal
property-setup-modal.js:38 Property ID: 5a1a2550-daa7-46de-95d5-d6bc4a80af7a
property-setup-modal.js:39 Property Data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:40 Property amenities: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:682 Normalized "Refrigerator" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Microwave" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Freezer" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Stove" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Oven" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Hot water kettle" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Coffee maker" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Toaster" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Blender" location to Kitchen (was: "kitchen")
property-setup-modal.js:682 Normalized "Coffee" location to Kitchen (was: "kitchen")
property-setup-modal.js:700 Enhanced categorization complete: {basicCount: 39, appliancesCount: 14, movedItems: 0}
property-setup-modal.js:304 Loading amenities section
property-setup-modal.js:305 Property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:306 Enhanced amenities data: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:307 Basic amenities count: 39
property-setup-modal.js:308 Appliances count: 14
property-setup-modal.js:716 🔍 Collecting current amenities...
property-setup-modal.js:720 Basic amenities container: <div class=​"grid grid-cols-2 md:​grid-cols-3 gap-2" id=​"basic-amenities">​…​</div>​grid
property-setup-modal.js:724 Found 39 basic amenity elements
property-setup-modal.js:730 Amenity 0: "Kitchen" - Checked: true
property-setup-modal.js:730 Amenity 1: "Wifi" - Checked: true
property-setup-modal.js:730 Amenity 2: "Dedicated workspace" - Checked: true
property-setup-modal.js:730 Amenity 3: "Free parking on premises" - Checked: true
property-setup-modal.js:730 Amenity 4: "Air conditioning" - Checked: true
property-setup-modal.js:730 Amenity 5: "Bathtub" - Checked: true
property-setup-modal.js:730 Amenity 6: "Patio or balcony" - Checked: true
property-setup-modal.js:730 Amenity 7: "Cleaning products" - Checked: true
property-setup-modal.js:730 Amenity 8: "Shampoo" - Checked: true
property-setup-modal.js:730 Amenity 9: "Body soap" - Checked: true
property-setup-modal.js:730 Amenity 10: "Bidet" - Checked: true
property-setup-modal.js:730 Amenity 11: "Hot water" - Checked: true
property-setup-modal.js:730 Amenity 12: "Shower gel" - Checked: true
property-setup-modal.js:730 Amenity 13: "Hangers" - Checked: true
property-setup-modal.js:730 Amenity 14: "Bed linens" - Checked: true
property-setup-modal.js:730 Amenity 15: "Extra pillows and blankets" - Checked: true
property-setup-modal.js:730 Amenity 16: "Iron" - Checked: true
property-setup-modal.js:730 Amenity 17: "Clothing storage" - Checked: true
property-setup-modal.js:730 Amenity 18: "Ethernet connection" - Checked: true
property-setup-modal.js:730 Amenity 19: "Books and reading material" - Checked: true
property-setup-modal.js:730 Amenity 20: "Board games" - Checked: true
property-setup-modal.js:730 Amenity 21: "Ceiling fan" - Checked: true
property-setup-modal.js:730 Amenity 22: "Heating" - Checked: true
property-setup-modal.js:730 Amenity 23: "Smoke alarm" - Checked: true
property-setup-modal.js:730 Amenity 24: "Carbon monoxide alarm" - Checked: true
property-setup-modal.js:730 Amenity 25: "Fire extinguisher" - Checked: true
property-setup-modal.js:730 Amenity 26: "First aid kit" - Checked: true
property-setup-modal.js:730 Amenity 27: "Cooking basics" - Checked: true
property-setup-modal.js:730 Amenity 28: "Dishes and silverware" - Checked: true
property-setup-modal.js:730 Amenity 29: "Wine glasses" - Checked: true
property-setup-modal.js:730 Amenity 30: "Baking sheet" - Checked: true
property-setup-modal.js:730 Amenity 31: "Outdoor dining area" - Checked: true
property-setup-modal.js:730 Amenity 32: "Free street parking" - Checked: true
property-setup-modal.js:730 Amenity 33: "Long term stays allowed" - Checked: true
property-setup-modal.js:730 Amenity 34: "Self check-in" - Checked: true
property-setup-modal.js:730 Amenity 35: "Smart lock" - Checked: true
property-setup-modal.js:730 Amenity 36: "Exterior security cameras on property" - Checked: true
property-setup-modal.js:730 Amenity 37: "Essentials" - Checked: true
property-setup-modal.js:730 Amenity 38: "Private entrance" - Checked: true
property-setup-modal.js:742 Appliances container: <div class=​"space-y-2" id=​"appliances-list">​…​</div>​
property-setup-modal.js:746 Found 14 appliance elements
property-setup-modal.js:750 Appliance 0: Found 4 input fields
property-setup-modal.js:758 Appliance 0 data: Name="TV", Location="Living Room", Brand="", Model=""
property-setup-modal.js:750 Appliance 1: Found 4 input fields
property-setup-modal.js:758 Appliance 1 data: Name="Washer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 2: Found 4 input fields
property-setup-modal.js:758 Appliance 2 data: Name="Dryer", Location="Laundry", Brand="", Model=""
property-setup-modal.js:750 Appliance 3: Found 4 input fields
property-setup-modal.js:758 Appliance 3 data: Name="Hair dryer", Location="Bathroom", Brand="", Model=""
property-setup-modal.js:750 Appliance 4: Found 4 input fields
property-setup-modal.js:758 Appliance 4 data: Name="Refrigerator", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 5: Found 4 input fields
property-setup-modal.js:758 Appliance 5 data: Name="Microwave", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 6: Found 4 input fields
property-setup-modal.js:758 Appliance 6 data: Name="Freezer", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 7: Found 4 input fields
property-setup-modal.js:758 Appliance 7 data: Name="Stove", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 8: Found 4 input fields
property-setup-modal.js:758 Appliance 8 data: Name="Oven", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 9: Found 4 input fields
property-setup-modal.js:758 Appliance 9 data: Name="Hot water kettle", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 10: Found 4 input fields
property-setup-modal.js:758 Appliance 10 data: Name="Coffee maker", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 11: Found 4 input fields
property-setup-modal.js:758 Appliance 11 data: Name="Toaster", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 12: Found 4 input fields
property-setup-modal.js:758 Appliance 12 data: Name="Blender", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:750 Appliance 13: Found 4 input fields
property-setup-modal.js:758 Appliance 13 data: Name="Coffee", Location="Kitchen", Brand="", Model=""
property-setup-modal.js:774 Collected current amenities: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:452 Saving basic information with amenities: {name: 'Eastside Townhouse - Fun & Stylish MKE Getaway', address: 'Eastside, Milwaukee, Wisconsin, United States', description: 'East Side townhouse offers ample space with four b…unique character of this walkable, urban setting.', icalUrl: '', checkInTime: '15:00', …}
property-setup-modal.js:794 Step 1 saved successfully
property-setup-modal.js:823 Updated amenities in local property data: {basic: Array(39), appliances: Array(14)}
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}
property-setup-modal.js:1780 🔧 Loading unified rules section...
property-setup-modal.js:1787 🔍 Debugging imported rules:
property-setup-modal.js:1788   - extractedRules: []
property-setup-modal.js:1789   - deepExtractedRules: []
property-setup-modal.js:1790   - allImportedRules: []
property-setup-modal.js:1791   - propertyData.importData: undefined
property-setup-modal.js:1792   - propertyData.importData?.rawData: undefined
property-setup-modal.js:1796 No rules in import data, checking knowledge items...
property-setup-modal.js:1806 Found rule items in knowledge: (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:1810   Rule 1: {type: 'rule', content: 'Lock door', tags: Array(2)}
property-setup-modal.js:1810   Rule 2: {type: 'rule', content: 'Self check-in with smart lock', tags: Array(2)}
property-setup-modal.js:1810   Rule 3: {type: 'rule', content: 'Maximum guests', tags: Array(2)}
property-setup-modal.js:1810   Rule 4: {type: 'rule', content: 'Clean up', tags: Array(2)}
property-setup-modal.js:1810   Rule 5: {type: 'rule', content: 'No events', tags: Array(2)}
property-setup-modal.js:1810   Rule 6: {type: 'rule', content: '5 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 7: {type: 'rule', content: 'Lock up', tags: Array(2)}
property-setup-modal.js:1810   Rule 8: {type: 'rule', content: '11 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 9: {type: 'rule', content: '6 guests maximum', tags: Array(2)}
property-setup-modal.js:1810   Rule 10: {type: 'rule', content: 'Checkout before 11:00 AM', tags: Array(2)}
property-setup-modal.js:1835 Filtered to 9 valid imported rules
property-setup-modal.js:1906 🚫 Conflict detected: Default rule "No parties or events" conflicts with imported rule "No events"
property-setup-modal.js:1906 🚫 Conflict detected: Default rule "Maximum occupancy" conflicts with imported rule "Maximum guests"
property-setup-modal.js:1841 Using 8 default rules (2 filtered out due to conflicts)
property-setup-modal.js:1849 Total unified rules: 17
property-setup-modal.js:1978 ✅ Rendered 17 unified rules (9 imported, 8 default)
property-setup-modal.js:1002 Saving unified house rules...
property-setup-modal.js:1029 House rules to save: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:794 Step 2 saved successfully
property-setup-modal.js:829 Updated house rules in local property data: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}address: "Eastside, Milwaukee, Wisconsin, United States"amenities: {basic: Array(39), appliances: Array(14)}checkInTime: "15:00"checkOutTime: "11:00"createdAt: "Sat, 19 Jul 2025 21:34:10 GMT"description: "East Side townhouse offers ample space with four bedrooms, ideal for groups exploring Milwaukee. Nestled in a lively neighborhood, you'll be steps away from the East Side's energetic mix of shops and restaurants. Experience the unique character of this walkable, urban setting."emergencyInfo: [{…}]hostId: "L7wwaH3Mo7bsFMx5IO3QLXAdWP52"houseRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]icalUrl: ""id: "5a1a2550-daa7-46de-95d5-d6bc4a80af7a"name: "Eastside Townhouse - Fun & Stylish MKE Getaway"new: truesafetyInfo: []status: "inactive"updatedAt: "Sun, 20 Jul 2025 17:47:21 GMT"wifiDetails: {networkName: 'erty123', password: 'Der123&'}[[Prototype]]: Object
property-setup-modal.js:1103 Loading emergency information...
property-setup-modal.js:1223 Imported safety info: []length: 0[[Prototype]]: Array(0)at: ƒ at()concat: ƒ concat()constructor: ƒ Array()copyWithin: ƒ copyWithin()entries: ƒ entries()every: ƒ every()fill: ƒ fill()filter: ƒ filter()find: ƒ find()findIndex: ƒ findIndex()findLast: ƒ findLast()findLastIndex: ƒ findLastIndex()flat: ƒ flat()flatMap: ƒ flatMap()forEach: ƒ forEach()includes: ƒ includes()indexOf: ƒ indexOf()join: ƒ join()keys: ƒ keys()lastIndexOf: ƒ lastIndexOf()length: 0map: ƒ map()pop: ƒ pop()push: ƒ push()reduce: ƒ reduce()reduceRight: ƒ reduceRight()reverse: ƒ reverse()shift: ƒ shift()slice: ƒ slice()some: ƒ some()sort: ƒ sort()splice: ƒ splice()toLocaleString: ƒ toLocaleString()toReversed: ƒ toReversed()toSorted: ƒ toSorted()toSpliced: ƒ toSpliced()toString: ƒ toString()unshift: ƒ unshift()values: ƒ values()with: ƒ with()Symbol(Symbol.iterator): ƒ values()Symbol(Symbol.unscopables): {at: true, copyWithin: true, entries: true, fill: true, find: true, …}[[Prototype]]: Object
property-setup-modal.js:1461 Adding custom emergency item...
property-setup-modal.js:1383 Saving emergency information...
property-setup-modal.js:1454 Emergency information to save: [{…}]0: {id: 'custom_1753034884752', title: 'Snake Bite', instructions: 'Go to a hospital immediately and notify the host.', location: '', enabled: true, …}length: 1[[Prototype]]: Array(0)
property-setup-modal.js:794 Step 3 saved successfully
property-setup-modal.js:835 Updated emergency info in local property data: [{…}]0: {id: 'custom_1753034884752', title: 'Snake Bite', instructions: 'Go to a hospital immediately and notify the host.', location: '', enabled: true, …}length: 1[[Prototype]]: Array(0)
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}address: "Eastside, Milwaukee, Wisconsin, United States"amenities: {basic: Array(39), appliances: Array(14)}checkInTime: "15:00"checkOutTime: "11:00"createdAt: "Sat, 19 Jul 2025 21:34:10 GMT"description: "East Side townhouse offers ample space with four bedrooms, ideal for groups exploring Milwaukee. Nestled in a lively neighborhood, you'll be steps away from the East Side's energetic mix of shops and restaurants. Experience the unique character of this walkable, urban setting."emergencyInfo: [{…}]hostId: "L7wwaH3Mo7bsFMx5IO3QLXAdWP52"houseRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]icalUrl: ""id: "5a1a2550-daa7-46de-95d5-d6bc4a80af7a"name: "Eastside Townhouse - Fun & Stylish MKE Getaway"new: truesafetyInfo: []status: "inactive"updatedAt: "Sun, 20 Jul 2025 17:47:21 GMT"wifiDetails: {networkName: 'erty123', password: 'Der123&'}[[Prototype]]: Object
property-setup-modal.js:1103 Loading emergency information...
property-setup-modal.js:1223 Imported safety info: []
property-setup-modal.js:1383 Saving emergency information...
property-setup-modal.js:1454 Emergency information to save: (2) [{…}, {…}]0: enabled: trueid: "custom_1753034884752"instructions: "Go to a hospital immediately and notify the host."location: ""title: "Snake Bite"type: "custom"[[Prototype]]: Object1: enabled: trueid: "gas_leak"instructions: "Do not use electrical switches or open flames. Evacuate immediately and call gas company emergency line."location: "Gas shut-off valve location: outside in the back of the building"title: "Gas Leak"type: "default"[[Prototype]]: Objectlength: 2[[Prototype]]: Array(0)
property-setup-modal.js:794 Step 3 saved successfully
property-setup-modal.js:835 Updated emergency info in local property data: (2) [{…}, {…}]
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}address: "Eastside, Milwaukee, Wisconsin, United States"amenities: {basic: Array(39), appliances: Array(14)}checkInTime: "15:00"checkOutTime: "11:00"createdAt: "Sat, 19 Jul 2025 21:34:10 GMT"description: "East Side townhouse offers ample space with four bedrooms, ideal for groups exploring Milwaukee. Nestled in a lively neighborhood, you'll be steps away from the East Side's energetic mix of shops and restaurants. Experience the unique character of this walkable, urban setting."emergencyInfo: (2) [{…}, {…}]0: {id: 'custom_1753034884752', title: 'Snake Bite', instructions: 'Go to a hospital immediately and notify the host.', location: '', enabled: true, …}1: {id: 'gas_leak', title: 'Gas Leak', instructions: 'Do not use electrical switches or open flames. Eva… immediately and call gas company emergency line.', location: 'Gas shut-off valve location: outside in the back of the building', enabled: true, …}length: 2[[Prototype]]: Array(0)hostId: "L7wwaH3Mo7bsFMx5IO3QLXAdWP52"houseRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]icalUrl: ""id: "5a1a2550-daa7-46de-95d5-d6bc4a80af7a"name: "Eastside Townhouse - Fun & Stylish MKE Getaway"new: truesafetyInfo: []length: 0[[Prototype]]: Array(0)at: ƒ at()concat: ƒ concat()constructor: ƒ Array()copyWithin: ƒ copyWithin()entries: ƒ entries()every: ƒ every()fill: ƒ fill()filter: ƒ filter()find: ƒ find()findIndex: ƒ findIndex()findLast: ƒ findLast()findLastIndex: ƒ findLastIndex()flat: ƒ flat()flatMap: ƒ flatMap()forEach: ƒ forEach()includes: ƒ includes()indexOf: ƒ indexOf()join: ƒ join()keys: ƒ keys()lastIndexOf: ƒ lastIndexOf()length: 0map: ƒ map()pop: ƒ pop()push: ƒ push()reduce: ƒ reduce()reduceRight: ƒ reduceRight()reverse: ƒ reverse()shift: ƒ shift()slice: ƒ slice()some: ƒ some()sort: ƒ sort()splice: ƒ splice()toLocaleString: ƒ toLocaleString()toReversed: ƒ toReversed()toSorted: ƒ toSorted()toSpliced: ƒ toSpliced()toString: ƒ toString()unshift: ƒ unshift()values: ƒ values()with: ƒ with()Symbol(Symbol.iterator): ƒ values()Symbol(Symbol.unscopables): {at: true, copyWithin: true, entries: true, fill: true, find: true, …}[[Prototype]]: Objectstatus: "inactive"updatedAt: "Sun, 20 Jul 2025 17:47:21 GMT"wifiDetails: {networkName: 'erty123', password: 'Der123&'}[[Prototype]]: Object
property-setup-modal.js:1103 Loading emergency information...
property-setup-modal.js:1223 Imported safety info: []
property-setup-modal.js:1383 Saving emergency information...
property-setup-modal.js:1454 Emergency information to save: [{…}]0: {id: 'custom_1753034884752', title: 'Snake Bite', instructions: 'Go to a hospital immediately and notify the host.', location: '', enabled: true, …}length: 1[[Prototype]]: Array(0)
property-setup-modal.js:794 Step 3 saved successfully
property-setup-modal.js:835 Updated emergency info in local property data: [{…}]
property-setup-modal.js:839 Updated local property data: {address: 'Eastside, Milwaukee, Wisconsin, United States', amenities: {…}, checkInTime: '15:00', checkOutTime: '11:00', createdAt: 'Sat, 19 Jul 2025 21:34:10 GMT', …}address: "Eastside, Milwaukee, Wisconsin, United States"amenities: {basic: Array(39), appliances: Array(14)}checkInTime: "15:00"checkOutTime: "11:00"createdAt: "Sat, 19 Jul 2025 21:34:10 GMT"description: "East Side townhouse offers ample space with four bedrooms, ideal for groups exploring Milwaukee. Nestled in a lively neighborhood, you'll be steps away from the East Side's energetic mix of shops and restaurants. Experience the unique character of this walkable, urban setting."emergencyInfo: Array(1)0: enabled: trueid: "custom_1753034884752"instructions: "Go to a hospital immediately and notify the host."location: ""title: "Snake Bite"type: "custom"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)hostId: "L7wwaH3Mo7bsFMx5IO3QLXAdWP52"houseRules: (9) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]icalUrl: ""id: "5a1a2550-daa7-46de-95d5-d6bc4a80af7a"name: "Eastside Townhouse - Fun & Stylish MKE Getaway"new: truesafetyInfo: []status: "inactive"updatedAt: "Sun, 20 Jul 2025 17:47:21 GMT"wifiDetails: {networkName: 'erty123', password: 'Der123&'}[[Prototype]]: Object
property-setup-modal.js:1103 Loading emergency information...
property-setup-modal.js:1223 Imported safety info: []