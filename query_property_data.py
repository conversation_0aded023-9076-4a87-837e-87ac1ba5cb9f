#!/usr/bin/env python3
"""
Query property data from Firestore to debug house rules storage and UI integration.
"""

import sys
import os
import json
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.firestore_client import get_property, list_knowledge_items_by_property

def query_property_data(property_id: str):
    """Query and display property data and knowledge items."""
    
    print(f"🔍 Querying property data for: {property_id}")
    print("=" * 80)
    
    # Get property data
    property_data = get_property(property_id)
    
    if not property_data:
        print(f"❌ Property {property_id} not found!")
        return
    
    print("📋 PROPERTY DATA:")
    print("-" * 40)
    
    # Display key fields
    print(f"Name: {property_data.get('name', 'N/A')}")
    print(f"Address: {property_data.get('address', 'N/A')}")
    print(f"Status: {property_data.get('status', 'N/A')}")
    print(f"New Property: {property_data.get('new', 'N/A')}")
    print(f"Host ID: {property_data.get('hostId', 'N/A')}")
    
    # Check amenities structure
    amenities = property_data.get('amenities', {})
    print(f"\n🏠 AMENITIES:")
    print(f"Basic amenities: {len(amenities.get('basic', []))}")
    print(f"Appliances: {len(amenities.get('appliances', []))}")
    
    # Show first few amenities for inspection
    basic_amenities = amenities.get('basic', [])
    if basic_amenities:
        print(f"First 5 basic amenities: {basic_amenities[:5]}")
    
    appliances = amenities.get('appliances', [])
    if appliances:
        print(f"First 5 appliances:")
        for i, appliance in enumerate(appliances[:5]):
            if isinstance(appliance, dict):
                print(f"  {i+1}. {appliance.get('name', 'N/A')} - Location: {appliance.get('location', 'N/A')}")
            else:
                print(f"  {i+1}. {appliance}")
    
    # Check house rules in property data
    house_rules = property_data.get('houseRules', [])
    print(f"\n📜 HOUSE RULES IN PROPERTY DATA: {len(house_rules)}")
    for i, rule in enumerate(house_rules[:5]):
        if isinstance(rule, dict):
            print(f"  {i+1}. {rule.get('title', 'N/A')}: {rule.get('description', 'N/A')[:50]}...")
        else:
            print(f"  {i+1}. {rule}")
    
    # Check import/raw data
    import_data = property_data.get('importData', {})
    raw_data = import_data.get('rawData', {})
    
    print(f"\n📦 IMPORT DATA:")
    print(f"Source: {import_data.get('source', 'N/A')}")
    print(f"Extracted at: {import_data.get('extractedAt', 'N/A')}")
    
    # Check house rules in raw data
    raw_house_rules = raw_data.get('house_rules', [])
    print(f"House rules in raw data: {len(raw_house_rules)}")
    for i, rule in enumerate(raw_house_rules[:5]):
        if isinstance(rule, dict):
            print(f"  {i+1}. {rule.get('title', 'N/A')}: {rule.get('description', 'N/A')[:50]}...")
        else:
            print(f"  {i+1}. {rule}")
    
    # Check extracted data in raw data
    extracted_data = raw_data.get('extracted', {})
    if extracted_data:
        extracted_rules = extracted_data.get('house_rules', [])
        print(f"House rules in extracted data: {len(extracted_rules)}")
        for i, rule in enumerate(extracted_rules[:5]):
            if isinstance(rule, dict):
                print(f"  {i+1}. {rule.get('title', 'N/A')}: {rule.get('description', 'N/A')[:50]}...")
            else:
                print(f"  {i+1}. {rule}")
    
    # Query knowledge items
    print(f"\n🧠 KNOWLEDGE ITEMS:")
    print("-" * 40)
    
    knowledge_items = list_knowledge_items_by_property(property_id)
    print(f"Total knowledge items: {len(knowledge_items)}")
    
    # Group by type
    by_type = {}
    for item in knowledge_items:
        item_type = item.get('type', 'unknown')
        if item_type not in by_type:
            by_type[item_type] = []
        by_type[item_type].append(item)
    
    for item_type, items in by_type.items():
        print(f"\n{item_type.upper()}: {len(items)} items")
        for i, item in enumerate(items[:3]):  # Show first 3 of each type
            status = item.get('status', 'N/A')
            content = item.get('content', 'N/A')[:50]
            print(f"  {i+1}. [{status}] {content}...")
    
    # Check setup progress
    setup_progress = property_data.get('setupProgress', {})
    print(f"\n⚙️ SETUP PROGRESS:")
    for step, completed in setup_progress.items():
        print(f"  {step}: {'✅' if completed else '❌'}")
    
    print("\n" + "=" * 80)
    print("🎯 SUMMARY:")
    print(f"- Property has {len(house_rules)} house rules in main data")
    print(f"- Raw data has {len(raw_house_rules)} house rules")
    print(f"- Knowledge items: {len(knowledge_items)} total")
    print(f"- Rule knowledge items: {len(by_type.get('rule', []))}")
    print(f"- Basic amenities: {len(basic_amenities)}")
    print(f"- Appliances: {len(appliances)}")

if __name__ == "__main__":
    property_id = "7a7edc31-ca3e-4707-a8b5-4ca7d31ac026"
    query_property_data(property_id)
