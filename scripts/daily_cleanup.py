#!/usr/bin/env python3
"""
Daily cleanup script for Concierge system.
This script can be run via cron job to automatically clean up expired data.

Usage:
    python scripts/daily_cleanup.py

Cron job example (runs daily at 2 AM):
    0 2 * * * cd /path/to/concierge && python scripts/daily_cleanup.py
"""

import sys
import os
import logging
from datetime import datetime, timezone

# Add the parent directory to the Python path so we can import concierge modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/daily_cleanup.log', mode='a'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Run daily cleanup tasks."""
    try:
        logger.info("=" * 50)
        logger.info("Starting daily cleanup script")
        logger.info(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
        
        # Import cleanup functions
        from concierge.utils.firestore_client import perform_daily_cleanup
        
        # Perform cleanup
        cleanup_results = perform_daily_cleanup()
        
        # Log results
        logger.info("Cleanup completed successfully:")
        logger.info(f"  - Expired magic links cleaned: {cleanup_results['expired_magic_links']}")
        logger.info(f"  - Expired temporary users cleaned: {cleanup_results['expired_temp_users']}")
        logger.info(f"  - Total items cleaned: {cleanup_results['total_cleaned']}")
        
        if cleanup_results['total_cleaned'] > 0:
            logger.info("✅ Cleanup completed with items removed")
        else:
            logger.info("✅ Cleanup completed - no expired items found")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Daily cleanup failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 