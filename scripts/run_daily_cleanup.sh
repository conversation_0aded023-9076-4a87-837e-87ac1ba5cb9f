#!/bin/bash
#
# Daily cleanup script wrapper for Guestrix system
# This script can be added to crontab for automated daily cleanup
#
# Usage:
#   ./scripts/run_daily_cleanup.sh
#
# Cron job example (runs daily at 2 AM):
#   0 2 * * * /path/to/concierge/scripts/run_daily_cleanup.sh
#

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_DIR"

# Create logs directory if it doesn't exist
mkdir -p logs

# Set environment variables if needed
export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"

# Run the cleanup script
echo "Starting daily cleanup at $(date)"
python3 scripts/daily_cleanup.py

# Check exit code
if [ $? -eq 0 ]; then
    echo "Daily cleanup completed successfully at $(date)"
else
    echo "Daily cleanup failed at $(date)"
    exit 1
fi 