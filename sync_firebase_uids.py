#!/usr/bin/env python3
"""
Script to synchronize Firebase Authentication UIDs with Firestore user UIDs.
This resolves the issue where a user has different UIDs in Firebase Auth vs Firestore.

This script will:
1. Find the Firebase Auth UID for phone +17738377523
2. Update the Firestore user to use the same UID
3. Mark the old user as inactive
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.firestore_client import get_user, find_user_by_phone, create_user, update_user
from concierge.utils.phone_utils import clean_phone_for_storage

def sync_firebase_uids():
    """Synchronize Firebase Auth and Firestore UIDs for the given phone number."""
    
    phone_number = "+17738377523"
    firebase_auth_uid = "8LnLzt6W9JM3sGFsPWvbGoVTrS32"  # From Firebase Authentication
    current_firestore_uid = "BqVhdHwSbuhLfEsOJIRf4XLYl9T2"  # Current Firestore UID
    
    print("=== Firebase UID Synchronization ===\n")
    
    # Step 1: Get current Firestore user data
    print(f"1. Getting current Firestore user data for UID: {current_firestore_uid}")
    current_user = get_user(current_firestore_uid)
    
    if not current_user:
        print(f"❌ ERROR: No user found with UID {current_firestore_uid}")
        return False
    
    print(f"✅ Found user: {current_user.get('displayName', 'Unknown')} ({current_user.get('phoneNumber', 'No phone')})")
    
    # Step 2: Check if Firebase Auth UID already exists in Firestore
    print(f"\n2. Checking if Firebase Auth UID already exists in Firestore: {firebase_auth_uid}")
    firebase_auth_user = get_user(firebase_auth_uid)
    
    if firebase_auth_user:
        print(f"⚠️  WARNING: User with Firebase Auth UID already exists in Firestore")
        print(f"   Name: {firebase_auth_user.get('displayName', 'Unknown')}")
        print(f"   Phone: {firebase_auth_user.get('phoneNumber', 'No phone')}")
        print(f"   Temporary: {firebase_auth_user.get('isTemporary', False)}")
        
        if firebase_auth_user.get('isTemporary', False):
            print(f"🗑️  Marking temporary user as inactive...")
            update_user(firebase_auth_uid, {
                'isActive': False,
                'deactivatedReason': 'Replaced by permanent user during UID sync',
                'replacedBy': current_firestore_uid
            })
            print(f"✅ Temporary user marked as inactive")
        else:
            print(f"❌ ERROR: Permanent user already exists with Firebase Auth UID. Manual intervention required.")
            return False
    else:
        print(f"✅ Firebase Auth UID is available in Firestore")
    
    # Step 3: Create new user with Firebase Auth UID
    print(f"\n3. Creating new user with Firebase Auth UID: {firebase_auth_uid}")
    
    # Prepare user data with new UID
    new_user_data = current_user.copy()
    new_user_data['uid'] = firebase_auth_uid
    new_user_data['id'] = firebase_auth_uid
    new_user_data['migratedFrom'] = current_firestore_uid
    new_user_data['migrationDate'] = 'auto-sync'
    
    # Remove any fields that shouldn't be copied
    fields_to_remove = ['createdAt', 'updatedAt']
    for field in fields_to_remove:
        new_user_data.pop(field, None)
    
    # Create the new user
    success = create_user(firebase_auth_uid, new_user_data)
    
    if not success:
        print(f"❌ ERROR: Failed to create user with Firebase Auth UID")
        return False
    
    print(f"✅ Successfully created user with Firebase Auth UID")
    
    # Step 4: Verify the new user
    print(f"\n4. Verifying new user creation...")
    new_user = get_user(firebase_auth_uid)
    
    if not new_user:
        print(f"❌ ERROR: Could not retrieve newly created user")
        return False
    
    print(f"✅ New user verified:")
    print(f"   UID: {new_user.get('uid', 'Not set')}")
    print(f"   Name: {new_user.get('displayName', 'Unknown')}")
    print(f"   Phone: {new_user.get('phoneNumber', 'No phone')}")
    print(f"   Role: {new_user.get('role', 'Not set')}")
    
    # Step 5: Mark old user as inactive (with confirmation)
    print(f"\n5. Ready to mark old user as inactive: {current_firestore_uid}")
    confirm = input("Are you sure you want to mark the old user as inactive? (yes/no): ")
    
    if confirm.lower() == 'yes':
        update_user(current_firestore_uid, {
            'isActive': False,
            'deactivatedReason': 'UID synchronized with Firebase Auth',
            'replacedBy': firebase_auth_uid,
            'phoneNumber': None  # Remove phone number so it doesn't conflict
        })
        print(f"✅ Old user marked as inactive")
        
        # Verify the update
        old_user_check = get_user(current_firestore_uid)
        if old_user_check and not old_user_check.get('isActive', True):
            print(f"✅ Old user deactivation confirmed")
        else:
            print(f"⚠️  WARNING: Old user may still be active")
    else:
        print(f"⚠️  Old user not deactivated. You now have duplicate users.")
        print(f"   You should manually deactivate the old user: {current_firestore_uid}")
    
    print(f"\n=== Synchronization Complete ===")
    print(f"✅ Firebase Auth UID: {firebase_auth_uid}")
    print(f"✅ Firestore UID: {firebase_auth_uid}")
    print(f"✅ Phone: {phone_number}")
    print(f"\nThe user should now be able to log in through both flows!")
    
    return True

if __name__ == "__main__":
    try:
        sync_firebase_uids()
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc() 