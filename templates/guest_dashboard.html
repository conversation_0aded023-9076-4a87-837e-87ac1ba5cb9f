    // Handle reservation selection
    function handleReservationSelection() {
        const selector = document.getElementById('reservation-selector');
        if (!selector) return;

        const selectedOption = selector.options[selector.selectedIndex];
        if (!selectedOption || !selectedOption.value || selectedOption.value === '') {
            console.log('No reservation selected');
            // Clear any existing property context
            if (window.dashboardState) {
                window.dashboardState.selectedReservation = null;
                window.dashboardState.propertyId = window.PROPERTY_ID || undefined;
            }
            return;
        }

        if (selectedOption.value === 'no-reservations') {
            console.log('No reservations available');
            return;
        }

        try {
            const reservationData = JSON.parse(selectedOption.getAttribute('data-reservation'));
            console.log('Selected reservation:', reservationData);

            // Update dashboard state with selected reservation
            if (window.dashboardState) {
                window.dashboardState.selectedReservation = reservationData;
                window.dashboardState.propertyId = reservationData.propertyId || reservationData.property_id || reservationData.id || window.PROPERTY_ID;
                
                // Update current reservation data
                window.dashboardState.currentReservation = reservationData;
                window.currentReservation = reservationData; // For backward compatibility
            }

            // Trigger property context update if function is available
            if (typeof window.updatePropertyContext === 'function') {
                window.updatePropertyContext(reservationData);
            }

            // Update the backend session context for all active connections
            updateBackendSessionContext(reservationData);

            // Add a system message to the chat about the selected property
            const propertyName = reservationData.propertyName || reservationData.property_name || 'your selected property';
            const systemMessage = `Now discussing ${propertyName}. How can I help you with your stay?`;
            
            if (typeof window.displayChatMessage === 'function') {
                window.displayChatMessage('system', systemMessage);
            }

        } catch (error) {
            console.error('Error parsing reservation data:', error);
        }
    }

    // Function to update backend session context when reservation changes
    function updateBackendSessionContext(reservationData) {
        console.log('Updating backend session context for reservation:', reservationData);

        const propertyId = reservationData.propertyId || reservationData.property_id || reservationData.id;
        const reservationId = reservationData.id || reservationData.reservationId || reservationData.ReservationId;
        
        // Get user data
        const userId = window.CURRENT_USER_ID;
        const guestName = window.dashboardState?.guestName || window.GUEST_NAME || "Guest";
        const phoneNumber = window.PHONE_NUMBER || "";

        if (!propertyId || !userId) {
            console.warn('Cannot update backend context: missing property ID or user ID');
            return;
        }

        // Create updated system prompt for the new reservation
        let updatedSystemPrompt = "";
        if (typeof window.createSharedSystemPrompt === 'function') {
            // Update dashboard state first so the system prompt uses the new data
            if (window.dashboardState) {
                window.dashboardState.propertyId = propertyId;
                window.dashboardState.currentReservation = reservationData;
            }
            
            updatedSystemPrompt = window.createSharedSystemPrompt();
            console.log('Created updated system prompt for new reservation, length:', updatedSystemPrompt.length);
        } else {
            // Fallback system prompt
            const propertyName = reservationData.propertyName || reservationData.property_name || 'this property';
            updatedSystemPrompt = `You are Staycee, a helpful AI concierge assistant for "${propertyName}". You are speaking with ${guestName}, a guest at this property.`;
        }

        // Update Socket.IO text chat session if connected
        if (window.socketIO && window.socketIO.connected) {
            console.log('Updating Socket.IO session context...');
            window.socketIO.emit('auth', {
                user_id: userId,
                property_id: propertyId,
                guest_name: guestName,
                reservation_id: reservationId,
                phone_number: phoneNumber,
                system_prompt: updatedSystemPrompt
            });
            
            console.log('Sent updated auth message to Socket.IO backend');
        }

        // Update Gemini Live session context if there's an active call
        if (window.currentCallState === 'active' && window.geminiSession) {
            console.log('Voice call is active - will update context on next message');
            // The voice system will use the updated dashboard state for the next interaction
        }

        // Store the updated context for voice calls
        if (window.dashboardState) {
            window.dashboardState.lastSystemPrompt = updatedSystemPrompt;
            window.dashboardState.lastContextUpdate = new Date().toISOString();
        }

        console.log('Backend session context update completed for property:', propertyId);
    } 