#!/usr/bin/env python3
"""
Test script to check for reservations with additional contacts
and verify they are preserved during sync.
"""

import sys
import os

# Add the concierge directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'concierge'))

def test_additional_contacts():
    """Test that additional contacts are preserved."""
    print("=" * 60)
    print("TESTING ADDITIONAL CONTACTS PRESERVATION")
    print("=" * 60)
    
    try:
        # Import required functions
        from concierge.utils.firestore_client import (
            get_firestore_client, 
            list_property_reservations,
            update_reservation
        )
        
        # Initialize Firestore
        db = get_firestore_client()
        if not db:
            print("Error: Could not initialize Firestore.")
            return
            
        print("Connected to Firestore successfully!")
        
        # Get reservations for the test property
        property_id = "1a344329-2670-4b34-a4f6-e28513a3200c"
        reservations = list_property_reservations(property_id)
        
        print(f"Found {len(reservations)} reservations for property {property_id}")
        
        # Check if any have additional contacts
        reservations_with_contacts = []
        for reservation in reservations:
            additional_contacts = (
                reservation.get('additional_contacts') or
                reservation.get('additionalContacts') or
                reservation.get('AdditionalContacts') or
                []
            )
            if additional_contacts:
                reservations_with_contacts.append(reservation)
                print(f"Reservation {reservation.get('id')} has {len(additional_contacts)} additional contacts")
        
        if not reservations_with_contacts:
            print("No reservations with additional contacts found.")
            print("Let's add some test additional contacts to a reservation...")
            
            # Add test contacts to the first reservation
            if reservations:
                test_reservation = reservations[0]
                reservation_id = test_reservation.get('id')
                
                test_contacts = [
                    {"name": "John Doe", "phone": "+1234567890"},
                    {"name": "Jane Smith", "phone": "+0987654321"}
                ]
                
                print(f"Adding test contacts to reservation {reservation_id}")
                update_data = {
                    'additional_contacts': test_contacts
                }
                
                from concierge.utils.firestore_client import update_reservation
                success = update_reservation(reservation_id, update_data)
                
                if success:
                    print(f"✅ Successfully added {len(test_contacts)} test contacts to reservation {reservation_id}")
                    print("Now let's run the sync to see if they are preserved...")
                    
                    # Run the sync
                    from concierge.utils.reservations import update_all_reservations
                    print("-" * 40)
                    update_all_reservations()
                    print("-" * 40)
                    
                    # Check if contacts were preserved
                    updated_reservations = list_property_reservations(property_id)
                    for res in updated_reservations:
                        if res.get('id') == reservation_id:
                            preserved_contacts = (
                                res.get('additional_contacts') or
                                res.get('additionalContacts') or
                                res.get('AdditionalContacts') or
                                []
                            )
                            if preserved_contacts:
                                print(f"✅ SUCCESS: {len(preserved_contacts)} additional contacts were preserved!")
                                for contact in preserved_contacts:
                                    print(f"   - {contact.get('name')}: {contact.get('phone')}")
                            else:
                                print("❌ FAILURE: Additional contacts were NOT preserved!")
                            break
                else:
                    print("❌ Failed to add test contacts")
        else:
            print(f"Found {len(reservations_with_contacts)} reservations with additional contacts.")
            print("Running sync to test preservation...")
            
            # Run the sync
            from concierge.utils.reservations import update_all_reservations
            print("-" * 40)
            update_all_reservations()
            print("-" * 40)
            
            # Check if contacts were preserved
            print("Checking if contacts were preserved after sync...")
            updated_reservations = list_property_reservations(property_id)
            
            for original_res in reservations_with_contacts:
                reservation_id = original_res.get('id')
                original_contacts = (
                    original_res.get('additional_contacts') or
                    original_res.get('additionalContacts') or
                    original_res.get('AdditionalContacts') or
                    []
                )
                
                # Find the updated reservation
                for updated_res in updated_reservations:
                    if updated_res.get('id') == reservation_id:
                        preserved_contacts = (
                            updated_res.get('additional_contacts') or
                            updated_res.get('additionalContacts') or
                            updated_res.get('AdditionalContacts') or
                            []
                        )
                        
                        if len(preserved_contacts) == len(original_contacts):
                            print(f"✅ SUCCESS: Reservation {reservation_id} preserved {len(preserved_contacts)} contacts")
                        else:
                            print(f"❌ FAILURE: Reservation {reservation_id} lost contacts! Had {len(original_contacts)}, now has {len(preserved_contacts)}")
                        break
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_additional_contacts() 