#!/usr/bin/env python3
"""
Comprehensive test script for guest signup/login logic flows.
Tests all the authentication flows described in the requirements.
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, Optional

class AuthFlowTester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name: str, status: str, details: str = ""):
        """Log test results."""
        result = {
            'test': test_name,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        print(f"[{status}] {test_name}: {details}")
        
    def create_test_magic_link(self) -> Optional[str]:
        """Create a test magic link for testing."""
        try:
            response = self.session.get(f"{self.base_url}/magic/test")
            if response.status_code == 200:
                # Extract token from response
                if 'token=' in response.text:
                    token = response.text.split('token=')[1].split('&')[0].split('"')[0]
                    self.log_test("Create Test Magic Link", "PASS", f"Token: {token[:8]}...")
                    return token
            self.log_test("Create Test Magic Link", "FAIL", f"Status: {response.status_code}")
            return None
        except Exception as e:
            self.log_test("Create Test Magic Link", "ERROR", str(e))
            return None
    
    def test_initial_access_valid_session(self, token: str):
        """Test: Valid Session + Same Token → Dashboard"""
        try:
            # First access to establish session
            response = self.session.get(f"{self.base_url}/magic/{token}")
            
            if response.status_code == 200:
                if 'pin' in response.text.lower() or 'enter' in response.text.lower():
                    self.log_test("Initial Access - Valid Session", "PASS", "Redirected to PIN screen as expected")
                else:
                    self.log_test("Initial Access - Valid Session", "PARTIAL", "Unexpected response content")
            else:
                self.log_test("Initial Access - Valid Session", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Initial Access - Valid Session", "ERROR", str(e))
    
    def test_pin_screen_options(self, token: str):
        """Test: PIN Screen has all required options"""
        try:
            response = self.session.get(f"{self.base_url}/magic/{token}")
            
            if response.status_code == 200:
                content = response.text.lower()
                
                # Check for PIN entry
                has_pin_entry = 'pin' in content and ('input' in content or 'digit' in content)
                
                # Check for phone login option
                has_phone_option = 'phone number' in content and 'account' in content
                
                # Check for help text
                has_help = 'help' in content or 'trouble' in content or 'contact' in content
                
                if has_pin_entry and has_phone_option and has_help:
                    self.log_test("PIN Screen Options", "PASS", "All required options present")
                else:
                    missing = []
                    if not has_pin_entry: missing.append("PIN entry")
                    if not has_phone_option: missing.append("Phone login option")
                    if not has_help: missing.append("Help text")
                    self.log_test("PIN Screen Options", "PARTIAL", f"Missing: {', '.join(missing)}")
            else:
                self.log_test("PIN Screen Options", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("PIN Screen Options", "ERROR", str(e))
    
    def test_pin_entry_logic(self, token: str, test_pin: str = "1234"):
        """Test: PIN Entry Logic with different scenarios"""
        try:
            # Test PIN submission
            pin_data = {'last_4_digits': test_pin}
            response = self.session.post(f"{self.base_url}/magic/{token}/verify", data=pin_data)
            
            if response.status_code in [200, 302]:  # Success or redirect
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    if 'dashboard' in location:
                        self.log_test("PIN Entry Logic", "PASS", "Redirected to dashboard")
                    elif 'name' in location:
                        self.log_test("PIN Entry Logic", "PASS", "Redirected to name collection")
                    elif 'confirm' in location:
                        self.log_test("PIN Entry Logic", "PASS", "Redirected to user confirmation")
                    else:
                        self.log_test("PIN Entry Logic", "PARTIAL", f"Unexpected redirect: {location}")
                else:
                    content = response.text.lower()
                    if 'name' in content or 'dashboard' in content or 'confirm' in content:
                        self.log_test("PIN Entry Logic", "PASS", "Appropriate response received")
                    else:
                        self.log_test("PIN Entry Logic", "PARTIAL", "Unexpected response content")
            else:
                self.log_test("PIN Entry Logic", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("PIN Entry Logic", "ERROR", str(e))
    
    def test_phone_login_flow(self, token: str, test_phone: str = "+15551234567"):
        """Test: Phone Login Flow"""
        try:
            # Access phone login page
            response = self.session.get(f"{self.base_url}/magic/{token}/phone-login")
            
            if response.status_code == 200:
                content = response.text.lower()
                if 'phone' in content and 'number' in content:
                    self.log_test("Phone Login Flow - Access", "PASS", "Phone login page accessible")
                    
                    # Test phone submission
                    phone_data = {'phone_number': test_phone}
                    response = self.session.post(f"{self.base_url}/magic/{token}/phone-login", data=phone_data)
                    
                    if response.status_code in [200, 302]:
                        self.log_test("Phone Login Flow - Submit", "PASS", "Phone submission handled")
                    else:
                        self.log_test("Phone Login Flow - Submit", "FAIL", f"Status: {response.status_code}")
                else:
                    self.log_test("Phone Login Flow - Access", "FAIL", "Phone login form not found")
            else:
                self.log_test("Phone Login Flow - Access", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Phone Login Flow", "ERROR", str(e))
    
    def test_name_collection_flow(self, token: str):
        """Test: Name Collection Screen"""
        try:
            # Try to access name collection (might need to be in the right state)
            response = self.session.get(f"{self.base_url}/magic/{token}/name")
            
            if response.status_code == 200:
                content = response.text.lower()
                
                # Check for name input
                has_name_input = 'name' in content and 'input' in content
                
                # Check for phone input
                has_phone_input = 'phone' in content and 'optional' in content
                
                # Check for skip option
                has_skip_option = 'skip' in content
                
                if has_name_input and has_phone_input and has_skip_option:
                    self.log_test("Name Collection Flow", "PASS", "All required elements present")
                else:
                    missing = []
                    if not has_name_input: missing.append("Name input")
                    if not has_phone_input: missing.append("Phone input")
                    if not has_skip_option: missing.append("Skip option")
                    self.log_test("Name Collection Flow", "PARTIAL", f"Missing: {', '.join(missing)}")
            else:
                self.log_test("Name Collection Flow", "SKIP", f"Status: {response.status_code} (may need proper state)")
                
        except Exception as e:
            self.log_test("Name Collection Flow", "ERROR", str(e))
    
    def run_all_tests(self):
        """Run all authentication flow tests."""
        print("🧪 Starting Authentication Flow Tests")
        print("=" * 50)
        
        # Create test magic link
        token = self.create_test_magic_link()
        if not token:
            print("❌ Cannot proceed without test magic link")
            return
        
        # Run all tests
        self.test_initial_access_valid_session(token)
        self.test_pin_screen_options(token)
        self.test_pin_entry_logic(token)
        self.test_phone_login_flow(token)
        self.test_name_collection_flow(token)
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        partial = len([r for r in self.test_results if r['status'] == 'PARTIAL'])
        errors = len([r for r in self.test_results if r['status'] == 'ERROR'])
        skipped = len([r for r in self.test_results if r['status'] == 'SKIP'])
        
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⚠️  Partial: {partial}")
        print(f"💥 Errors: {errors}")
        print(f"⏭️  Skipped: {skipped}")
        
        print(f"\nTotal Tests: {len(self.test_results)}")
        
        if failed > 0 or errors > 0:
            print("\n🔍 ISSUES FOUND:")
            for result in self.test_results:
                if result['status'] in ['FAIL', 'ERROR']:
                    print(f"  - {result['test']}: {result['details']}")

if __name__ == "__main__":
    tester = AuthFlowTester()
    tester.run_all_tests()
