#!/usr/bin/env python3
"""
Test the comprehensive improvements to house rules and amenity extraction.
"""

import sys
import os
import time
import json

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def test_comprehensive_improvements():
    """Test comprehensive improvements on multiple fresh listing URLs."""
    
    # Fresh test URLs with different configurations
    test_listings = [
        {
            "url": "https://www.airbnb.com/rooms/700299802944952028",
            "name": "Saint Francis Townhouse (Previous Test)",
            "expected_improvements": [
                "Coffee should be basic amenity, not appliance",
                "No duplicate guest maximum rules",
                "Compiled 'Before you leave' instructions",
                "No conflicting party rules"
            ]
        },
        {
            "url": "https://www.airbnb.com/rooms/53339040",
            "name": "Fresh Test Property 1",
            "expected_improvements": [
                "Proper amenity classification",
                "Deduplicated rules",
                "Compiled checkout instructions"
            ]
        },
        {
            "url": "https://www.airbnb.com/rooms/20669368",
            "name": "Fresh Test Property 2", 
            "expected_improvements": [
                "Accurate rule extraction",
                "No misclassified amenities",
                "Clean rule descriptions"
            ]
        }
    ]
    
    print(f"🔍 TESTING COMPREHENSIVE IMPROVEMENTS WITH GEMINI VALIDATION")
    print("=" * 80)
    
    results = []
    
    for i, listing in enumerate(test_listings):
        print(f"\n📋 TESTING LISTING {i+1}/{len(test_listings)}")
        print(f"Name: {listing['name']}")
        print(f"URL: {listing['url']}")
        print("-" * 60)
        
        scraper = AirbnbScraper(use_selenium=True, headless=True)
        
        try:
            start_time = time.time()
            
            # Extract deep property data
            extracted_data = scraper.extract_deep_property_data(listing['url'])
            
            extraction_time = time.time() - start_time
            
            # Analyze results
            house_rules = extracted_data.get('house_rules', [])
            amenities = extracted_data.get('amenities', {'basic': [], 'appliances': []})
            basic_amenities = amenities.get('basic', [])
            appliances = amenities.get('appliances', [])
            
            print(f"✅ EXTRACTION COMPLETED in {extraction_time:.1f}s")
            print(f"House Rules: {len(house_rules)}")
            print(f"Basic Amenities: {len(basic_amenities)}")
            print(f"Appliances: {len(appliances)}")
            
            # Analyze house rules
            print(f"\n📊 HOUSE RULES ANALYSIS:")
            rule_types = {}
            guest_max_rules = []
            before_leave_rules = []
            party_rules = []
            
            for rule in house_rules:
                rule_type = rule.get('type', 'unknown')
                description = rule.get('description', '')
                
                # Count by type
                rule_types[rule_type] = rule_types.get(rule_type, 0) + 1
                
                # Check for specific issues
                if 'guest' in description.lower() and 'maximum' in description.lower():
                    guest_max_rules.append(description)
                if 'before you leave' in description.lower():
                    before_leave_rules.append(description)
                if 'parties' in description.lower() or 'events' in description.lower():
                    party_rules.append(description)
                
                print(f"  [{rule_type}] {description}")
            
            print(f"\nRule types: {rule_types}")
            print(f"Guest maximum rules: {len(guest_max_rules)}")
            print(f"Before you leave rules: {len(before_leave_rules)}")
            print(f"Party-related rules: {len(party_rules)}")
            
            # Analyze amenities
            print(f"\n📊 AMENITIES ANALYSIS:")
            
            # Check for common misclassifications
            coffee_in_appliances = any('coffee' in str(app).lower() and 'maker' not in str(app).lower() 
                                    for app in appliances)
            coffee_in_basic = any('coffee' in amenity.lower() and 'maker' not in amenity.lower() 
                                for amenity in basic_amenities)
            
            print(f"Coffee misclassification check:")
            print(f"  Coffee in appliances (BAD): {coffee_in_appliances}")
            print(f"  Coffee in basic amenities (GOOD): {coffee_in_basic}")
            
            # Show sample amenities
            print(f"\nBasic amenities (first 10):")
            for amenity in basic_amenities[:10]:
                print(f"  - {amenity}")
            
            print(f"\nAppliances (first 10):")
            for appliance in appliances[:10]:
                if isinstance(appliance, dict):
                    name = appliance.get('name', 'Unknown')
                    location = appliance.get('location', 'No location')
                    print(f"  - {name} ({location})")
                else:
                    print(f"  - {appliance}")
            
            # Quality assessment
            quality_score = 0
            quality_issues = []
            
            # Rule quality checks
            if len(guest_max_rules) <= 1:
                quality_score += 20
            else:
                quality_issues.append(f"Multiple guest maximum rules: {guest_max_rules}")
            
            if len(before_leave_rules) <= 1:
                quality_score += 20
            else:
                quality_issues.append(f"Multiple 'before you leave' rules (should be compiled): {before_leave_rules}")
            
            if len(party_rules) <= 1:
                quality_score += 20
            else:
                quality_issues.append(f"Multiple party rules (should be deduplicated): {party_rules}")
            
            # Amenity quality checks
            if not coffee_in_appliances:
                quality_score += 20
            else:
                quality_issues.append("Coffee misclassified as appliance")
            
            if len(house_rules) >= 8:
                quality_score += 20
            else:
                quality_issues.append(f"Low rule count: {len(house_rules)}")
            
            print(f"\n🎯 QUALITY ASSESSMENT:")
            print(f"Quality Score: {quality_score}/100")
            if quality_issues:
                print(f"Issues found:")
                for issue in quality_issues:
                    print(f"  ❌ {issue}")
            else:
                print(f"✅ No quality issues found!")
            
            # Store results
            result = {
                'listing': listing,
                'extraction_time': extraction_time,
                'house_rules_count': len(house_rules),
                'basic_amenities_count': len(basic_amenities),
                'appliances_count': len(appliances),
                'quality_score': quality_score,
                'quality_issues': quality_issues,
                'guest_max_rules': len(guest_max_rules),
                'before_leave_rules': len(before_leave_rules),
                'party_rules': len(party_rules),
                'coffee_misclassified': coffee_in_appliances,
                'house_rules': house_rules,
                'basic_amenities': basic_amenities[:20],  # First 20 for analysis
                'appliances': appliances[:20]  # First 20 for analysis
            }
            results.append(result)
            
        except Exception as e:
            print(f"❌ Error extracting from {listing['name']}: {e}")
            import traceback
            traceback.print_exc()
            
            result = {
                'listing': listing,
                'error': str(e),
                'quality_score': 0
            }
            results.append(result)
        
        finally:
            # Clean up
            if hasattr(scraper, 'driver') and scraper.driver:
                try:
                    scraper.driver.quit()
                except:
                    pass
        
        print(f"\n" + "="*60)
    
    # Overall summary
    print(f"\n📊 OVERALL IMPROVEMENT SUMMARY:")
    print("=" * 80)
    
    successful_extractions = [r for r in results if 'error' not in r]
    failed_extractions = [r for r in results if 'error' in r]
    
    print(f"Successful extractions: {len(successful_extractions)}/{len(results)}")
    print(f"Failed extractions: {len(failed_extractions)}")
    
    if successful_extractions:
        avg_quality = sum(r['quality_score'] for r in successful_extractions) / len(successful_extractions)
        avg_rules = sum(r['house_rules_count'] for r in successful_extractions) / len(successful_extractions)
        avg_time = sum(r['extraction_time'] for r in successful_extractions) / len(successful_extractions)
        
        # Count improvements
        no_coffee_misclass = sum(1 for r in successful_extractions if not r.get('coffee_misclassified', True))
        single_guest_max = sum(1 for r in successful_extractions if r.get('guest_max_rules', 2) <= 1)
        compiled_before_leave = sum(1 for r in successful_extractions if r.get('before_leave_rules', 2) <= 1)
        clean_party_rules = sum(1 for r in successful_extractions if r.get('party_rules', 2) <= 1)
        
        print(f"\n🎯 IMPROVEMENT METRICS:")
        print(f"Average quality score: {avg_quality:.1f}/100")
        print(f"Average rules per listing: {avg_rules:.1f}")
        print(f"Average extraction time: {avg_time:.1f}s")
        print(f"\n✅ SPECIFIC IMPROVEMENTS:")
        print(f"Coffee properly classified: {no_coffee_misclass}/{len(successful_extractions)} ({no_coffee_misclass/len(successful_extractions)*100:.1f}%)")
        print(f"Single guest maximum rule: {single_guest_max}/{len(successful_extractions)} ({single_guest_max/len(successful_extractions)*100:.1f}%)")
        print(f"Compiled 'before you leave': {compiled_before_leave}/{len(successful_extractions)} ({compiled_before_leave/len(successful_extractions)*100:.1f}%)")
        print(f"Clean party rules: {clean_party_rules}/{len(successful_extractions)} ({clean_party_rules/len(successful_extractions)*100:.1f}%)")
        
        print(f"\nDetailed results:")
        for i, result in enumerate(successful_extractions):
            name = result['listing']['name']
            quality = result['quality_score']
            rules = result['house_rules_count']
            issues = len(result.get('quality_issues', []))
            print(f"  {i+1}. {name}: {quality}/100 quality, {rules} rules, {issues} issues")
    
    # Save results
    with open('comprehensive_improvement_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print(f"\n💾 Results saved to comprehensive_improvement_results.json")
    
    # Determine overall success
    overall_success = (len(successful_extractions) >= 2 and 
                      avg_quality >= 80 and 
                      no_coffee_misclass >= len(successful_extractions) * 0.8)
    
    return overall_success

if __name__ == "__main__":
    success = test_comprehensive_improvements()
    if success:
        print(f"\n🎉 COMPREHENSIVE IMPROVEMENTS TEST PASSED!")
    else:
        print(f"\n❌ COMPREHENSIVE IMPROVEMENTS TEST NEEDS MORE WORK")
    
    sys.exit(0 if success else 1)
