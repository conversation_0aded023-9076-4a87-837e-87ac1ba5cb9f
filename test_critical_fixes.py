#!/usr/bin/env python3
"""
Test the critical fixes for JavaScript handling and coffee classification.
"""

import sys
import os
import time

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import Airbnb<PERSON><PERSON>raper

def test_critical_fixes():
    """Test critical fixes on a single listing."""
    
    # Test URL that previously had issues
    test_url = "https://www.airbnb.com/rooms/700299802944952028"
    
    print(f"🔍 TESTING CRITICAL FIXES")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        start_time = time.time()
        
        # Extract deep property data
        extracted_data = scraper.extract_deep_property_data(test_url)
        
        extraction_time = time.time() - start_time
        
        # Analyze results
        house_rules = extracted_data.get('house_rules', [])
        amenities = extracted_data.get('amenities', {'basic': [], 'appliances': []})
        basic_amenities = amenities.get('basic', [])
        appliances = amenities.get('appliances', [])
        
        print(f"✅ EXTRACTION COMPLETED in {extraction_time:.1f}s")
        print(f"House Rules: {len(house_rules)}")
        print(f"Basic Amenities: {len(basic_amenities)}")
        print(f"Appliances: {len(appliances)}")
        
        # Test 1: Coffee Classification Fix
        print(f"\n🔍 TEST 1: COFFEE CLASSIFICATION")
        coffee_in_appliances = []
        coffee_in_basic = []
        
        for appliance in appliances:
            if isinstance(appliance, dict):
                name = appliance.get('name', '')
                if 'coffee' in name.lower() and 'maker' not in name.lower():
                    coffee_in_appliances.append(name)
            elif isinstance(appliance, str) and 'coffee' in appliance.lower() and 'maker' not in appliance.lower():
                coffee_in_appliances.append(appliance)
        
        for amenity in basic_amenities:
            if 'coffee' in amenity.lower() and 'maker' not in amenity.lower():
                coffee_in_basic.append(amenity)
        
        print(f"Coffee items in appliances (should be empty): {coffee_in_appliances}")
        print(f"Coffee items in basic amenities (should have coffee): {coffee_in_basic}")
        
        coffee_fix_success = len(coffee_in_appliances) == 0 and len(coffee_in_basic) > 0
        print(f"Coffee classification fix: {'✅ PASSED' if coffee_fix_success else '❌ FAILED'}")
        
        # Test 2: House Rules Extraction
        print(f"\n🔍 TEST 2: HOUSE RULES EXTRACTION")
        print(f"Rules extracted: {len(house_rules)}")
        
        if house_rules:
            print(f"Sample rules:")
            for i, rule in enumerate(house_rules[:5]):
                rule_type = rule.get('type', 'unknown')
                description = rule.get('description', 'N/A')
                print(f"  {i+1}. [{rule_type}] {description}")
        else:
            print(f"❌ No house rules extracted")
        
        rules_fix_success = len(house_rules) >= 5
        print(f"House rules extraction fix: {'✅ PASSED' if rules_fix_success else '❌ FAILED'}")
        
        # Test 3: JavaScript Loading
        print(f"\n🔍 TEST 3: JAVASCRIPT LOADING")
        description = extracted_data.get('description', '')
        has_js_warning = 'javascript enabled' in description.lower()
        
        print(f"Description length: {len(description)} characters")
        print(f"JavaScript warning present: {has_js_warning}")
        
        js_fix_success = not has_js_warning and len(description) > 100
        print(f"JavaScript loading fix: {'✅ PASSED' if js_fix_success else '❌ FAILED'}")
        
        # Test 4: Amenity Quality
        print(f"\n🔍 TEST 4: AMENITY QUALITY")
        total_amenities = len(basic_amenities) + len(appliances)
        
        # Check for duplicates
        basic_lower = [a.lower() for a in basic_amenities]
        basic_duplicates = len(basic_amenities) - len(set(basic_lower))
        
        appliance_names = []
        for app in appliances:
            if isinstance(app, dict):
                appliance_names.append(app.get('name', '').lower())
            else:
                appliance_names.append(str(app).lower())
        appliance_duplicates = len(appliances) - len(set(appliance_names))
        
        print(f"Total amenities: {total_amenities}")
        print(f"Basic amenity duplicates: {basic_duplicates}")
        print(f"Appliance duplicates: {appliance_duplicates}")
        
        amenity_quality_success = total_amenities >= 30 and basic_duplicates == 0 and appliance_duplicates == 0
        print(f"Amenity quality fix: {'✅ PASSED' if amenity_quality_success else '❌ FAILED'}")
        
        # Overall Assessment
        print(f"\n📊 OVERALL ASSESSMENT:")
        print("=" * 60)
        
        fixes_passed = sum([coffee_fix_success, rules_fix_success, js_fix_success, amenity_quality_success])
        total_fixes = 4
        
        print(f"Fixes passed: {fixes_passed}/{total_fixes}")
        print(f"Success rate: {fixes_passed/total_fixes*100:.1f}%")
        
        if fixes_passed >= 3:
            print(f"🎉 CRITICAL FIXES TEST PASSED!")
            return True
        else:
            print(f"❌ CRITICAL FIXES TEST FAILED - Need more work")
            return False
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    success = test_critical_fixes()
    sys.exit(0 if success else 1)
