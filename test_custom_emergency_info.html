<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Emergency Information Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'persian-green': '#00A693',
                        'saffron': '#F4C430',
                        'dark-purple': '#2D1B69'
                    }
                }
            }
        }
    </script>
    <style>
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #00A693;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-dark-purple mb-6">Custom Emergency Information Test</h1>
        
        <!-- Test Container -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div id="emergency-step-content">
                <!-- Content will be loaded here -->
            </div>
            
            <!-- Test Controls -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-4">Test Controls</h3>
                <div class="flex flex-wrap gap-4">
                    <button onclick="loadEmergencyStep()" class="bg-persian-green text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Load Emergency Step
                    </button>
                    <button onclick="testAddCustomItem()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Add Custom Item
                    </button>
                    <button onclick="testSaveEmergencyInfo()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Test Save
                    </button>
                    <button onclick="showTestData()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Show Data
                    </button>
                    <button onclick="testValidation()" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Test Validation
                    </button>
                </div>
            </div>
            
            <!-- Test Results -->
            <div id="test-results" class="mt-4 p-4 bg-blue-50 rounded-lg hidden">
                <h3 class="font-semibold mb-2">Test Results</h3>
                <pre id="test-output" class="text-sm bg-white p-2 rounded border overflow-auto max-h-64"></pre>
            </div>
        </div>
    </div>

    <script>
        // Mock property setup modal class for testing
        class MockPropertySetupModal {
            constructor() {
                this.propertyData = {
                    emergencyInfo: [
                        {
                            id: 'custom_existing',
                            title: 'Existing Custom Item',
                            instructions: 'This is an existing custom emergency item',
                            location: 'Test location',
                            enabled: true,
                            type: 'custom'
                        }
                    ],
                    importData: {
                        rawData: {
                            extracted: {
                                safety_info: [
                                    {
                                        title: 'Smoke Detector',
                                        description: 'Smoke detector is installed in the living room',
                                        location: 'Living room ceiling'
                                    }
                                ]
                            }
                        }
                    }
                };
                this.setupData = {};
                this.currentEmergencyInfo = [];
            }

            getDefaultEmergencyInfo() {
                return [
                    {
                        id: 'fire_emergency',
                        title: 'Fire Emergency',
                        instructions: 'Call 911 immediately. Exit the building using the nearest exit.',
                        location: '',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'first_aid',
                        title: 'First Aid Kit',
                        instructions: 'Basic first aid supplies for minor injuries.',
                        location: 'First aid kit location: ',
                        enabled: false,
                        type: 'default'
                    }
                ];
            }

            getImportedEmergencyInfo() {
                const importedSafety = this.propertyData.importData?.rawData?.extracted?.safety_info || [];
                return importedSafety.map((item, index) => ({
                    id: `imported_${index}`,
                    title: item.title || 'Safety Information',
                    instructions: item.description || item.instructions || '',
                    location: item.location || '',
                    enabled: true,
                    type: 'imported'
                }));
            }

            mergeEmergencyInfo(defaultInfo, importedInfo) {
                const merged = [...importedInfo];
                
                // Add any existing custom items from previous saves
                const existingCustomItems = this.propertyData.emergencyInfo?.filter(item => item.type === 'custom') || [];
                merged.push(...existingCustomItems);
                
                // Add default info that doesn't conflict
                defaultInfo.forEach(defaultItem => {
                    const hasConflict = importedInfo.some(imported => 
                        imported.title.toLowerCase().includes(defaultItem.title.toLowerCase()) ||
                        defaultItem.title.toLowerCase().includes(imported.title.toLowerCase())
                    );
                    
                    if (!hasConflict) {
                        merged.push(defaultItem);
                    }
                });
                
                return merged;
            }

            renderEmergencyInformation(emergencyInfo) {
                const container = document.getElementById('emergency-info-content');
                if (!container) return;

                this.currentEmergencyInfo = emergencyInfo;

                let html = '';

                // Add "Add Custom Emergency Item" button at the top
                html += `
                    <div class="mb-6">
                        <button onclick="testModal.addCustomEmergencyItem()" 
                                class="inline-flex items-center px-4 py-2 border border-persian-green text-persian-green rounded-lg hover:bg-persian-green hover:text-white transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Add Custom Emergency Item
                        </button>
                    </div>
                `;

                html += '<div class="space-y-4" id="emergency-items-container">';

                emergencyInfo.forEach((info, index) => {
                    const isImported = info.type === 'imported';
                    const isCustom = info.type === 'custom';
                    const sourceClass = isImported ? 'bg-blue-100 text-blue-800' : 
                                       isCustom ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
                    const sourceLabel = isImported ? 'Imported' : isCustom ? 'Custom' : 'Default';
                    const borderClass = isImported ? 'border-blue-200 bg-blue-50' : 
                                       isCustom ? 'border-green-200 bg-green-50' : 'border-gray-200';

                    html += `
                        <div class="border rounded-lg p-4 ${borderClass}" data-emergency-index="${index}">
                            <div class="flex items-start space-x-3">
                                <input type="checkbox"
                                       id="emergency_${index}"
                                       data-emergency-id="${info.id}"
                                       ${info.enabled ? 'checked' : ''}
                                       class="mt-1 h-4 w-4 text-persian-green border-gray-300 rounded focus:ring-persian-green">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-2">
                                            ${isCustom ? `
                                                <input type="text"
                                                       id="emergency_title_${index}"
                                                       value="${info.title}"
                                                       class="font-medium text-gray-900 bg-transparent border-none p-0 focus:ring-0 focus:border-b focus:border-persian-green"
                                                       placeholder="Emergency Title">
                                            ` : `
                                                <label for="emergency_${index}" class="font-medium text-gray-900 cursor-pointer">
                                                    ${info.title}
                                                </label>
                                            `}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${sourceClass}">
                                                ${sourceLabel}
                                            </span>
                                        </div>
                                        ${isCustom ? `
                                            <button onclick="testModal.removeCustomEmergencyItem(${index})"
                                                    class="text-red-600 hover:text-red-800 p-1"
                                                    title="Remove custom item">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                    
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                                            <textarea id="emergency_instructions_${index}"
                                                      class="w-full p-2 border border-gray-300 rounded-md text-sm resize-none"
                                                      rows="3"
                                                      placeholder="Enter emergency instructions...">${info.instructions}</textarea>
                                        </div>
                                        
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Location/Additional Info</label>
                                            <input type="text"
                                                   id="emergency_location_${index}"
                                                   class="w-full p-2 border border-gray-300 rounded-md text-sm"
                                                   placeholder="Enter location or additional information..."
                                                   value="${info.location || ''}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';

                container.innerHTML = html;
            }

            addCustomEmergencyItem() {
                console.log('Adding custom emergency item...');
                
                const newCustomItem = {
                    id: `custom_${Date.now()}`,
                    title: '',
                    instructions: '',
                    location: '',
                    enabled: true,
                    type: 'custom'
                };
                
                if (!this.currentEmergencyInfo) {
                    this.currentEmergencyInfo = [];
                }
                this.currentEmergencyInfo.push(newCustomItem);
                
                this.renderEmergencyInformation(this.currentEmergencyInfo);
                
                const newIndex = this.currentEmergencyInfo.length - 1;
                setTimeout(() => {
                    const titleInput = document.getElementById(`emergency_title_${newIndex}`);
                    if (titleInput) {
                        titleInput.focus();
                        titleInput.select();
                    }
                }, 100);
            }

            removeCustomEmergencyItem(index) {
                console.log(`Removing custom emergency item at index ${index}...`);
                
                if (!this.currentEmergencyInfo || index < 0 || index >= this.currentEmergencyInfo.length) {
                    console.error('Invalid index for removing emergency item');
                    return;
                }
                
                const item = this.currentEmergencyInfo[index];
                
                if (item.type !== 'custom') {
                    console.error('Can only remove custom emergency items');
                    return;
                }
                
                if (item.title.trim() || item.instructions.trim()) {
                    if (!confirm(`Are you sure you want to remove "${item.title || 'this custom emergency item'}"?`)) {
                        return;
                    }
                }
                
                this.currentEmergencyInfo.splice(index, 1);
                this.renderEmergencyInformation(this.currentEmergencyInfo);
            }

            async loadEmergencyInformation() {
                const defaultEmergencyInfo = this.getDefaultEmergencyInfo();
                const importedEmergencyInfo = this.getImportedEmergencyInfo();
                const allEmergencyInfo = this.mergeEmergencyInfo(defaultEmergencyInfo, importedEmergencyInfo);
                this.renderEmergencyInformation(allEmergencyInfo);
            }

            saveEmergencyInformation() {
                const allEmergencyInfo = [];
                const validationErrors = [];
                const container = document.getElementById('emergency-info-content');

                if (container) {
                    const checkboxes = container.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach((checkbox, index) => {
                        if (checkbox.checked) {
                            const instructionsTextarea = document.getElementById(`emergency_instructions_${index}`);
                            const locationInput = document.getElementById(`emergency_location_${index}`);
                            
                            const titleInput = document.getElementById(`emergency_title_${index}`);
                            const titleElement = document.querySelector(`label[for="emergency_${index}"]`);
                            
                            const title = titleInput ? titleInput.value.trim() : 
                                         titleElement ? titleElement.textContent.trim() : '';
                            const instructions = instructionsTextarea ? instructionsTextarea.value.trim() : '';
                            
                            if (!title) {
                                validationErrors.push(`Emergency item ${index + 1} is enabled but missing title`);
                            }
                            
                            if (!instructions) {
                                validationErrors.push(`"${title || 'Emergency item'}" is enabled but missing instructions`);
                            }
                            
                            const originalItem = this.currentEmergencyInfo && this.currentEmergencyInfo[index];
                            const itemType = originalItem ? originalItem.type : 'default';
                            
                            const emergencyInfo = {
                                id: checkbox.dataset.emergencyId || `emergency_${index}`,
                                title: title,
                                instructions: instructions,
                                location: locationInput ? locationInput.value.trim() : '',
                                enabled: true,
                                type: itemType
                            };

                            allEmergencyInfo.push(emergencyInfo);
                        }
                    });
                }

                this.setupData.emergencyInfo = allEmergencyInfo;
                
                return {
                    success: validationErrors.length === 0,
                    data: allEmergencyInfo,
                    errors: validationErrors
                };
            }
        }

        // Global test instance
        let testModal = new MockPropertySetupModal();

        function loadEmergencyStep() {
            testModal.loadEmergencyInformation();
        }

        function testAddCustomItem() {
            testModal.addCustomEmergencyItem();
            showTestResults('Added Custom Item', 'Custom emergency item added successfully');
        }

        function testSaveEmergencyInfo() {
            const result = testModal.saveEmergencyInformation();
            showTestResults('Save Emergency Information', result);
        }

        function showTestData() {
            const testData = {
                currentEmergencyInfo: testModal.currentEmergencyInfo,
                setupData: testModal.setupData
            };
            showTestResults('Test Data', testData);
        }

        function testValidation() {
            // Add a custom item with missing data to test validation
            testModal.addCustomEmergencyItem();
            
            setTimeout(() => {
                const result = testModal.saveEmergencyInformation();
                showTestResults('Validation Test', result);
            }, 500);
        }

        function showTestResults(title, data) {
            const resultsDiv = document.getElementById('test-results');
            const outputPre = document.getElementById('test-output');
            
            outputPre.textContent = `${title}:\n\n${JSON.stringify(data, null, 2)}`;
            resultsDiv.classList.remove('hidden');
        }

        // Auto-load on page load
        window.addEventListener('load', () => {
            loadEmergencyStep();
        });
    </script>
</body>
</html>
