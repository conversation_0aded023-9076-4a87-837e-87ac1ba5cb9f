#!/usr/bin/env python3
"""
Test script for improved property description generation.
Tests the enhanced description logic on fresh Airbnb listings from various US cities.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.airbnb_scraper import AirbnbScraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_description_generation():
    """Test description generation logic with sample text"""

    # Sample description texts to test the compilation logic
    test_descriptions = [
        {
            "name": "Modern Downtown Loft",
            "text": "Experience the heart of Austin in this stunning modern loft located in the vibrant downtown district. The space features floor-to-ceiling windows with city views, exposed brick walls, and contemporary furnishings. Located just steps from the famous 6th Street entertainment district and within walking distance of the State Capitol. The loft offers a unique urban experience with easy access to Austin's best restaurants, live music venues, and cultural attractions. Perfect for exploring the city's renowned food scene and nightlife.",
            "expected_quality": "good"
        },
        {
            "name": "Generic Property",
            "text": "This is a great accommodation in a perfect location. Ideal for travelers looking for a comfortable place to stay. The property offers amazing amenities and wonderful features. Perfect for your stay.",
            "expected_quality": "poor"
        },
        {
            "name": "Historic Charleston Home",
            "text": "Step back in time in this beautifully restored 1850s Charleston single house in the heart of the French Quarter. Original hardwood floors, 12-foot ceilings, and period antiques create an authentic Southern charm. The home features a private courtyard garden with fountain and is located on a quiet cobblestone street lined with historic mansions. Walk to Rainbow Row, the City Market, and renowned restaurants like Husk and FIG. Experience Charleston's rich history while enjoying modern comforts.",
            "expected_quality": "good"
        },
        {
            "name": "Short Generic",
            "text": "Nice place. Good location. Comfortable accommodation.",
            "expected_quality": "poor"
        }
    ]

    scraper = AirbnbScraper()

    # Test Gemini availability
    try:
        from concierge.config import GEMINI_MODEL
        if GEMINI_MODEL:
            print("✅ Gemini model available")
        else:
            print("❌ Gemini model not available")
    except Exception as e:
        print(f"❌ Gemini import error: {e}")

    print("🏠 Testing Enhanced Property Description Generation")
    print("=" * 60)

    for i, test_case in enumerate(test_descriptions, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"Expected Quality: {test_case['expected_quality'].upper()}")
        print("-" * 40)

        try:
            # Test the description compilation logic directly
            print(f"🔍 Input text: {test_case['text'][:100]}...")
            compiled_description = scraper._compile_brief_description(test_case['text'])
            print(f"🔍 Raw result: '{compiled_description}'")

            if compiled_description:
                print(f"📝 Generated Description ({len(compiled_description.split())} words):")
                print(f"   {compiled_description}")

                # Quality assessment
                words = compiled_description.split()
                if len(words) >= 20 and len(words) <= 65:
                    print("✅ Description length: GOOD")
                else:
                    print(f"⚠️  Description length: {len(words)} words (target: 20-65)")

                # Check for generic phrases
                generic_phrases = [
                    "perfect for", "great location", "ideal for travelers",
                    "comfortable accommodation", "convenient location",
                    "amazing", "wonderful", "perfect"
                ]
                has_generic = any(phrase in compiled_description.lower() for phrase in generic_phrases)
                if has_generic:
                    print("⚠️  Contains generic phrases")
                else:
                    print("✅ No generic phrases detected")

                # Check if result matches expected quality
                if test_case['expected_quality'] == 'good' and compiled_description:
                    print("✅ Quality filter result: CORRECT (good content preserved)")
                elif test_case['expected_quality'] == 'poor' and not compiled_description:
                    print("✅ Quality filter result: CORRECT (poor content rejected)")
                else:
                    print("⚠️  Quality filter result: UNEXPECTED")

            else:
                print("❌ No description generated (rejected by quality filters)")
                if test_case['expected_quality'] == 'poor':
                    print("✅ Quality filter result: CORRECT (poor content rejected)")
                else:
                    print("⚠️  Quality filter result: UNEXPECTED (good content rejected)")

        except Exception as e:
            print(f"❌ Error processing description: {e}")

        print()
    
    print("🎯 Test Summary:")
    print("- Enhanced description generation focuses on unique property features")
    print("- Quality filters reject generic or meaningless content")
    print("- Descriptions should be 20-65 words and property-specific")
    print("- Empty descriptions are preferred over poor quality ones")

if __name__ == "__main__":
    test_description_generation()
