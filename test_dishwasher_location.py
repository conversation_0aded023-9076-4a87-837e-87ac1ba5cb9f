#!/usr/bin/env python3
"""
Test script for dishwasher location assignment.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.airbnb_scraper import AirbnbScraper
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_dishwasher_location():
    """Test dishwasher location assignment"""
    
    scraper = AirbnbScraper()
    
    print("🧪 Testing Dishwasher Location Assignment")
    print("=" * 50)
    
    # Test cases for appliance parsing
    test_appliances = [
        "Dishwasher",
        "dishwasher",
        "Built-in dishwasher",
        "Bosch dishwasher",
        "Kitchen dishwasher",
        "Microwave",
        "Coffee maker",
        "Toaster",
        "TV",
        "Hair dryer"
    ]
    
    for appliance in test_appliances:
        print(f"\n🔍 Testing: '{appliance}'")
        
        # Test the appliance parsing logic
        parsed = scraper._parse_appliance_info(appliance)
        
        print(f"   Name: {parsed['name']}")
        print(f"   Location: {parsed['location']}")
        print(f"   Brand: {parsed['brand']}")
        print(f"   Model: {parsed['model']}")
        
        # Check if dishwasher gets Kitchen location
        if 'dishwasher' in appliance.lower():
            if parsed['location'] == 'Kitchen':
                print("   ✅ Dishwasher correctly assigned Kitchen location")
            else:
                print(f"   ❌ Dishwasher location issue: got '{parsed['location']}', expected 'Kitchen'")
        elif appliance.lower() in ['microwave', 'coffee maker', 'toaster']:
            if parsed['location'] == 'Kitchen':
                print("   ✅ Kitchen appliance correctly assigned Kitchen location")
            else:
                print(f"   ❌ Kitchen appliance location issue: got '{parsed['location']}', expected 'Kitchen'")
        elif appliance.lower() in ['tv', 'hair dryer']:
            if parsed['location'] == '':
                print("   ✅ Non-kitchen appliance correctly has no location")
            else:
                print(f"   ❌ Non-kitchen appliance should have no location: got '{parsed['location']}'")

    print("\n" + "=" * 50)
    print("🎯 Test Summary:")
    print("- Dishwasher should get 'Kitchen' location")
    print("- Other kitchen appliances should get 'Kitchen' location")
    print("- Non-kitchen appliances should have empty location")

if __name__ == "__main__":
    test_dishwasher_location()
