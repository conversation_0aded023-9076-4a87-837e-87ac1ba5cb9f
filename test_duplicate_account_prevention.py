#!/usr/bin/env python3
"""
Test script to verify duplicate account prevention works correctly.
This simulates the scenario where an existing permanent user tries to log in
via Firebase authentication instead of the phone login flow.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.firestore_client import find_user_by_phone, get_user
from concierge.utils.phone_utils import clean_phone_for_storage

def test_duplicate_account_prevention():
    """Test the duplicate account prevention logic."""
    
    print("=== Testing Duplicate Account Prevention ===\n")
    
    # Test phone number from the logs
    test_phone = "+***********"
    clean_phone = clean_phone_for_storage(test_phone)
    
    print(f"Testing phone number: {test_phone}")
    print(f"Cleaned phone number: {clean_phone}")
    
    # Check if user exists by phone
    existing_user = find_user_by_phone(clean_phone)
    
    if existing_user:
        print(f"\n✓ Found existing user with phone {clean_phone}:")
        print(f"  - User ID: {existing_user.get('id') or existing_user.get('uid')}")
        print(f"  - Display Name: {existing_user.get('displayName', 'N/A')}")
        print(f"  - Role: {existing_user.get('role', 'N/A')}")
        print(f"  - Is Temporary: {existing_user.get('isTemporary', False)}")
        print(f"  - Phone Number: {existing_user.get('phoneNumber', 'N/A')}")
        
        if not existing_user.get('isTemporary', False):
            print(f"\n✓ This is a permanent user - duplicate account prevention should trigger")
            print(f"  - Any new Firebase UID with phone {clean_phone} should be blocked")
            print(f"  - User should be redirected to /auth/phone-login")
        else:
            print(f"\n⚠ This is a temporary user - duplicate prevention may not apply")
    else:
        print(f"\n✗ No existing user found with phone {clean_phone}")
        print(f"  - This suggests the phone number format may not match")
        print(f"  - Check how the existing user's phone number is stored")
    
    # Test with BqVhdHwSbuhLfEsOJIRf4XLYl9T2 directly
    print(f"\n=== Checking existing user BqVhdHwSbuhLfEsOJIRf4XLYl9T2 ===")
    existing_user_by_id = get_user("BqVhdHwSbuhLfEsOJIRf4XLYl9T2")
    
    if existing_user_by_id:
        print(f"✓ Found user BqVhdHwSbuhLfEsOJIRf4XLYl9T2:")
        print(f"  - Display Name: {existing_user_by_id.get('displayName', 'N/A')}")
        print(f"  - Role: {existing_user_by_id.get('role', 'N/A')}")
        print(f"  - Phone Number: {existing_user_by_id.get('phoneNumber', 'N/A')}")
        print(f"  - Is Temporary: {existing_user_by_id.get('isTemporary', False)}")
        
        stored_phone = existing_user_by_id.get('phoneNumber', '')
        if stored_phone:
            print(f"  - Stored phone matches test phone: {stored_phone == clean_phone}")
            print(f"  - Stored phone: '{stored_phone}'")
            print(f"  - Test phone: '{clean_phone}'")
        else:
            print(f"  - ⚠ No phone number stored for this user!")
    else:
        print(f"✗ User BqVhdHwSbuhLfEsOJIRf4XLYl9T2 not found")
    
    print(f"\n=== Test Summary ===")
    print(f"With the fix in place, when a user with phone {test_phone}:")
    print(f"1. Logs in via Firebase authentication")
    print(f"2. Firebase creates a new UID (like 8LnLzt6W9JM3sGFsPWvbGoVTrS32)")
    print(f"3. /auth/verify-token checks if phone {clean_phone} is already in use")
    print(f"4. If found, returns 409 Conflict with redirect to /auth/phone-login")
    print(f"5. JavaScript redirects user to proper login flow")
    print(f"6. No duplicate account is created")

if __name__ == "__main__":
    test_duplicate_account_prevention() 