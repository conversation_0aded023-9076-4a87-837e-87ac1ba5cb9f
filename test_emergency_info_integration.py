#!/usr/bin/env python3
"""
Test the Emergency Information step integration with the Property Setup modal.
"""

import sys
import os
import json
import time

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

def test_emergency_info_integration():
    """Test Emergency Information step integration."""
    
    print(f"🔍 TESTING EMERGENCY INFORMATION STEP INTEGRATION")
    print("=" * 80)
    
    # Test data structure
    test_emergency_info = [
        {
            "id": "fire_emergency",
            "title": "Fire Emergency",
            "instructions": "Call 911 immediately. Exit the building using the nearest exit. Do not use elevators.",
            "location": "",
            "enabled": True
        },
        {
            "id": "medical_emergency", 
            "title": "Medical Emergency",
            "instructions": "Call 911 for serious medical emergencies. For non-emergencies, contact local urgent care.",
            "location": "",
            "enabled": True
        },
        {
            "id": "gas_leak",
            "title": "Gas Leak", 
            "instructions": "Do not use electrical switches or open flames. Evacuate immediately and call gas company emergency line.",
            "location": "Gas shut-off valve location: Basement utility room, left wall",
            "enabled": True
        },
        {
            "id": "first_aid",
            "title": "First Aid Kit",
            "instructions": "Basic first aid supplies for minor injuries.",
            "location": "First aid kit location: Kitchen cabinet above sink",
            "enabled": True
        },
        {
            "id": "imported_0",
            "title": "Smoke Detector",
            "instructions": "Smoke detector is installed in the living room",
            "location": "Living room ceiling",
            "enabled": True
        }
    ]
    
    print(f"📊 TEST DATA STRUCTURE:")
    print(f"Emergency items: {len(test_emergency_info)}")
    print(f"Enabled items: {len([item for item in test_emergency_info if item.get('enabled')])}")
    
    for i, item in enumerate(test_emergency_info):
        print(f"  {i+1}. [{item['id']}] {item['title']} - {'✅ Enabled' if item.get('enabled') else '❌ Disabled'}")
    
    print(f"\n🔍 TESTING DATA VALIDATION:")
    
    # Test 1: Validate required fields
    validation_errors = []
    for item in test_emergency_info:
        if item.get('enabled', False):
            if not item.get('title', '').strip():
                validation_errors.append(f"Item {item.get('id', 'unknown')} missing title")
            if not item.get('instructions', '').strip():
                validation_errors.append(f"Item '{item.get('title', 'unknown')}' missing instructions")
    
    if validation_errors:
        print(f"❌ Validation errors found:")
        for error in validation_errors:
            print(f"  - {error}")
    else:
        print(f"✅ All enabled items have required fields")
    
    # Test 2: Test knowledge item creation format
    print(f"\n🔍 TESTING KNOWLEDGE ITEM FORMAT:")
    
    knowledge_items = []
    for item in test_emergency_info:
        if item.get('enabled', False):
            content = item.get('title', '')
            if item.get('instructions'):
                content += f"\n\nInstructions: {item['instructions']}"
            if item.get('location'):
                content += f"\n\nLocation: {item['location']}"
            
            knowledge_item = {
                'type': 'emergency',
                'tags': ['emergency', 'safety', 'setup_wizard'],
                'content': content,
                'status': 'approved',
                'source': 'property_setup'
            }
            knowledge_items.append(knowledge_item)
    
    print(f"Knowledge items to create: {len(knowledge_items)}")
    for i, item in enumerate(knowledge_items):
        content_preview = item['content'][:60] + "..." if len(item['content']) > 60 else item['content']
        print(f"  {i+1}. [{item['type']}] {content_preview}")
    
    # Test 3: Test API payload format
    print(f"\n🔍 TESTING API PAYLOAD FORMAT:")
    
    api_payload = {
        "step": 3,
        "data": {
            "emergencyInfo": test_emergency_info
        }
    }
    
    print(f"API payload structure:")
    print(f"  Step: {api_payload['step']}")
    print(f"  Emergency items: {len(api_payload['data']['emergencyInfo'])}")
    print(f"  Payload size: {len(json.dumps(api_payload))} characters")
    
    # Test 4: Test UI rendering data
    print(f"\n🔍 TESTING UI RENDERING:")
    
    imported_items = [item for item in test_emergency_info if item['id'].startswith('imported_')]
    default_items = [item for item in test_emergency_info if not item['id'].startswith('imported_')]
    
    print(f"UI rendering breakdown:")
    print(f"  Imported items: {len(imported_items)} (enabled by default)")
    print(f"  Default items: {len(default_items)} (disabled by default)")
    print(f"  Total enabled: {len([item for item in test_emergency_info if item.get('enabled')])}")
    
    # Test 5: Test edge cases
    print(f"\n🔍 TESTING EDGE CASES:")
    
    edge_cases = [
        {
            "case": "Empty instructions",
            "item": {"id": "test", "title": "Test", "instructions": "", "enabled": True},
            "should_fail": True
        },
        {
            "case": "Missing title",
            "item": {"id": "test", "title": "", "instructions": "Test instructions", "enabled": True},
            "should_fail": True
        },
        {
            "case": "Disabled item with missing data",
            "item": {"id": "test", "title": "", "instructions": "", "enabled": False},
            "should_fail": False
        },
        {
            "case": "Valid item with location",
            "item": {"id": "test", "title": "Test", "instructions": "Test instructions", "location": "Test location", "enabled": True},
            "should_fail": False
        }
    ]
    
    for edge_case in edge_cases:
        item = edge_case["item"]
        should_fail = edge_case["should_fail"]
        
        # Simulate validation
        has_error = False
        if item.get('enabled', False):
            if not item.get('title', '').strip() or not item.get('instructions', '').strip():
                has_error = True
        
        result = "❌ FAIL" if has_error else "✅ PASS"
        expected = "should fail" if should_fail else "should pass"
        correct = (has_error == should_fail)
        
        print(f"  {edge_case['case']}: {result} ({expected}) {'✅' if correct else '❌'}")
    
    # Overall assessment
    print(f"\n📊 OVERALL ASSESSMENT:")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 5
    
    # Test results
    if not validation_errors:
        tests_passed += 1
    if len(knowledge_items) == len([item for item in test_emergency_info if item.get('enabled')]):
        tests_passed += 1
    if api_payload['step'] == 3 and 'emergencyInfo' in api_payload['data']:
        tests_passed += 1
    if len(imported_items) + len(default_items) == len(test_emergency_info):
        tests_passed += 1
    
    # Edge case results
    edge_case_correct = sum(1 for case in edge_cases if 
                           (case["item"].get('enabled', False) and 
                            (not case["item"].get('title', '').strip() or not case["item"].get('instructions', '').strip())) == case["should_fail"])
    if edge_case_correct == len(edge_cases):
        tests_passed += 1
    
    success_rate = (tests_passed / total_tests) * 100
    
    print(f"Tests passed: {tests_passed}/{total_tests}")
    print(f"Success rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print(f"🎉 EMERGENCY INFORMATION INTEGRATION TEST PASSED!")
        return True
    else:
        print(f"❌ EMERGENCY INFORMATION INTEGRATION TEST FAILED")
        return False

if __name__ == "__main__":
    success = test_emergency_info_integration()
    sys.exit(0 if success else 1)
