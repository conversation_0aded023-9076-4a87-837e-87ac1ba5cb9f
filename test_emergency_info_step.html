<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Information Step Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'persian-green': '#00A693',
                        'saffron': '#F4C430',
                        'dark-purple': '#2D1B69'
                    }
                }
            }
        }
    </script>
    <style>
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #00A693;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-dark-purple mb-6">Emergency Information Step Test</h1>
        
        <!-- Test Container -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div id="emergency-step-content">
                <!-- Content will be loaded here -->
            </div>
            
            <!-- Test Controls -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-4">Test Controls</h3>
                <div class="flex space-x-4">
                    <button onclick="loadEmergencyStep()" class="bg-persian-green text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Load Emergency Step
                    </button>
                    <button onclick="testSaveEmergencyInfo()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Test Save
                    </button>
                    <button onclick="showTestData()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Show Test Data
                    </button>
                </div>
            </div>
            
            <!-- Test Results -->
            <div id="test-results" class="mt-4 p-4 bg-blue-50 rounded-lg hidden">
                <h3 class="font-semibold mb-2">Test Results</h3>
                <pre id="test-output" class="text-sm bg-white p-2 rounded border overflow-auto max-h-64"></pre>
            </div>
        </div>
    </div>

    <script>
        // Mock property setup modal class for testing
        class MockPropertySetupModal {
            constructor() {
                this.propertyData = {
                    importData: {
                        rawData: {
                            extracted: {
                                safety_info: [
                                    {
                                        title: 'Smoke Detector',
                                        description: 'Smoke detector is installed in the living room',
                                        location: 'Living room ceiling'
                                    },
                                    {
                                        title: 'Fire Extinguisher',
                                        description: 'Fire extinguisher available for emergencies',
                                        location: 'Kitchen cabinet under sink'
                                    }
                                ]
                            },
                            safety_info: [
                                {
                                    title: 'Carbon Monoxide Detector',
                                    instructions: 'CO detector installed and tested monthly',
                                    location: 'Hallway near bedrooms'
                                }
                            ]
                        }
                    }
                };
                this.setupData = {};
            }

            getDefaultEmergencyInfo() {
                return [
                    {
                        id: 'fire_emergency',
                        title: 'Fire Emergency',
                        instructions: 'Call 911 immediately. Exit the building using the nearest exit. Do not use elevators.',
                        location: '',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'medical_emergency',
                        title: 'Medical Emergency',
                        instructions: 'Call 911 for serious medical emergencies. For non-emergencies, contact local urgent care.',
                        location: '',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'gas_leak',
                        title: 'Gas Leak',
                        instructions: 'Do not use electrical switches or open flames. Evacuate immediately and call gas company emergency line.',
                        location: 'Gas shut-off valve location: ',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'first_aid',
                        title: 'First Aid Kit',
                        instructions: 'Basic first aid supplies for minor injuries.',
                        location: 'First aid kit location: ',
                        enabled: false,
                        type: 'default'
                    }
                ];
            }

            getImportedEmergencyInfo() {
                const importedSafety = this.propertyData.importData?.rawData?.extracted?.safety_info || [];
                const deepExtractedSafety = this.propertyData.importData?.rawData?.safety_info || [];
                const allImportedSafety = [...importedSafety, ...deepExtractedSafety];
                
                return allImportedSafety.map((item, index) => ({
                    id: `imported_${index}`,
                    title: item.title || item.type || 'Safety Information',
                    instructions: item.description || item.instructions || '',
                    location: item.location || '',
                    enabled: true,
                    type: 'imported'
                }));
            }

            mergeEmergencyInfo(defaultInfo, importedInfo) {
                const merged = [...importedInfo];
                
                defaultInfo.forEach(defaultItem => {
                    const hasConflict = importedInfo.some(imported => 
                        imported.title.toLowerCase().includes(defaultItem.title.toLowerCase()) ||
                        defaultItem.title.toLowerCase().includes(imported.title.toLowerCase())
                    );
                    
                    if (!hasConflict) {
                        merged.push(defaultItem);
                    }
                });
                
                return merged;
            }

            renderEmergencyInformation(emergencyInfo) {
                const container = document.getElementById('emergency-info-content');
                if (!container) return;

                let html = '<div class="space-y-4">';

                emergencyInfo.forEach((info, index) => {
                    const isImported = info.type === 'imported';
                    const sourceClass = isImported ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800';
                    const sourceLabel = isImported ? 'Imported' : 'Default';
                    const borderClass = isImported ? 'border-blue-200 bg-blue-50' : 'border-gray-200';

                    html += `
                        <div class="border rounded-lg p-4 ${borderClass}">
                            <div class="flex items-start space-x-3">
                                <input type="checkbox"
                                       id="emergency_${index}"
                                       data-emergency-id="${info.id}"
                                       ${info.enabled ? 'checked' : ''}
                                       class="mt-1 h-4 w-4 text-persian-green border-gray-300 rounded focus:ring-persian-green">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-3">
                                        <label for="emergency_${index}" class="font-medium text-gray-900 cursor-pointer">
                                            ${info.title}
                                        </label>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${sourceClass}">
                                            ${sourceLabel}
                                        </span>
                                    </div>
                                    
                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                                            <textarea id="emergency_instructions_${index}"
                                                      class="w-full p-2 border border-gray-300 rounded-md text-sm resize-none"
                                                      rows="3"
                                                      placeholder="Enter emergency instructions...">${info.instructions}</textarea>
                                        </div>
                                        
                                        ${info.location !== undefined ? `
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Location/Additional Info</label>
                                                <input type="text"
                                                       id="emergency_location_${index}"
                                                       class="w-full p-2 border border-gray-300 rounded-md text-sm"
                                                       placeholder="Enter location or additional information..."
                                                       value="${info.location}">
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';

                // Add help text
                html += `
                    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-info-circle text-blue-600 mt-0.5"></i>
                            <div class="text-sm text-blue-800">
                                <p class="font-medium mb-1">Emergency Information Tips:</p>
                                <ul class="list-disc list-inside space-y-1 text-blue-700">
                                    <li>Only enable information that applies to your property</li>
                                    <li>Customize instructions to be specific to your location</li>
                                    <li>Include exact locations for safety equipment and shut-offs</li>
                                    <li>Keep emergency contact information up to date</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `;

                container.innerHTML = html;
            }

            async loadEmergencyInformation() {
                const defaultEmergencyInfo = this.getDefaultEmergencyInfo();
                const importedEmergencyInfo = this.getImportedEmergencyInfo();
                const allEmergencyInfo = this.mergeEmergencyInfo(defaultEmergencyInfo, importedEmergencyInfo);
                this.renderEmergencyInformation(allEmergencyInfo);
            }

            saveEmergencyInformation() {
                const allEmergencyInfo = [];
                const container = document.getElementById('emergency-info-content');

                if (container) {
                    const checkboxes = container.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach((checkbox, index) => {
                        if (checkbox.checked) {
                            const instructionsTextarea = document.getElementById(`emergency_instructions_${index}`);
                            const locationInput = document.getElementById(`emergency_location_${index}`);
                            const titleElement = document.querySelector(`label[for="emergency_${index}"]`);
                            
                            const emergencyInfo = {
                                id: checkbox.dataset.emergencyId || `emergency_${index}`,
                                title: titleElement ? titleElement.textContent.trim() : '',
                                instructions: instructionsTextarea ? instructionsTextarea.value : '',
                                location: locationInput ? locationInput.value : '',
                                enabled: true
                            };

                            allEmergencyInfo.push(emergencyInfo);
                        }
                    });
                }

                this.setupData.emergencyInfo = allEmergencyInfo;
                return allEmergencyInfo;
            }
        }

        // Global test instance
        let testModal = new MockPropertySetupModal();

        function loadEmergencyStep() {
            const content = document.getElementById('emergency-step-content');
            content.innerHTML = `
                <div>
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold text-dark-purple mb-2">
                            <i class="fas fa-exclamation-triangle text-persian-green mr-2"></i>
                            Emergency Information
                        </h4>
                        <p class="text-gray-600">Provide essential emergency information for your guests. Enable and customize the information that applies to your property.</p>
                    </div>

                    <div id="emergency-info-content">
                        <div class="text-center py-8">
                            <div class="loading-spinner mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading emergency information...</p>
                        </div>
                    </div>
                </div>
            `;

            // Load emergency information after a short delay to show loading
            setTimeout(() => {
                testModal.loadEmergencyInformation();
            }, 1000);
        }

        function testSaveEmergencyInfo() {
            const savedData = testModal.saveEmergencyInformation();
            showTestResults('Saved Emergency Information', savedData);
        }

        function showTestData() {
            const testData = {
                propertyData: testModal.propertyData,
                setupData: testModal.setupData
            };
            showTestResults('Test Data', testData);
        }

        function showTestResults(title, data) {
            const resultsDiv = document.getElementById('test-results');
            const outputPre = document.getElementById('test-output');
            
            outputPre.textContent = `${title}:\n\n${JSON.stringify(data, null, 2)}`;
            resultsDiv.classList.remove('hidden');
        }

        // Auto-load on page load
        window.addEventListener('load', () => {
            loadEmergencyStep();
        });
    </script>
</body>
</html>
