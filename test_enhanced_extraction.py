#!/usr/bin/env python3
"""
Test the enhanced house rules extraction logic with the specific listing
that has the detailed modal content.
"""

import sys
import os
import time

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def test_enhanced_extraction():
    """Test the enhanced extraction logic."""
    
    test_url = "https://www.airbnb.com/rooms/700299802944952028"
    
    print(f"🔍 TESTING ENHANCED HOUSE RULES EXTRACTION")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    # Expected rules from the modal you provided
    expected_rules = [
        "Check-in after 3:00 PM",
        "Checkout before 11:00 AM", 
        "Self check-in with smart lock",
        "6 guests maximum",
        "Pets allowed",
        "Quiet hours 9:00 PM - 7:00 AM",
        "No parties or events",
        "No commercial photography", 
        "No smoking",
        "No smoking or vaping, no parties allowed",
        "Gather used towels",
        "Turn things off",
        "Lock up"
    ]
    
    print(f"📋 EXPECTED RULES ({len(expected_rules)}):")
    for i, rule in enumerate(expected_rules):
        print(f"  {i+1}. {rule}")
    
    print(f"\n🚀 STARTING EXTRACTION...")
    print("-" * 40)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        # Extract deep property data which includes house rules
        extracted_data = scraper.extract_deep_property_data(test_url)
        house_rules = extracted_data.get('house_rules', [])
        
        print(f"\n✅ EXTRACTION COMPLETED")
        print(f"Total rules extracted: {len(house_rules)}")
        print("-" * 40)
        
        # Analyze results
        extracted_descriptions = [rule['description'] for rule in house_rules]
        
        print(f"\n📊 EXTRACTED RULES ({len(house_rules)}):")
        for i, rule in enumerate(house_rules):
            rule_type = rule.get('type', 'unknown')
            description = rule.get('description', 'N/A')
            print(f"  {i+1}. [{rule_type}] {description}")
        
        # Check which expected rules were found
        print(f"\n🎯 MATCHING ANALYSIS:")
        print("-" * 40)
        
        found_rules = []
        missing_rules = []
        
        for expected in expected_rules:
            found = False
            for extracted in extracted_descriptions:
                # Check for exact match or partial match
                if (expected.lower() in extracted.lower() or 
                    extracted.lower() in expected.lower() or
                    # Check for key terms
                    any(term in extracted.lower() for term in expected.lower().split() if len(term) > 3)):
                    found_rules.append((expected, extracted))
                    found = True
                    break
            
            if not found:
                missing_rules.append(expected)
        
        print(f"✅ FOUND RULES ({len(found_rules)}/{len(expected_rules)}):")
        for expected, extracted in found_rules:
            print(f"  ✅ '{expected}' → '{extracted}'")
        
        print(f"\n❌ MISSING RULES ({len(missing_rules)}/{len(expected_rules)}):")
        for missing in missing_rules:
            print(f"  ❌ '{missing}'")
        
        # Calculate success rate
        success_rate = (len(found_rules) / len(expected_rules)) * 100
        print(f"\n📈 SUCCESS RATE: {success_rate:.1f}% ({len(found_rules)}/{len(expected_rules)})")
        
        # Additional analysis
        print(f"\n🔍 ADDITIONAL ANALYSIS:")
        print("-" * 40)
        
        # Group by type
        by_type = {}
        for rule in house_rules:
            rule_type = rule.get('type', 'unknown')
            if rule_type not in by_type:
                by_type[rule_type] = []
            by_type[rule_type].append(rule['description'])
        
        for rule_type, descriptions in by_type.items():
            print(f"{rule_type.upper()}: {len(descriptions)} rules")
            for desc in descriptions:
                print(f"  - {desc}")
        
        # Check for specific critical rules
        critical_rules = [
            "Quiet hours",
            "No parties", 
            "No smoking",
            "Commercial photography"
        ]
        
        print(f"\n🚨 CRITICAL RULES CHECK:")
        for critical in critical_rules:
            found = any(critical.lower() in rule.lower() for rule in extracted_descriptions)
            status = "✅ FOUND" if found else "❌ MISSING"
            print(f"  {status}: {critical}")
        
        return success_rate >= 80  # Consider success if we get 80%+ of expected rules
        
    except Exception as e:
        print(f"❌ Error during extraction: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    success = test_enhanced_extraction()
    if success:
        print(f"\n🎉 TEST PASSED: Enhanced extraction is working well!")
    else:
        print(f"\n❌ TEST FAILED: Need further improvements")
    
    sys.exit(0 if success else 1)
