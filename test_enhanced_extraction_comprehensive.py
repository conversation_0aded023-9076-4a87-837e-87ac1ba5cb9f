#!/usr/bin/env python3
"""
Comprehensive test of enhanced house rules extraction logic.
Tests with 10 fresh Austin Airbnb listings to ensure reliable results.
"""

import sys
import os
from datetime import datetime
import time

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbS<PERSON>raper

def test_comprehensive_extraction():
    """Test enhanced extraction logic with multiple Austin listings."""
    
    # Fresh Austin Airbnb listings
    test_listings = [
        "https://www.airbnb.com/rooms/5337141",
        "https://www.airbnb.com/rooms/5046776", 
        "https://www.airbnb.com/rooms/1188587149245653247",
        "https://www.airbnb.com/rooms/1221817044193581456",
        "https://www.airbnb.com/rooms/1305859865915531793",
        "https://www.airbnb.com/rooms/32866140",
        "https://www.airbnb.com/rooms/1244967849665604651",
        "https://www.airbnb.com/rooms/1080468382899410272",
        "https://www.airbnb.com/rooms/672627355132212425",
        "https://www.airbnb.com/rooms/1171696760116722623"
    ]
    
    print(f"🧪 COMPREHENSIVE ENHANCED EXTRACTION TEST")
    print(f"Testing {len(test_listings)} Austin Airbnb listings")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    results = []
    
    try:
        for i, url in enumerate(test_listings):
            print(f"\n🔍 TESTING LISTING {i+1}/10")
            print(f"URL: {url}")
            print("-" * 60)
            
            try:
                # Step 1: Extract listing data
                print("📋 Step 1: Extracting listing data...")
                listing_data = scraper.extract_listing_details(url)
                
                if not listing_data:
                    print("❌ Failed to extract listing data")
                    results.append({
                        'url': url,
                        'status': 'failed_listing',
                        'error': 'No listing data'
                    })
                    continue
                    
                print(f"✅ Listing: {listing_data.get('title', 'N/A')}")
                
                # Step 2: Extract deep property data
                print("🔍 Step 2: Extracting deep property data...")
                extracted_data = scraper.extract_deep_property_data(url)
                
                if not extracted_data:
                    print("❌ Failed to extract deep property data")
                    results.append({
                        'url': url,
                        'status': 'failed_extraction',
                        'listing_title': listing_data.get('title', 'N/A'),
                        'error': 'No deep data'
                    })
                    continue
                
                # Analyze results
                house_rules = extracted_data.get('house_rules', [])
                checkin_checkout = extracted_data.get('checkin_checkout', {})
                amenities = extracted_data.get('amenities', {})
                
                print(f"📊 EXTRACTION RESULTS:")
                print(f"  - House rules: {len(house_rules)}")
                print(f"  - Check-in/out data: {bool(checkin_checkout)}")
                print(f"  - Amenities: {len(amenities.get('basic', []))}")
                
                # Show house rules details
                if house_rules:
                    print(f"📜 HOUSE RULES FOUND:")
                    for j, rule in enumerate(house_rules):
                        title = rule.get('title', 'No title')
                        description = rule.get('description', 'No description')
                        print(f"  {j+1}. {title}")
                        print(f"     {description[:100]}{'...' if len(description) > 100 else ''}")
                else:
                    print("📜 No house rules found")
                
                # Check for invalid content
                invalid_rules = []
                for rule in house_rules:
                    desc = rule.get('description', '').lower()
                    # Check for reviews
                    if any(phrase in desc for phrase in ['we had', 'we loved', 'our stay', 'great stay']):
                        invalid_rules.append(('review', rule))
                    # Check for check-in times
                    elif any(phrase in desc for phrase in ['check-in after', 'check-out before']):
                        invalid_rules.append(('time', rule))
                    # Check for navigation
                    elif any(phrase in desc for phrase in ['support', 'help center', 'aircover']):
                        invalid_rules.append(('navigation', rule))
                
                if invalid_rules:
                    print(f"⚠️  INVALID RULES DETECTED:")
                    for inv_type, rule in invalid_rules:
                        print(f"  - {inv_type.upper()}: {rule.get('description', '')[:80]}...")
                
                # Store results
                result = {
                    'url': url,
                    'status': 'success',
                    'listing_title': listing_data.get('title', 'N/A'),
                    'house_rules_count': len(house_rules),
                    'has_checkin_checkout': bool(checkin_checkout),
                    'amenities_count': len(amenities.get('basic', [])),
                    'invalid_rules_count': len(invalid_rules),
                    'house_rules': house_rules,
                    'invalid_rules': invalid_rules
                }
                results.append(result)
                
                print(f"✅ Listing {i+1} processed successfully")
                
                # Small delay between requests
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ Error processing listing {i+1}: {e}")
                results.append({
                    'url': url,
                    'status': 'error',
                    'error': str(e)
                })
                continue
        
        # Final assessment
        print(f"\n" + "=" * 80)
        print("🎯 COMPREHENSIVE EXTRACTION ASSESSMENT")
        print("=" * 80)
        
        successful = [r for r in results if r['status'] == 'success']
        failed = [r for r in results if r['status'] != 'success']
        
        print(f"📊 OVERALL RESULTS:")
        print(f"  - Successful extractions: {len(successful)}/{len(test_listings)}")
        print(f"  - Failed extractions: {len(failed)}")
        
        if successful:
            total_rules = sum(r['house_rules_count'] for r in successful)
            total_invalid = sum(r['invalid_rules_count'] for r in successful)
            avg_rules = total_rules / len(successful)
            
            print(f"  - Total house rules extracted: {total_rules}")
            print(f"  - Average rules per listing: {avg_rules:.1f}")
            print(f"  - Invalid rules detected: {total_invalid}")
            print(f"  - Quality score: {((total_rules - total_invalid) / max(total_rules, 1) * 100):.1f}%")
            
            # Show listings with good rule extraction
            good_extractions = [r for r in successful if r['house_rules_count'] >= 3 and r['invalid_rules_count'] == 0]
            print(f"  - Listings with good rule extraction: {len(good_extractions)}")
            
            # Show problematic patterns
            if total_invalid > 0:
                print(f"\n⚠️  ISSUES TO ADDRESS:")
                for result in successful:
                    if result['invalid_rules_count'] > 0:
                        print(f"  - {result['listing_title'][:50]}... has {result['invalid_rules_count']} invalid rules")
        
        # Recommendations
        print(f"\n🚀 RECOMMENDATIONS:")
        if len(successful) >= 8:
            print("✅ Extraction success rate is good (80%+)")
        else:
            print("⚠️  Extraction success rate needs improvement")
            
        if successful and sum(r['invalid_rules_count'] for r in successful) == 0:
            print("✅ No invalid rules detected - filtering is working well")
        else:
            print("⚠️  Invalid rules detected - filtering needs enhancement")
            
        if successful and sum(r['house_rules_count'] for r in successful) / len(successful) >= 2:
            print("✅ Good average rule extraction (2+ rules per listing)")
        else:
            print("⚠️  Low rule extraction - may need to enhance detection")
        
        return results
        
    except Exception as e:
        print(f"❌ Error during comprehensive test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    test_comprehensive_extraction()
