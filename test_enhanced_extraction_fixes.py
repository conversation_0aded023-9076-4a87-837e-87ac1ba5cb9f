#!/usr/bin/env python3
"""
Test the enhanced extraction fixes for house rules and knowledge filtering.
"""

import sys
import os
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def test_enhanced_extraction_fixes():
    """Test the enhanced extraction fixes."""
    
    # Test with the original listing that has comprehensive house rules
    test_url = "https://www.airbnb.com/rooms/973815691982105805"
    host_id = "test_host_fixes"
    
    print(f"🧪 TESTING ENHANCED EXTRACTION FIXES")
    print(f"URL: {test_url}")
    print(f"Host ID: {host_id}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        # Step 1: Extract listing data
        print("🔍 Step 1: Extracting listing data...")
        listing_data = scraper.extract_listing_details(test_url)
        
        if not listing_data:
            print("❌ Failed to extract listing data")
            return
            
        print(f"✅ Listing data extracted: {listing_data.get('title', 'N/A')}")
        
        # Step 2: Extract deep property data with enhanced logic
        print("\n🔍 Step 2: Extracting deep property data with enhanced logic...")
        extracted_data = scraper.extract_deep_property_data(test_url)
        
        if not extracted_data:
            print("❌ Failed to extract deep property data")
            return
            
        house_rules = extracted_data.get('house_rules', [])
        amenities = extracted_data.get('amenities', {})
        
        print(f"✅ Deep data extracted:")
        print(f"  - House rules: {len(house_rules)}")
        print(f"  - Basic amenities: {len(amenities.get('basic', []))}")
        print(f"  - Appliances: {len(amenities.get('appliances', []))}")
        
        # Step 3: Analyze house rules quality
        print(f"\n📜 HOUSE RULES ANALYSIS:")
        
        if house_rules:
            pets_rules = [rule for rule in house_rules if 'pet' in rule.get('description', '').lower()]
            guest_rules = [rule for rule in house_rules if 'guest' in rule.get('description', '').lower() and 'maximum' in rule.get('description', '').lower()]
            
            print(f"  - Total rules found: {len(house_rules)}")
            print(f"  - Pet-related rules: {len(pets_rules)}")
            print(f"  - Guest capacity rules: {len(guest_rules)}")
            
            print(f"\n📋 DETAILED RULES:")
            for i, rule in enumerate(house_rules):
                title = rule.get('title', 'No title')
                description = rule.get('description', 'No description')
                source = rule.get('source', 'unknown')
                print(f"  {i+1}. [{source}] {title}")
                print(f"     {description}")
                
                # Check for specific issues
                if 'pets allowed' in description.lower():
                    print(f"     ✅ Found 'Pets allowed' rule!")
                elif '5 guests' in description.lower():
                    print(f"     ⚠️  Found '5 guests' rule - verify if correct")
                elif '4 guests' in description.lower():
                    print(f"     ✅ Found '4 guests' rule - likely correct")
        else:
            print("  ❌ No house rules found")
        
        # Step 4: Test knowledge item filtering
        print(f"\n🔍 Step 4: Testing knowledge item filtering...")
        
        # Test the local area filtering
        test_content = [
            "The most important thing about me when I travel is to be in a good neighborhood",  # Should be filtered
            "2025 Summer Release",  # Should be filtered
            "Airbnb.org emergency stays",  # Should be filtered
            "West Allis is a vibrant city located in Milwaukee County",  # Should be kept
            "Free parking on premises",  # Should be kept
            "Carbon monoxide alarm"  # Should be kept
        ]
        
        valid_content = []
        filtered_content = []
        
        for content in test_content:
            if scraper._is_valid_local_area_content(content):
                valid_content.append(content)
            else:
                filtered_content.append(content)
        
        print(f"  - Valid content: {len(valid_content)}")
        print(f"  - Filtered content: {len(filtered_content)}")
        
        print(f"\n✅ VALID CONTENT:")
        for content in valid_content:
            print(f"    - {content[:60]}...")
            
        print(f"\n❌ FILTERED CONTENT:")
        for content in filtered_content:
            print(f"    - {content[:60]}...")
        
        # Step 5: Create property and test integration
        print(f"\n🔍 Step 5: Creating property with enhanced extraction...")
        property_id = scraper.create_property_from_extraction(host_id, listing_data, extracted_data)
        
        if not property_id:
            print("❌ Failed to create property")
            return
            
        print(f"✅ Property created with ID: {property_id}")
        
        # Step 6: Verify knowledge items quality
        print(f"\n🔍 Step 6: Verifying knowledge items quality...")
        from concierge.utils.firestore_client import list_knowledge_items_by_property
        
        knowledge_items = list_knowledge_items_by_property(property_id)
        
        if knowledge_items:
            print(f"✅ Found {len(knowledge_items)} knowledge items")
            
            # Analyze knowledge item quality
            rule_items = [item for item in knowledge_items if item.get('type') == 'rule']
            emergency_items = [item for item in knowledge_items if item.get('type') == 'emergency']
            places_items = [item for item in knowledge_items if item.get('type') == 'places']
            
            print(f"  - Rule items: {len(rule_items)}")
            print(f"  - Emergency items: {len(emergency_items)}")
            print(f"  - Places items: {len(places_items)}")
            
            # Check for problematic content
            problematic_items = []
            for item in knowledge_items:
                content = item.get('content', '').lower()
                if any(bad in content for bad in ['summer release', 'the most important thing about me', 'airbnb.org']):
                    problematic_items.append(item)
            
            if problematic_items:
                print(f"  ⚠️  Found {len(problematic_items)} problematic knowledge items:")
                for item in problematic_items:
                    print(f"    - {item.get('content', '')[:60]}...")
            else:
                print(f"  ✅ No problematic knowledge items found!")
        
        # Final assessment
        print(f"\n" + "=" * 80)
        print("🎯 ENHANCED EXTRACTION FIXES ASSESSMENT:")
        
        # Check improvements
        has_multiple_rules = len(house_rules) > 1
        has_pets_rule = any('pets allowed' in rule.get('description', '').lower() for rule in house_rules)
        has_correct_guest_count = any('4 guests' in rule.get('description', '').lower() for rule in house_rules)
        no_problematic_knowledge = len(problematic_items) == 0 if 'problematic_items' in locals() else True
        
        print(f"✅ Multiple rules extracted: {'Yes' if has_multiple_rules else 'No'} ({len(house_rules)} rules)")
        print(f"✅ 'Pets allowed' rule found: {'Yes' if has_pets_rule else 'No'}")
        print(f"✅ Correct guest count: {'Yes' if has_correct_guest_count else 'No'}")
        print(f"✅ Clean knowledge items: {'Yes' if no_problematic_knowledge else 'No'}")
        
        # Overall success assessment
        success_score = sum([has_multiple_rules, has_pets_rule, has_correct_guest_count, no_problematic_knowledge])
        
        if success_score >= 3:
            print("🎉 EXCELLENT: Enhanced extraction fixes working well!")
        elif success_score >= 2:
            print("👍 GOOD: Most fixes working correctly")
        else:
            print("⚠️  NEEDS MORE WORK: Several issues still present")
        
        return property_id
        
    except Exception as e:
        print(f"❌ Error during enhanced extraction test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    test_enhanced_extraction_fixes()
