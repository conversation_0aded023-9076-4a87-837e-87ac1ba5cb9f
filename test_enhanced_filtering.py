#!/usr/bin/env python3
"""
Test the enhanced filtering logic for house rules.
"""

import sys
import os

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import <PERSON>bnb<PERSON><PERSON>raper

def test_enhanced_filtering():
    """Test the enhanced filtering logic."""
    
    print(f"🧪 TESTING ENHANCED FILTERING LOGIC")
    print("=" * 60)
    
    scraper = AirbnbScraper()
    
    # Test cases: (text, should_be_valid)
    test_cases = [
        # Valid rules
        ("Quiet hours from 10 PM to 8 AM", True),
        ("No smoking anywhere on the property", True),
        ("No parties or events allowed", True),
        ("Maximum 4 guests allowed", True),
        ("Pets allowed with prior approval", True),
        ("Check-out by 11 AM required", False),  # Should be filtered as check-in/out time
        
        # Invalid rules that should be filtered
        ("Recent guests loved Evolve's communication.", False),  # Testimonial
        ("Guests1 guest", False),  # UI text
        ("Select check-in date", False),  # UI element
        ("Self check-in available", False),  # UI element
        ("Check-in after 3:00 PM", False),  # Check-in time
        ("Guests loved the location", False),  # Testimonial
        ("Highly rated for cleanliness", False),  # Testimonial
        ("Amazing host communication", False),  # Testimonial
        ("Add guests", False),  # UI text
        
        # Edge cases
        ("", False),  # Too short
        ("No", False),  # Too short
        ("Pets", False),  # Too generic
        ("Check-in", False),  # Too generic
    ]
    
    print(f"Testing {len(test_cases)} rule filtering cases...")
    print()
    
    passed = 0
    failed = 0
    
    for i, (text, expected_valid) in enumerate(test_cases):
        result = scraper._is_likely_house_rule(text)
        status = "✅ PASS" if result == expected_valid else "❌ FAIL"
        
        print(f"{i+1:2d}. {status} | Expected: {'VALID' if expected_valid else 'INVALID':7} | Got: {'VALID' if result else 'INVALID':7} | '{text}'")
        
        if result == expected_valid:
            passed += 1
        else:
            failed += 1
    
    print()
    print("=" * 60)
    print(f"🎯 FILTERING TEST RESULTS:")
    print(f"  - Passed: {passed}/{len(test_cases)}")
    print(f"  - Failed: {failed}/{len(test_cases)}")
    print(f"  - Success rate: {(passed/len(test_cases)*100):.1f}%")
    
    if failed == 0:
        print("🎉 PERFECT! All filtering tests passed!")
    elif failed <= 2:
        print("👍 GOOD! Most filtering tests passed.")
    else:
        print("⚠️  NEEDS IMPROVEMENT: Several filtering tests failed.")
    
    return passed, failed

if __name__ == "__main__":
    test_enhanced_filtering()
