#!/usr/bin/env python3
"""
Test the enhanced house rules processing and time extraction.
"""

import sys
import os
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import <PERSON>bnb<PERSON><PERSON>raper

def test_enhanced_rules_processing():
    """Test the enhanced house rules processing with time extraction."""
    
    # Test with the working listing
    test_url = "https://www.airbnb.com/rooms/1126993904928991778"
    host_id = "test_host_enhanced"
    
    print(f"🧪 TESTING ENHANCED HOUSE RULES PROCESSING")
    print(f"URL: {test_url}")
    print(f"Host ID: {host_id}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        # Step 1: Extract listing data
        print("🔍 Step 1: Extracting listing data...")
        listing_data = scraper.extract_listing_details(test_url)
        
        if not listing_data:
            print("❌ Failed to extract listing data")
            return
            
        print(f"✅ Listing data extracted: {listing_data.get('title', 'N/A')}")
        
        # Step 2: Extract deep property data
        print("\n🔍 Step 2: Extracting deep property data...")
        extracted_data = scraper.extract_deep_property_data(test_url)
        
        if not extracted_data:
            print("❌ Failed to extract deep property data")
            return
            
        house_rules = extracted_data.get('house_rules', [])
        checkin_checkout = extracted_data.get('checkin_checkout', {})
        
        print(f"✅ Deep data extracted:")
        print(f"  - House rules: {len(house_rules)}")
        print(f"  - Check-in/check-out data: {bool(checkin_checkout)}")
        
        # Show house rules
        print(f"\n📜 EXTRACTED HOUSE RULES:")
        for i, rule in enumerate(house_rules):
            title = rule.get('title', 'No title')
            description = rule.get('description', 'No description')
            print(f"  {i+1}. {title}")
            print(f"     {description}")
        
        # Show check-in/check-out info
        print(f"\n🕐 CHECK-IN/CHECK-OUT INFO:")
        print(f"  - Check-in time: {checkin_checkout.get('checkin_time', 'Not found')}")
        print(f"  - Check-out time: {checkin_checkout.get('checkout_time', 'Not found')}")
        print(f"  - Check-in instructions: {len(checkin_checkout.get('checkin_instructions', ''))} chars")
        print(f"  - Check-out instructions: {len(checkin_checkout.get('checkout_instructions', ''))} chars")
        
        # Step 3: Test time extraction from house rules
        print(f"\n🔍 Step 3: Testing time extraction from house rules...")
        times_from_rules = scraper._extract_times_from_house_rules(house_rules)
        
        print(f"✅ Times extracted from house rules:")
        print(f"  - Check-in time: {times_from_rules.get('checkin_time', 'Not found')}")
        print(f"  - Check-out time: {times_from_rules.get('checkout_time', 'Not found')}")
        
        # Step 4: Create property and test integration
        print(f"\n🔍 Step 4: Creating property with enhanced processing...")
        property_id = scraper.create_property_from_extraction(host_id, listing_data, extracted_data)
        
        if not property_id:
            print("❌ Failed to create property")
            return
            
        print(f"✅ Property created with ID: {property_id}")
        
        # Step 5: Verify property data
        print(f"\n🔍 Step 5: Verifying property data...")
        from concierge.utils.firestore_client import get_property
        
        property_data = get_property(property_id)
        if not property_data:
            print("❌ Failed to retrieve property data")
            return
            
        print(f"✅ Property data retrieved:")
        print(f"  - Name: {property_data.get('name', 'N/A')}")
        print(f"  - Check-in time: {property_data.get('checkInTime', 'Not set')}")
        print(f"  - Check-out time: {property_data.get('checkOutTime', 'Not set')}")
        
        # Check import data structure
        import_data = property_data.get('importData', {})
        raw_data = import_data.get('rawData', {})
        
        print(f"  - Import source: {import_data.get('source', 'N/A')}")
        print(f"  - House rules in rawData: {len(raw_data.get('house_rules', []))}")
        print(f"  - House rules in extracted: {len(raw_data.get('extracted', {}).get('house_rules', []))}")
        
        # Test specific time extraction patterns
        print(f"\n🔍 Step 6: Testing specific time extraction patterns...")
        test_texts = [
            "Check-in after 3:00 PM",
            "Check-out before 11:00 AM", 
            "Quiet hours (12:00 AM - 7:00 AM)",
            "Check-in: 3 PM",
            "Checkout: 11 AM",
            "Check in time is 15:00",
            "Check out by 11:30 AM"
        ]
        
        for text in test_texts:
            extracted_time = scraper._extract_time_from_text(text)
            print(f"  '{text}' → {extracted_time or 'No time found'}")
        
        # Final assessment
        print(f"\n" + "=" * 80)
        print("🎯 ENHANCED RULES PROCESSING ASSESSMENT:")
        
        # Check if times were properly extracted and applied
        has_checkin = bool(property_data.get('checkInTime'))
        has_checkout = bool(property_data.get('checkOutTime'))
        has_rules = len(house_rules) > 0
        has_import_data = bool(import_data.get('rawData'))
        
        print(f"✅ Check-in time extracted: {'Yes' if has_checkin else 'No'} ({property_data.get('checkInTime', 'N/A')})")
        print(f"✅ Check-out time extracted: {'Yes' if has_checkout else 'No'} ({property_data.get('checkOutTime', 'N/A')})")
        print(f"✅ House rules extracted: {'Yes' if has_rules else 'No'} ({len(house_rules)} rules)")
        print(f"✅ Import data preserved: {'Yes' if has_import_data else 'No'}")
        
        # Overall success assessment
        success_score = sum([has_checkin, has_checkout, has_rules, has_import_data])
        
        if success_score >= 3:
            print("🎉 EXCELLENT: Enhanced rules processing working perfectly!")
        elif success_score >= 2:
            print("👍 GOOD: Most features working correctly")
        else:
            print("⚠️  NEEDS IMPROVEMENT: Some features not working as expected")
        
        return property_id
        
    except Exception as e:
        print(f"❌ Error during enhanced rules processing test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    test_enhanced_rules_processing()
