#!/usr/bin/env python3
"""
Test script for the icalUrl and house rules extraction fixes.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.airbnb_scraper import AirbnbScraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_house_rules_extraction():
    """Test the improved house rules extraction logic"""
    
    scraper = AirbnbScraper()
    
    print("🏠 Testing Improved House Rules Extraction")
    print("=" * 60)
    
    # Test the rule title extraction with specific examples
    test_rules = [
        "Quiet hours 12:00 AM - 7:00 AM",
        "No smoking anywhere on the property",
        "No parties or events allowed",
        "Maximum 4 guests",
        "Check-in after 3:00 PM",
        "Check-out before 11:00 AM",
        "Pets are not allowed",
        "Please keep noise to a minimum between 10 PM and 8 AM"
    ]
    
    print("🔍 Testing Rule Title Extraction:")
    for rule_text in test_rules:
        title = scraper._extract_precise_rule_title(rule_text)
        print(f"   '{rule_text}' → '{title}'")
    
    print("\n🔍 Testing Rule Filtering:")
    test_texts = [
        "Quiet hours 12:00 AM - 7:00 AM",  # Should be included
        "House Rules",  # Should be excluded (heading)
        "No smoking",  # Should be included
        "Contact the host for more information",  # Should be excluded
        "This property has amazing views",  # Should be excluded
        "Maximum occupancy 6 guests"  # Should be included
    ]
    
    for text in test_texts:
        is_rule = scraper._is_likely_house_rule(text)
        status = "✅ INCLUDE" if is_rule else "❌ EXCLUDE"
        print(f"   '{text}' → {status}")

def test_ical_url_saving():
    """Test that icalUrl is properly saved"""
    
    print("\n📅 Testing iCal URL Saving")
    print("=" * 60)
    
    # This would need to be tested in the actual UI
    # For now, just verify the API route includes the field
    print("✅ icalUrl field added to API route in concierge/api/routes.py")
    print("   - Line 3330: 'icalUrl': step_data.get('icalUrl', '')")
    print("   - This should now save the icalUrl to Firestore")
    
    print("\n🔧 To test manually:")
    print("1. Import a property or open Property Setup")
    print("2. Add an iCal URL in the Basic Information step")
    print("3. Save and check Firestore to verify the icalUrl field is saved")

if __name__ == "__main__":
    test_house_rules_extraction()
    test_ical_url_saving()
    
    print("\n🎯 Summary of Fixes:")
    print("=" * 60)
    print("✅ icalUrl field now saved to Firestore in Property Setup")
    print("✅ House rules extraction improved with:")
    print("   - Better targeting of rule sections")
    print("   - Precise time extraction for quiet hours")
    print("   - Improved rule filtering")
    print("   - Enhanced JSON script processing")
    print("   - More accurate rule title generation")
    
    print("\n📋 Next Steps:")
    print("1. Test icalUrl saving in the UI")
    print("2. Import the Airbnb listing again to test house rules")
    print("3. Verify that 'Quiet hours (12:00 AM - 7:00 AM)' is extracted correctly")
