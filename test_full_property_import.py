#!/usr/bin/env python3
"""
Test the full property import flow including UI integration.
"""

import sys
import os
import json
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper
from concierge.utils.firestore_client import get_property, list_knowledge_items_by_property

def test_multiple_property_imports():
    """Test the complete property import flow on multiple listings."""

    # Test multiple fresh listings
    test_listings = [
        {
            "url": "https://www.airbnb.com/rooms/1126993904928991778",
            "description": "Beehive Shipping Container Cabin - Lapeer, Michigan",
            "host_id": "test_host_123"
        },
        {
            "url": "https://www.airbnb.com/rooms/53089734",
            "description": "Austin downtown loft",
            "host_id": "test_host_456"
        },
        {
            "url": "https://www.airbnb.com/rooms/50663982",
            "description": "Portland tiny house",
            "host_id": "test_host_789"
        }
    ]

    successful_imports = []
    failed_imports = []

    for i, listing in enumerate(test_listings):
        print(f"\n{'='*80}")
        print(f"🧪 TESTING PROPERTY IMPORT {i+1}/{len(test_listings)}")
        print(f"Description: {listing['description']}")
        print(f"URL: {listing['url']}")
        print(f"Host ID: {listing['host_id']}")
        print('='*80)

        result = test_single_property_import(listing['url'], listing['host_id'], listing['description'])

        if result:
            successful_imports.append({**listing, 'property_id': result})
        else:
            failed_imports.append(listing)

    # Final summary
    print(f"\n{'='*80}")
    print("🎯 FINAL SUMMARY:")
    print(f"✅ Successful imports: {len(successful_imports)}")
    print(f"❌ Failed imports: {len(failed_imports)}")

    for success in successful_imports:
        print(f"  ✅ {success['description']} → {success['property_id']}")

    for failure in failed_imports:
        print(f"  ❌ {failure['description']} → Failed")

    print("="*80)

    return successful_imports, failed_imports

def test_single_property_import(test_url: str, host_id: str, description: str):
    """Test the complete property import flow for a single listing."""
    
    print(f"🧪 TESTING FULL PROPERTY IMPORT")
    print(f"URL: {test_url}")
    print(f"Host ID: {host_id}")
    print("=" * 80)
    
    scraper = AirbnbScraper()
    
    try:
        # Step 1: Extract listing data
        print("🔍 Step 1: Extracting listing data...")
        listing_data = scraper.extract_listing_details(test_url)
        
        if not listing_data:
            print("❌ Failed to extract listing data")
            return
            
        print(f"✅ Listing data extracted: {listing_data.get('title', 'N/A')}")
        
        # Step 2: Extract deep property data
        print("\n🔍 Step 2: Extracting deep property data...")
        extracted_data = scraper.extract_deep_property_data(test_url)
        
        if not extracted_data:
            print("❌ Failed to extract deep property data")
            return
            
        print(f"✅ Deep data extracted:")
        print(f"  - Description: {len(extracted_data.get('description', ''))} chars")
        print(f"  - Basic amenities: {len(extracted_data.get('amenities', {}).get('basic', []))}")
        print(f"  - Appliances: {len(extracted_data.get('amenities', {}).get('appliances', []))}")
        print(f"  - House rules: {len(extracted_data.get('house_rules', []))}")
        
        # Step 3: Create property from extraction
        print("\n🔍 Step 3: Creating property from extraction...")
        property_id = scraper.create_property_from_extraction(host_id, listing_data, extracted_data)
        
        if not property_id:
            print("❌ Failed to create property")
            return
            
        print(f"✅ Property created with ID: {property_id}")
        
        # Step 4: Verify property data
        print("\n🔍 Step 4: Verifying property data...")
        property_data = get_property(property_id)
        
        if not property_data:
            print("❌ Failed to retrieve property data")
            return
            
        print(f"✅ Property data retrieved:")
        print(f"  - Name: {property_data.get('name', 'N/A')}")
        print(f"  - Address: {property_data.get('address', 'N/A')}")
        print(f"  - Status: {property_data.get('status', 'N/A')}")
        print(f"  - New property: {property_data.get('new', 'N/A')}")
        
        # Check amenities
        amenities = property_data.get('amenities', {})
        basic_amenities = amenities.get('basic', [])
        appliances = amenities.get('appliances', [])
        
        print(f"  - Basic amenities: {len(basic_amenities)}")
        print(f"  - Appliances: {len(appliances)}")
        
        # Check import data structure
        import_data = property_data.get('importData', {})
        raw_data = import_data.get('rawData', {})
        
        print(f"  - Import source: {import_data.get('source', 'N/A')}")
        print(f"  - House rules in rawData.house_rules: {len(raw_data.get('house_rules', []))}")
        print(f"  - House rules in rawData.extracted.house_rules: {len(raw_data.get('extracted', {}).get('house_rules', []))}")
        
        # Step 5: Verify knowledge items
        print("\n🔍 Step 5: Verifying knowledge items...")
        knowledge_items = list_knowledge_items_by_property(property_id)
        
        print(f"✅ Knowledge items created: {len(knowledge_items)}")
        
        # Group by type
        by_type = {}
        for item in knowledge_items:
            item_type = item.get('type', 'unknown')
            if item_type not in by_type:
                by_type[item_type] = []
            by_type[item_type].append(item)
        
        for item_type, items in by_type.items():
            print(f"  - {item_type}: {len(items)} items")
            
        # Show first few rule items
        rule_items = by_type.get('rule', [])
        if rule_items:
            print(f"\n📜 First 3 house rule knowledge items:")
            for i, item in enumerate(rule_items[:3]):
                content = item.get('content', 'N/A')[:50]
                status = item.get('status', 'N/A')
                print(f"  {i+1}. [{status}] {content}...")
        
        # Step 6: Check UI data structure compatibility
        print("\n🔍 Step 6: Checking UI compatibility...")
        
        # Check if house rules are accessible for the UI
        ui_house_rules_1 = raw_data.get('extracted', {}).get('house_rules', [])
        ui_house_rules_2 = raw_data.get('house_rules', [])
        
        print(f"✅ UI compatibility check:")
        print(f"  - rawData.extracted.house_rules: {len(ui_house_rules_1)} rules (UI looks here)")
        print(f"  - rawData.house_rules: {len(ui_house_rules_2)} rules (UI fallback)")
        print(f"  - Knowledge items (rule type): {len(rule_items)} items (UI fallback)")
        
        if ui_house_rules_1 or ui_house_rules_2 or rule_items:
            print("  ✅ House rules will be visible in UI!")
        else:
            print("  ❌ House rules may not be visible in UI")
            
        # Final summary
        print("\n" + "=" * 80)
        print("🎯 IMPORT SUMMARY:")
        print(f"✅ Property ID: {property_id}")
        print(f"✅ House rules extracted: {len(extracted_data.get('house_rules', []))}")
        print(f"✅ Knowledge items created: {len(knowledge_items)}")
        print(f"✅ UI-compatible data: {'Yes' if (ui_house_rules_1 or ui_house_rules_2 or rule_items) else 'No'}")
        print(f"✅ Amenities clean: {len(basic_amenities)} basic, {len(appliances)} appliances")
        
        # Check for appliances with locations
        appliances_with_locations = sum(1 for app in appliances if isinstance(app, dict) and app.get('location'))
        print(f"✅ Appliances with locations: {appliances_with_locations}/{len(appliances)}")
        
        print("\n🎉 FULL PROPERTY IMPORT TEST COMPLETED SUCCESSFULLY!")
        
        return property_id
        
    except Exception as e:
        print(f"❌ Error during property import: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_multiple_property_imports()
