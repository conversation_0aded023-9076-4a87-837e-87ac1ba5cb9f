#!/usr/bin/env python3
"""
Test script for house rules extraction from the specific Airbnb listing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.airbnb_scraper import AirbnbScraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_house_rules_extraction():
    """Test enhanced house rules extraction from multiple listings"""

    # Test URLs - multiple fresh listings to ensure reliability
    test_urls = [
        {
            "url": "https://www.airbnb.com/rooms/1126993904928991778",
            "description": "Beehive Shipping Container Cabin - Lapeer, Michigan",
            "expected_rules": ["Quiet hours", "Check-in", "No smoking"]
        },
        {
            "url": "https://www.airbnb.com/rooms/53089734",
            "description": "Austin downtown loft",
            "expected_rules": ["No smoking", "No parties", "Quiet hours"]
        },
        {
            "url": "https://www.airbnb.com/rooms/50663982",
            "description": "Portland tiny house",
            "expected_rules": ["Quiet hours", "No pets", "No smoking"]
        }
    ]

    scraper = AirbnbScraper()

    print("🏠 Testing Enhanced House Rules Extraction")
    print("=" * 70)

    for i, test_case in enumerate(test_urls, 1):
        print(f"\n{i}. Testing: {test_case['description']}")
        print(f"URL: {test_case['url']}")
        print("-" * 50)

        try:
            # Test the deep extraction method
            print("🔍 Running deep property extraction...")
            property_data = scraper.extract_deep_property_data(test_case['url'])

            if property_data:
                print("✅ Property data extracted successfully")
                print(f"Property name: {property_data.get('name', 'N/A')}")
                print(f"Property address: {property_data.get('address', 'N/A')}")

                # Check for house rules in the extracted data
                house_rules = property_data.get('house_rules', [])
                print(f"\n📋 House Rules Found: {len(house_rules)}")

                if house_rules:
                    for j, rule in enumerate(house_rules, 1):
                        print(f"  {j}. Title: {rule.get('title', 'No title')}")
                        print(f"     Description: {rule.get('description', 'No description')}")
                        print(f"     Source: {rule.get('source', 'Unknown')}")
                        print()

                    # Check if expected rules were found
                    found_expected = []
                    for expected in test_case['expected_rules']:
                        for rule in house_rules:
                            if expected.lower() in rule.get('description', '').lower():
                                found_expected.append(expected)
                                break

                    if found_expected:
                        print(f"✅ Found expected rules: {found_expected}")
                    else:
                        print(f"⚠️  Expected rules not found: {test_case['expected_rules']}")

                else:
                    print("❌ No house rules found in extracted data")

                    # Test direct house rules URL construction
                    print("\n🔍 Testing house rules URL construction...")
                    house_rules_url = scraper._construct_house_rules_url(test_case['url'])
                    if house_rules_url:
                        print(f"House rules URL: {house_rules_url}")

                        # Try to fetch the house rules page directly
                        try:
                            import requests
                            response = requests.get(house_rules_url, timeout=10)
                            print(f"House rules page status: {response.status_code}")
                            if response.status_code == 200:
                                print("✅ House rules page accessible")
                            else:
                                print("❌ House rules page not accessible")
                        except Exception as e:
                            print(f"❌ Error accessing house rules page: {e}")

            else:
                print("❌ Failed to extract property data")

        except Exception as e:
            print(f"❌ Error during extraction: {e}")
            import traceback
            traceback.print_exc()

        print()

def test_rule_title_extraction():
    """Test the rule title extraction logic"""
    
    print("\n🔧 Testing Rule Title Extraction")
    print("=" * 60)
    
    scraper = AirbnbScraper()
    
    # Test cases that should be found in the Airbnb listing
    test_cases = [
        "Quiet hours 12:00 AM - 7:00 AM",
        "No smoking",
        "No parties or events",
        "Check-in: 4:00 PM - 12:00 AM",
        "Check-out: 11:00 AM",
        "Maximum occupancy: 4 guests"
    ]
    
    for test_case in test_cases:
        title = scraper._extract_precise_rule_title(test_case)
        is_rule = scraper._is_likely_house_rule(test_case)
        status = "✅ VALID RULE" if is_rule else "❌ NOT A RULE"
        print(f"'{test_case}' → '{title}' ({status})")

if __name__ == "__main__":
    test_house_rules_extraction()
    test_rule_title_extraction()
    
    print("\n🎯 Summary:")
    print("=" * 60)
    print("This test checks if house rules are properly extracted from the Airbnb listing.")
    print("If no rules are found, check the server logs for extraction details.")
    print("The listing should have quiet hours from 12:00 AM - 7:00 AM.")
