#!/usr/bin/env python3
"""
Test the improved extraction logic with fresh Airbnb listings.
"""

import sys
import os
import json
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper
from concierge.utils.firestore_client import get_property, list_knowledge_items_by_property

def test_extraction_on_fresh_listings():
    """Test extraction on fresh Airbnb listings."""
    
    # Test URLs - fresh listings that should work
    test_urls = [
        {
            "url": "https://www.airbnb.com/rooms/1126993904928991778",
            "description": "Beehive Shipping Container Cabin - Lapeer, Michigan",
            "expected_features": ["house_rules", "amenities", "appliances"]
        },
        {
            "url": "https://www.airbnb.com/rooms/53089734",
            "description": "Austin downtown loft",
            "expected_features": ["house_rules", "amenities", "appliances"]
        }
    ]
    
    scraper = AirbnbScraper()
    
    for i, test_case in enumerate(test_urls):
        print(f"\n{'='*80}")
        print(f"🧪 TEST {i+1}: {test_case['description']}")
        print(f"URL: {test_case['url']}")
        print('='*80)
        
        try:
            # Extract data from the listing
            print("🔍 Extracting data from listing...")
            extracted_data = scraper.extract_deep_property_data(test_case['url'])
            
            if not extracted_data:
                print("❌ Failed to extract data")
                continue
                
            print("✅ Data extraction successful!")
            
            # Analyze the extracted data
            print(f"\n📊 EXTRACTION RESULTS:")
            print(f"- Description: {len(extracted_data.get('description', ''))} characters")
            
            amenities = extracted_data.get('amenities', {})
            basic_amenities = amenities.get('basic', [])
            appliances = amenities.get('appliances', [])
            
            print(f"- Basic amenities: {len(basic_amenities)}")
            print(f"- Appliances: {len(appliances)}")
            
            # Show first few amenities
            if basic_amenities:
                print(f"  First 5 basic amenities: {basic_amenities[:5]}")
            
            if appliances:
                print(f"  First 5 appliances:")
                for j, appliance in enumerate(appliances[:5]):
                    if isinstance(appliance, dict):
                        name = appliance.get('name', 'N/A')
                        location = appliance.get('location', 'N/A')
                        print(f"    {j+1}. {name} - Location: {location}")
                    else:
                        print(f"    {j+1}. {appliance}")
            
            # Check house rules
            house_rules = extracted_data.get('house_rules', [])
            print(f"- House rules: {len(house_rules)}")
            
            if house_rules:
                print(f"  House rules found:")
                for j, rule in enumerate(house_rules[:5]):
                    if isinstance(rule, dict):
                        title = rule.get('title', 'N/A')
                        description = rule.get('description', 'N/A')[:50]
                        print(f"    {j+1}. {title}: {description}...")
                    else:
                        print(f"    {j+1}. {rule}")
            
            # Check for duplicates in amenities
            basic_lower = [a.lower() for a in basic_amenities]
            basic_duplicates = len(basic_amenities) - len(set(basic_lower))
            
            appliance_names = [a.get('name', '').lower() if isinstance(a, dict) else str(a).lower() for a in appliances]
            appliance_duplicates = len(appliances) - len(set(appliance_names))
            
            print(f"\n🔍 QUALITY CHECK:")
            print(f"- Basic amenity duplicates: {basic_duplicates}")
            print(f"- Appliance duplicates: {appliance_duplicates}")
            
            # Check appliance locations
            kitchen_appliances = 0
            empty_locations = 0
            for appliance in appliances:
                if isinstance(appliance, dict):
                    location = appliance.get('location', '')
                    if location.lower() == 'kitchen':
                        kitchen_appliances += 1
                    elif not location:
                        empty_locations += 1
            
            print(f"- Kitchen appliances: {kitchen_appliances}")
            print(f"- Appliances with empty locations: {empty_locations}")
            
            # Overall assessment
            print(f"\n🎯 ASSESSMENT:")
            issues = []
            if basic_duplicates > 0:
                issues.append(f"{basic_duplicates} duplicate basic amenities")
            if appliance_duplicates > 0:
                issues.append(f"{appliance_duplicates} duplicate appliances")
            if empty_locations > kitchen_appliances:
                issues.append(f"Many appliances missing locations")
            if len(house_rules) == 0:
                issues.append("No house rules extracted")
            
            if issues:
                print(f"❌ Issues found: {', '.join(issues)}")
            else:
                print("✅ Extraction looks good!")
                
        except Exception as e:
            print(f"❌ Error during extraction: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_extraction_on_fresh_listings()
