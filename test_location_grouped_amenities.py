#!/usr/bin/env python3
"""
Test the enhanced location-grouped amenities extraction.
"""

import sys
import os
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def test_location_grouped_amenities():
    """Test the location-grouped amenities extraction."""
    
    # Test with the working listing that has good amenities
    test_url = "https://www.airbnb.com/rooms/1126993904928991778"
    
    print(f"🧪 TESTING LOCATION-GROUPED AMENITIES EXTRACTION")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    # Create scraper with Selenium enabled
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        print("🔍 Step 1: Extracting deep property data with location grouping...")
        extracted_data = scraper.extract_deep_property_data(test_url)
        
        if not extracted_data:
            print("❌ Failed to extract deep property data")
            return
            
        amenities = extracted_data.get('amenities', {})
        basic_amenities = amenities.get('basic', [])
        appliances = amenities.get('appliances', [])
        
        print(f"✅ Deep data extracted:")
        print(f"  - Basic amenities: {len(basic_amenities)}")
        print(f"  - Appliances: {len(appliances)}")
        
        # Check appliance locations
        appliances_with_locations = 0
        location_counts = {}
        
        print(f"\n📍 APPLIANCE LOCATIONS:")
        for i, appliance in enumerate(appliances):
            if isinstance(appliance, dict):
                name = appliance.get('name', 'Unknown')
                location = appliance.get('location', 'No location')
                brand = appliance.get('brand', '')
                model = appliance.get('model', '')
                
                if location and location != 'No location' and location != '':
                    appliances_with_locations += 1
                    location_counts[location] = location_counts.get(location, 0) + 1
                
                brand_model = f" ({brand} {model})".strip() if brand or model else ""
                print(f"  {i+1}. {name} → {location}{brand_model}")
        
        print(f"\n📊 LOCATION SUMMARY:")
        print(f"  - Appliances with locations: {appliances_with_locations}/{len(appliances)}")
        print(f"  - Location distribution:")
        for location, count in sorted(location_counts.items()):
            print(f"    • {location}: {count} appliances")
        
        # Show some basic amenities
        print(f"\n🏠 BASIC AMENITIES (first 10):")
        for i, amenity in enumerate(basic_amenities[:10]):
            print(f"  {i+1}. {amenity}")
        
        if len(basic_amenities) > 10:
            print(f"  ... and {len(basic_amenities) - 10} more")
        
        # Test specific appliances we expect
        expected_appliances = ['microwave', 'coffee', 'tv', 'hair dryer', 'fridge']
        found_appliances = []
        
        print(f"\n🔍 CHECKING FOR EXPECTED APPLIANCES:")
        for appliance in appliances:
            if isinstance(appliance, dict):
                name = appliance.get('name', '').lower()
                for expected in expected_appliances:
                    if expected in name:
                        found_appliances.append(expected)
                        location = appliance.get('location', 'No location')
                        print(f"  ✅ Found {expected} → {location}")
        
        missing_appliances = set(expected_appliances) - set(found_appliances)
        if missing_appliances:
            print(f"  ❌ Missing: {', '.join(missing_appliances)}")
        
        # Final assessment
        print(f"\n" + "=" * 80)
        print("🎯 LOCATION-GROUPED EXTRACTION ASSESSMENT:")
        
        location_success = appliances_with_locations / len(appliances) if appliances else 0
        print(f"✅ Location assignment success: {location_success:.1%} ({appliances_with_locations}/{len(appliances)})")
        
        if location_success >= 0.8:
            print("🎉 EXCELLENT: Most appliances have proper locations!")
        elif location_success >= 0.6:
            print("👍 GOOD: Majority of appliances have locations")
        elif location_success >= 0.4:
            print("⚠️  FAIR: Some appliances have locations")
        else:
            print("❌ POOR: Few appliances have locations")
        
        # Check for location diversity
        unique_locations = len(location_counts)
        print(f"✅ Location diversity: {unique_locations} different locations")
        
        if unique_locations >= 3:
            print("🎉 EXCELLENT: Good location diversity!")
        elif unique_locations >= 2:
            print("👍 GOOD: Some location diversity")
        else:
            print("⚠️  LIMITED: Low location diversity")
        
        print(f"✅ Total amenities extracted: {len(basic_amenities) + len(appliances)}")
        
        return extracted_data
        
    except Exception as e:
        print(f"❌ Error during location-grouped extraction: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    test_location_grouped_amenities()
