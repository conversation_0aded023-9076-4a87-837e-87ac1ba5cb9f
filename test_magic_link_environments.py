#!/usr/bin/env python3
"""
Test script to verify magic link generation across different environments.
"""

import sys
import os
from datetime import datetime, timezone, timedelta

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'concierge'))

def test_environment_detection():
    """Test magic link URL generation for different environments."""
    print("🧪 Testing Magic Link URL Generation Across Environments\n")
    
    try:
        from concierge.utils.firestore_client import generate_magic_link_url
        import importlib
        import concierge.utils.firestore_client
        
        # Generate a test token
        test_token = "test-token-12345"
        
        # Test scenarios
        scenarios = [
            {
                'name': 'Local Development (DEPLOYMENT_ENV)',
                'env_vars': {'DEPLOYMENT_ENV': 'local'},
                'expected_url': f'http://localhost:5001/magic/{test_token}'
            },
            {
                'name': 'Local Development (FLASK_ENV)',
                'env_vars': {'FLASK_ENV': 'development', 'DEPLOYMENT_ENV': ''},
                'expected_url': f'http://localhost:5001/magic/{test_token}'
            },
            {
                'name': 'Local Development (DEBUG_MODE)',
                'env_vars': {'DEBUG_MODE': 'true', 'FLASK_ENV': '', 'DEPLOYMENT_ENV': ''},
                'expected_url': f'http://localhost:5001/magic/{test_token}'
            },
            {
                'name': 'Staging Environment',
                'env_vars': {'DEPLOYMENT_ENV': 'staging'},
                'expected_url': f'https://dev.guestrix.ai/magic/{test_token}'
            },
            {
                'name': 'Production Environment',
                'env_vars': {'DEPLOYMENT_ENV': 'production'},
                'expected_url': f'https://app.guestrix.ai/magic/{test_token}'
            },
            {
                'name': 'Default (Production Fallback)',
                'env_vars': {'DEPLOYMENT_ENV': '', 'FLASK_ENV': 'production', 'DEBUG_MODE': 'false'},
                'expected_url': f'https://app.guestrix.ai/magic/{test_token}'
            }
        ]
        
        results = []
        
        for scenario in scenarios:
            print(f"Testing: {scenario['name']}")
            
            # Store original environment variables
            original_env = {}
            env_keys = ['DEPLOYMENT_ENV', 'FLASK_ENV', 'DEBUG_MODE']
            for key in env_keys:
                original_env[key] = os.environ.get(key)
            
            try:
                # Clear all relevant environment variables first
                for key in env_keys:
                    if key in os.environ:
                        del os.environ[key]
                
                # Set test environment variables
                for key, value in scenario['env_vars'].items():
                    if value:
                        os.environ[key] = value
                
                # Force reload the module to pick up new environment variables
                importlib.reload(concierge.utils.firestore_client)
                from concierge.utils.firestore_client import generate_magic_link_url
                
                # Generate URL
                generated_url = generate_magic_link_url(test_token)
                
                # Check result
                if generated_url == scenario['expected_url']:
                    print(f"✅ PASS: {generated_url}")
                    results.append(True)
                else:
                    print(f"❌ FAIL: Expected {scenario['expected_url']}, got {generated_url}")
                    results.append(False)
                    
            finally:
                # Restore original environment variables
                for key in env_keys:
                    if key in os.environ:
                        del os.environ[key]
                    if original_env[key] is not None:
                        os.environ[key] = original_env[key]
                
                # Reload again to restore original state
                importlib.reload(concierge.utils.firestore_client)
            
            print()
        
        # Summary
        passed = sum(results)
        total = len(results)
        
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All environment tests passed!")
            return True
        else:
            print("❌ Some environment tests failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base_url_override():
    """Test that base_url parameter properly overrides environment detection."""
    print("🧪 Testing Base URL Override\n")
    
    try:
        from concierge.utils.firestore_client import generate_magic_link_url
        
        test_token = "test-token-override"
        test_base_urls = [
            "https://custom.example.com",
            "https://dev.guestrix.ai",
            "https://app.guestrix.ai",
            "http://localhost:3000"
        ]
        
        # Set environment to production (should be overridden)
        os.environ['DEPLOYMENT_ENV'] = 'production'
        
        results = []
        
        for base_url in test_base_urls:
            expected_url = f"{base_url}/magic/{test_token}"
            generated_url = generate_magic_link_url(test_token, base_url=base_url)
            
            if generated_url == expected_url:
                print(f"✅ PASS: {generated_url}")
                results.append(True)
            else:
                print(f"❌ FAIL: Expected {expected_url}, got {generated_url}")
                results.append(False)
        
        # Test with trailing slash
        base_url_with_slash = "https://test.example.com/"
        expected_url = f"https://test.example.com/magic/{test_token}"
        generated_url = generate_magic_link_url(test_token, base_url=base_url_with_slash)
        
        if generated_url == expected_url:
            print(f"✅ PASS (trailing slash): {generated_url}")
            results.append(True)
        else:
            print(f"❌ FAIL (trailing slash): Expected {expected_url}, got {generated_url}")
            results.append(False)
        
        # Clean up
        if 'DEPLOYMENT_ENV' in os.environ:
            del os.environ['DEPLOYMENT_ENV']
        
        passed = sum(results)
        total = len(results)
        
        print(f"\n📊 Base URL Override Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All base URL override tests passed!")
            return True
        else:
            print("❌ Some base URL override tests failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during base URL testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_environment_documentation():
    """Create documentation for environment configuration."""
    print("📝 Creating Environment Configuration Documentation\n")
    
    doc_content = """# Magic Link Environment Configuration

## Overview
Magic links are generated with different base URLs depending on the environment configuration.

## Environment Variables

### Primary Configuration
- `DEPLOYMENT_ENV`: Explicit environment setting (recommended)
  - `local` or `development`: http://localhost:5001
  - `staging`: https://dev.guestrix.ai
  - `production`: https://app.guestrix.ai

### Legacy Configuration (fallback)
- `FLASK_ENV`: Flask environment setting
  - `development`: http://localhost:5001
  - (other values): https://app.guestrix.ai
- `DEBUG_MODE`: Debug mode flag
  - `true`, `1`, `yes`: http://localhost:5001
  - (other values): https://app.guestrix.ai

## Environment Setup Examples

### Local Development
```bash
export DEPLOYMENT_ENV=local
# OR
export FLASK_ENV=development
# OR
export DEBUG_MODE=true
```

### Staging Environment
```bash
export DEPLOYMENT_ENV=staging
```

### Production Environment
```bash
export DEPLOYMENT_ENV=production
# OR (default fallback)
# No environment variables set
```

## Base URL Override
You can override the environment-based URL generation by passing a `base_url` parameter:

```python
from concierge.utils.firestore_client import generate_magic_link_url

# Override with custom base URL
url = generate_magic_link_url(token, base_url="https://custom.domain.com")
```

## Testing
Run the test script to verify environment configuration:
```bash
python test_magic_link_environments.py
```
"""
    
    try:
        with open('MAGIC_LINK_ENVIRONMENT_CONFIG.md', 'w') as f:
            f.write(doc_content)
        print("✅ Documentation created: MAGIC_LINK_ENVIRONMENT_CONFIG.md")
        return True
    except Exception as e:
        print(f"❌ Error creating documentation: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Magic Link Environment Testing\n")
    
    # Run tests
    env_test_passed = test_environment_detection()
    base_url_test_passed = test_base_url_override()
    doc_created = create_environment_documentation()
    
    print(f"\n📊 Final Results:")
    print(f"Environment Detection: {'✅ PASS' if env_test_passed else '❌ FAIL'}")
    print(f"Base URL Override: {'✅ PASS' if base_url_test_passed else '❌ FAIL'}")
    print(f"Documentation Created: {'✅ PASS' if doc_created else '❌ FAIL'}")
    
    if env_test_passed and base_url_test_passed:
        print(f"\n🎉 All tests passed! Magic link generation is ready for all environments.")
        print(f"\n📝 Next steps:")
        print(f"1. Set DEPLOYMENT_ENV=staging in your staging environment")
        print(f"2. Set DEPLOYMENT_ENV=production in your production environment")
        print(f"3. Use DEPLOYMENT_ENV=local for local development")
        return True
    else:
        print(f"\n❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 