#!/usr/bin/env python3
"""
Test the modal house rules extraction specifically.
"""

import sys
import os
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def test_modal_extraction():
    """Test the modal extraction with a fresh session."""
    
    test_url = "https://www.airbnb.com/rooms/973815691982105805"
    
    print(f"🧪 TESTING MODAL HOUSE RULES EXTRACTION")
    print(f"URL: {test_url}")
    print("=" * 80)
    
    scraper = AirbnbScraper(use_selenium=True, headless=True)
    
    try:
        # Step 1: Extract with enhanced modal detection
        print("🔍 Step 1: Extracting with enhanced modal detection...")
        extracted_data = scraper.extract_deep_property_data(test_url)
        
        if not extracted_data:
            print("❌ Failed to extract deep property data")
            return
            
        house_rules = extracted_data.get('house_rules', [])
        
        print(f"✅ Deep data extracted:")
        print(f"  - House rules: {len(house_rules)}")
        
        # Step 2: Analyze extracted rules
        print(f"\n📜 DETAILED HOUSE RULES ANALYSIS:")
        
        if house_rules:
            # Categorize rules by source
            modal_rules = [rule for rule in house_rules if rule.get('source') == 'airbnb_modal_extraction']
            pattern_rules = [rule for rule in house_rules if rule.get('source') == 'airbnb_pattern_extraction']
            structure_rules = [rule for rule in house_rules if rule.get('source') == 'airbnb_structure_extraction']
            
            print(f"  - Modal rules: {len(modal_rules)}")
            print(f"  - Pattern rules: {len(pattern_rules)}")
            print(f"  - Structure rules: {len(structure_rules)}")
            
            print(f"\n📋 ALL EXTRACTED RULES:")
            for i, rule in enumerate(house_rules):
                title = rule.get('title', 'No title')
                description = rule.get('description', 'No description')
                source = rule.get('source', 'unknown')
                rule_type = rule.get('type', 'rule')
                print(f"  {i+1}. [{source}] ({rule_type}) {title}")
                if description != title:
                    print(f"     Description: {description}")
            
            # Check for expected rules from screenshot
            expected_rules = [
                'Check-in after 3:00 PM',
                'Checkout before 11:00 AM', 
                'Self check-in with smart lock',
                '5 guests maximum',
                'No pets',
                'Quiet hours',
                'No parties or events',
                'No commercial photography',
                'No smoking',
                'Throw trash away',
                'Turn things off',
                'Return keys',
                'Lock up'
            ]
            
            print(f"\n🎯 EXPECTED RULES CHECK:")
            found_count = 0
            for expected in expected_rules:
                found = False
                for rule in house_rules:
                    if expected.lower() in rule.get('title', '').lower() or expected.lower() in rule.get('description', '').lower():
                        found = True
                        found_count += 1
                        break
                
                status = "✅ FOUND" if found else "❌ MISSING"
                print(f"  {status}: {expected}")
            
            print(f"\n📊 EXTRACTION SUMMARY:")
            print(f"  - Total rules extracted: {len(house_rules)}")
            print(f"  - Expected rules found: {found_count}/{len(expected_rules)}")
            print(f"  - Success rate: {(found_count/len(expected_rules)*100):.1f}%")
            
            if found_count >= 10:
                print("🎉 EXCELLENT: Most rules successfully extracted!")
            elif found_count >= 6:
                print("👍 GOOD: Majority of rules extracted")
            elif found_count >= 3:
                print("⚠️  PARTIAL: Some rules extracted, needs improvement")
            else:
                print("❌ POOR: Very few rules extracted")
                
        else:
            print("  ❌ No house rules found")
        
        return len(house_rules), len([r for r in house_rules if r.get('source') == 'airbnb_modal_extraction'])
        
    except Exception as e:
        print(f"❌ Error during modal extraction test: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0
    finally:
        # Clean up
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass

if __name__ == "__main__":
    test_modal_extraction()
