#!/usr/bin/env python3
"""
Test the enhanced house rules extraction on multiple listing URLs.
"""

import sys
import os
import time
import json

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.airbnb_scraper import AirbnbScraper

def test_multiple_listings():
    """Test enhanced extraction on multiple listings."""
    
    # Test URLs with different house rules configurations
    test_listings = [
        {
            "url": "https://www.airbnb.com/rooms/700299802944952028",
            "name": "Saint Francis Townhouse",
            "expected_rules": [
                "Check-in after 3:00 PM", "Checkout before 11:00 AM", "Self check-in with smart lock",
                "6 guests maximum", "Pets allowed", "Quiet hours 9:00 PM - 7:00 AM",
                "No parties or events", "No commercial photography", "No smoking",
                "No smoking or vaping, no parties allowed", "<PERSON><PERSON> used towels", "Turn things off", "Lock up"
            ]
        },
        {
            "url": "https://www.airbnb.com/rooms/973815691982105805",
            "name": "Previous Test Property",
            "expected_rules": [
                "Check-in after 3:00 PM", "Checkout before 11:00 AM", "Self check-in with smart lock",
                "5 guests maximum", "No pets", "Quiet hours 10:00 PM - 7:00 AM",
                "No parties or events", "No commercial photography", "No smoking",
                "Throw trash away", "Turn things off", "Return keys", "Lock up"
            ]
        },
        {
            "url": "https://www.airbnb.com/rooms/53339040",
            "name": "Alternative Test Property",
            "expected_rules": []  # We'll discover what rules this has
        }
    ]
    
    print(f"🔍 TESTING ENHANCED HOUSE RULES EXTRACTION ON MULTIPLE LISTINGS")
    print("=" * 80)
    
    results = []
    
    for i, listing in enumerate(test_listings):
        print(f"\n📋 TESTING LISTING {i+1}/{len(test_listings)}")
        print(f"Name: {listing['name']}")
        print(f"URL: {listing['url']}")
        print(f"Expected rules: {len(listing['expected_rules'])}")
        print("-" * 60)
        
        scraper = AirbnbScraper(use_selenium=True, headless=True)
        
        try:
            start_time = time.time()
            
            # Extract deep property data
            extracted_data = scraper.extract_deep_property_data(listing['url'])
            house_rules = extracted_data.get('house_rules', [])
            
            extraction_time = time.time() - start_time
            
            print(f"✅ EXTRACTION COMPLETED in {extraction_time:.1f}s")
            print(f"Total rules extracted: {len(house_rules)}")
            
            # Analyze results
            extracted_descriptions = [rule['description'] for rule in house_rules]
            
            print(f"\n📊 EXTRACTED RULES:")
            for j, rule in enumerate(house_rules):
                rule_type = rule.get('type', 'unknown')
                description = rule.get('description', 'N/A')
                print(f"  {j+1}. [{rule_type}] {description}")
            
            # Compare with expected rules if available
            if listing['expected_rules']:
                found_rules = []
                missing_rules = []
                
                for expected in listing['expected_rules']:
                    found = False
                    for extracted in extracted_descriptions:
                        if (expected.lower() in extracted.lower() or 
                            extracted.lower() in expected.lower() or
                            any(term in extracted.lower() for term in expected.lower().split() if len(term) > 3)):
                            found_rules.append((expected, extracted))
                            found = True
                            break
                    
                    if not found:
                        missing_rules.append(expected)
                
                success_rate = (len(found_rules) / len(listing['expected_rules'])) * 100
                
                print(f"\n🎯 MATCHING ANALYSIS:")
                print(f"✅ Found: {len(found_rules)}/{len(listing['expected_rules'])} ({success_rate:.1f}%)")
                print(f"❌ Missing: {len(missing_rules)}")
                
                if missing_rules:
                    print(f"\nMissing rules:")
                    for missing in missing_rules[:5]:  # Show first 5
                        print(f"  - {missing}")
                    if len(missing_rules) > 5:
                        print(f"  ... and {len(missing_rules) - 5} more")
            else:
                success_rate = None
                found_rules = []
                missing_rules = []
            
            # Store results
            result = {
                'listing': listing,
                'extraction_time': extraction_time,
                'extracted_rules': house_rules,
                'success_rate': success_rate,
                'found_rules': len(found_rules) if found_rules else 0,
                'missing_rules': len(missing_rules) if missing_rules else 0
            }
            results.append(result)
            
        except Exception as e:
            print(f"❌ Error extracting from {listing['name']}: {e}")
            result = {
                'listing': listing,
                'error': str(e),
                'extracted_rules': [],
                'success_rate': 0
            }
            results.append(result)
        
        finally:
            # Clean up
            if hasattr(scraper, 'driver') and scraper.driver:
                try:
                    scraper.driver.quit()
                except:
                    pass
        
        print(f"\n" + "="*60)
    
    # Overall summary
    print(f"\n📊 OVERALL SUMMARY:")
    print("=" * 80)
    
    successful_extractions = [r for r in results if 'error' not in r]
    failed_extractions = [r for r in results if 'error' in r]
    
    print(f"Successful extractions: {len(successful_extractions)}/{len(results)}")
    print(f"Failed extractions: {len(failed_extractions)}")
    
    if successful_extractions:
        avg_rules = sum(len(r['extracted_rules']) for r in successful_extractions) / len(successful_extractions)
        avg_time = sum(r['extraction_time'] for r in successful_extractions) / len(successful_extractions)
        
        success_rates = [r['success_rate'] for r in successful_extractions if r['success_rate'] is not None]
        avg_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0
        
        print(f"Average rules per listing: {avg_rules:.1f}")
        print(f"Average extraction time: {avg_time:.1f}s")
        if success_rates:
            print(f"Average success rate: {avg_success_rate:.1f}%")
        
        print(f"\nDetailed results:")
        for i, result in enumerate(successful_extractions):
            name = result['listing']['name']
            rules_count = len(result['extracted_rules'])
            success_rate = result.get('success_rate', 'N/A')
            print(f"  {i+1}. {name}: {rules_count} rules ({success_rate}% match)")
    
    # Save results to file
    with open('extraction_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print(f"\n💾 Results saved to extraction_test_results.json")
    
    # Determine overall success
    overall_success = len(successful_extractions) >= 2 and avg_success_rate >= 70 if success_rates else len(successful_extractions) >= 2
    
    return overall_success

if __name__ == "__main__":
    success = test_multiple_listings()
    if success:
        print(f"\n🎉 MULTI-LISTING TEST PASSED!")
    else:
        print(f"\n❌ MULTI-LISTING TEST FAILED")
    
    sys.exit(0 if success else 1)
