<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Facts Revision Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'persian-green': '#00A693',
                        'saffron': '#F4C430',
                        'dark-purple': '#2D1B69'
                    }
                }
            }
        }
    </script>
    <style>
        .rotate-180 {
            transform: rotate(180deg);
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-dark-purple mb-6">Property Facts Step Revision Test</h1>
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <i class="fas fa-home text-persian-green text-2xl mr-3"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900">Property Facts</h3>
                        <p class="text-sm text-gray-600" id="property-facts-count">15 questions</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <i class="fas fa-map-marker-alt text-persian-green text-2xl mr-3"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900">Locating Things</h3>
                        <p class="text-sm text-gray-600" id="locating-things-count">20 questions</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <i class="fas fa-star text-persian-green text-2xl mr-3"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900">Host Recommendations</h3>
                        <p class="text-sm text-gray-600" id="host-recommendations-count">12 questions</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Facts Step Content -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div id="property-facts-content">
                <!-- Content will be loaded here -->
            </div>
            
            <!-- Test Controls -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-4">Test Controls</h3>
                <div class="flex flex-wrap gap-4">
                    <button onclick="loadPropertyFactsStep()" class="bg-persian-green text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Load Property Facts Step
                    </button>
                    <button onclick="expandAllSections()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Expand All Sections
                    </button>
                    <button onclick="collapseAllSections()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Collapse All Sections
                    </button>
                    <button onclick="testSectionToggle()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Test Section Toggle
                    </button>
                    <button onclick="showQuestionAnalysis()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Show Analysis
                    </button>
                    <button onclick="testCustomFacts()" class="bg-orange-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Test Custom Facts
                    </button>
                </div>
            </div>
        </div>

        <!-- Analysis Results -->
        <div id="analysis-results" class="mt-8 bg-white rounded-lg shadow-lg p-6 hidden">
            <h2 class="text-xl font-semibold mb-4">Question Analysis</h2>
            <div id="analysis-content" class="space-y-4">
                <!-- Analysis content will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // Mock Property Setup Modal for testing
        class MockPropertySetupModal {
            constructor() {
                this.propertyFactsData = {
                    propertyFacts: {
                        title: "Property Facts",
                        icon: "fas fa-home",
                        description: "Property-specific information that guests can't find elsewhere",
                        questions: [
                            // Basic Property Characteristics (most fundamental info first)
                            "What is the maximum occupancy for this property?",
                            "How many people can comfortably sit at the dining table?",
                            "Are there stairs inside the property that guests need to navigate?",

                            // Comfort & Environment (grouped together)
                            "Is there air conditioning available in all rooms?",
                            "What's the water pressure like in the shower?",
                            "What's the noise level like in this area?",

                            // Technology & Connectivity (grouped together)
                            "What's the internet speed like for video calls and streaming?",
                            "How's the cell phone signal strength at the property?",
                            "Which cell phone carrier works best in this area?",

                            // Practical Considerations (safety and logistics)
                            "Is parking included, and where exactly should guests park?",
                            "Can guests safely drink the tap water for drinking and cooking?",
                            "Are there any construction or renovation projects happening nearby?",

                            // Unique Property Features (most specific to this property)
                            "What wildlife might guests see around the property?",
                            "Are there any unique quirks about the property guests should know?",
                            "What should guests be particularly careful about at this property?"
                        ]
                    },
                    locatingThings: {
                        title: "Locating Things",
                        icon: "fas fa-map-marker-alt",
                        description: "Help guests find items and amenities in your property",
                        questions: [
                            // Bedroom & Sleeping (start with where guests first go)
                            "What's the bedding configuration in each room?",
                            "Where can guests find extra pillows and blankets?",
                            "Where are the extra towels and linens stored?",

                            // Bathroom Essentials (logical next area)
                            "Where do you keep the extra toilet paper?",
                            "Where is the hair dryer located?",

                            // Kitchen & Dining (daily use items)
                            "Where are the wine glasses and coffee cups?",
                            "Where is the dishwasher detergent kept?",

                            // Cleaning & Maintenance (grouped together)
                            "Where are the cleaning supplies, broom, and vacuum stored?",
                            "Where do you store the vacuum cleaner?",
                            "Where is the tool kit for minor repairs?",

                            // Laundry & Personal Care (related activities)
                            "Where are the laundry detergent and fabric softener?",
                            "Where is the iron and ironing board located?",

                            // Waste & Recycling (practical daily need)
                            "Where should guests put trash and recycling, and when is pickup?",

                            // Special Equipment & Seasonal Items (less frequent but important)
                            "Do you provide a crib, pack-n-play, or high chair?",
                            "Where are beach towels stored (if applicable)?",
                            "Where do you store seasonal items like umbrellas, fans, or heaters?",

                            // Emergency & Safety Items (important but hopefully not needed)
                            "Where is the first aid kit located?",
                            "Where do you keep flashlights and candles?",

                            // Special Features (property-specific)
                            "Is there a fireplace or fire pit, and how do guests use it safely?"
                        ]
                    },
                    hostRecommendations: {
                        title: "Host Recommendations",
                        icon: "fas fa-star",
                        description: "Share your personal recommendations and local insights",
                        questions: [
                            // Must-See & Must-Do (start with the most important)
                            "What's the one thing guests absolutely shouldn't miss in this area?",
                            "What are your favorite local attractions and things to do nearby?",
                            "What are some hidden gems that only locals know about?",

                            // Dining & Food (very common guest need)
                            "Which local restaurants and cafes do you recommend?",
                            "What's your personal favorite local coffee shop?",

                            // Family & Entertainment (specific audience needs)
                            "What do you recommend for families with children?",
                            "Are there sports channels available on the TV?",

                            // Timing & Events (helps with planning)
                            "What's the best time to visit local attractions to avoid crowds?",
                            "Are there any local events or festivals guests should know about?",

                            // Business & Practical (value-added services)
                            "Do you have partnerships or discounts with any local businesses?",
                            "Do you offer any discounts for longer stays or return visits?",

                            // Additional Resources (catch-all for other recommendations)
                            "Are there any other local contacts or resources you'd like to share?"
                        ]
                    }
                };
            }

            renderPropertyFactsSections(propertyFactsData) {
                let html = '';
                let questionIndex = 0;

                Object.keys(propertyFactsData).forEach(sectionKey => {
                    const section = propertyFactsData[sectionKey];
                    const sectionId = `section-${sectionKey}`;
                    
                    html += `
                        <div class="border border-gray-200 rounded-lg mb-4">
                            <!-- Section Header -->
                            <div class="p-4 bg-gray-50 border-b border-gray-200 cursor-pointer" 
                                 onclick="testModal.togglePropertyFactsSection('${sectionId}')">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="${section.icon} text-persian-green mr-3"></i>
                                        <div>
                                            <h5 class="font-medium text-gray-900">${section.title}</h5>
                                            <p class="text-sm text-gray-600">${section.description}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-500">${section.questions.length} questions</span>
                                        <i class="fas fa-chevron-down transition-transform duration-200" id="${sectionId}-chevron"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Section Content (Initially Hidden) -->
                            <div class="hidden p-4 space-y-4" id="${sectionId}-content">
                                ${section.questions.map(question => {
                                    const currentIndex = questionIndex++;
                                    return `
                                        <div class="bg-white p-3 rounded border border-gray-100 property-fact-item" data-type="default">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                ${question}
                                            </label>
                                            <textarea
                                                id="fact-${currentIndex}"
                                                name="fact-${currentIndex}"
                                                data-question="${question}"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green resize-none"
                                                rows="2"
                                                placeholder="Enter your answer (optional)..."
                                            ></textarea>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                });

                return html;
            }

            togglePropertyFactsSection(sectionId) {
                const content = document.getElementById(`${sectionId}-content`);
                const chevron = document.getElementById(`${sectionId}-chevron`);

                if (content && chevron) {
                    const isHidden = content.classList.contains('hidden');

                    if (isHidden) {
                        content.classList.remove('hidden');
                        chevron.classList.add('rotate-180');
                    } else {
                        content.classList.add('hidden');
                        chevron.classList.remove('rotate-180');
                    }
                }
            }

            showCustomPropertyFactForm() {
                const form = document.getElementById('custom-fact-form');
                const button = document.getElementById('add-custom-fact-btn');
                const contentInput = document.getElementById('custom-fact-content');

                if (form && button) {
                    form.classList.remove('hidden');
                    button.classList.add('hidden');
                    contentInput?.focus();
                }
            }

            cancelCustomPropertyFact() {
                const form = document.getElementById('custom-fact-form');
                const button = document.getElementById('add-custom-fact-btn');
                const contentInput = document.getElementById('custom-fact-content');

                if (form && button) {
                    form.classList.add('hidden');
                    button.classList.remove('hidden');
                    if (contentInput) {
                        contentInput.value = '';
                    }
                }
            }

            saveCustomPropertyFact() {
                const contentInput = document.getElementById('custom-fact-content');
                const content = contentInput?.value?.trim() || '';

                if (!content) {
                    alert('Please enter a property fact before saving it.');
                    contentInput?.focus();
                    return;
                }

                // Generate unique ID for the custom fact
                const customFactId = `custom-fact-${Date.now()}`;

                // Add to the custom facts list
                const customFactsList = document.getElementById('custom-facts-list');
                if (customFactsList) {
                    const newFactHtml = `
                        <div class="bg-green-50 p-4 rounded-lg border border-green-200 custom-fact-item" data-fact-id="${customFactId}">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <i class="fas fa-plus-circle text-green-600 mr-2"></i>
                                        <span class="text-sm font-medium text-green-800">Custom Property Fact</span>
                                    </div>
                                    <p class="text-gray-700 text-sm">${content}</p>
                                </div>
                                <button onclick="testModal.removeCustomPropertyFact('${customFactId}')"
                                        class="text-red-600 hover:text-red-800 ml-3 p-1 rounded hover:bg-red-100 transition-colors"
                                        title="Remove this fact">
                                    <i class="fas fa-trash-alt text-sm"></i>
                                </button>
                            </div>
                        </div>
                    `;

                    customFactsList.insertAdjacentHTML('beforeend', newFactHtml);
                }

                // Hide form and show button
                this.cancelCustomPropertyFact();

                console.log('Added custom property fact:', content);
            }

            removeCustomPropertyFact(factId) {
                const factElement = document.querySelector(`[data-fact-id="${factId}"]`);
                if (factElement) {
                    factElement.remove();
                }
            }
        }

        // Global test instance
        let testModal = new MockPropertySetupModal();

        function loadPropertyFactsStep() {
            const content = document.getElementById('property-facts-content');
            content.innerHTML = `
                <div>
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold text-dark-purple mb-2">
                            <i class="fas fa-clipboard-list text-persian-green mr-2"></i>
                            Property Information
                        </h4>
                        <p class="text-gray-600 mb-4">
                            Provide property-specific information that guests can't find elsewhere. This helps your AI assistant answer unique questions about your property.
                            <strong>All questions are optional</strong> - only answer what's relevant to your property.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                                <p class="text-sm text-blue-800">
                                    <strong>Focus on unique insights:</strong> Skip anything guests can find via web search. Share property quirks, exact locations of items, and personal recommendations only you would know.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-6" id="property-facts-form">
                        ${testModal.renderPropertyFactsSections(testModal.propertyFactsData)}
                    </div>

                    <!-- Custom Property Facts Section -->
                    <div class="mt-8">
                        <!-- Custom Facts List -->
                        <div id="custom-facts-list" class="space-y-3 mb-4">
                            <!-- Custom facts will be added here dynamically -->
                        </div>

                        <!-- Add Custom Fact Form (Initially Hidden) -->
                        <div id="custom-fact-form" class="hidden p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                            <h6 class="font-medium text-gray-900 mb-3">Add Custom Property Fact</h6>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Property Fact</label>
                                    <textarea id="custom-fact-content"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green resize-none"
                                              rows="3"
                                              placeholder="Enter a useful fact about your property (e.g., 'The WiFi password is written on the router', 'Extra towels are in the hall closet')..."></textarea>
                                </div>
                                <div class="flex space-x-3">
                                    <button onclick="testModal.saveCustomPropertyFact()"
                                            class="inline-flex items-center px-4 py-2 bg-persian-green text-white rounded-lg hover:bg-opacity-90 transition-colors">
                                        <i class="fas fa-check mr-2"></i>
                                        Save Fact
                                    </button>
                                    <button onclick="testModal.cancelCustomPropertyFact()"
                                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Add Custom Fact Button -->
                        <button id="add-custom-fact-btn"
                                onclick="testModal.showCustomPropertyFactForm()"
                                class="inline-flex items-center px-4 py-2 border border-persian-green text-persian-green rounded-lg hover:bg-persian-green hover:text-white transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Add Custom Property Fact
                        </button>
                    </div>
                </div>
            `;
        }

        function expandAllSections() {
            ['section-propertyFacts', 'section-locatingThings', 'section-hostRecommendations'].forEach(sectionId => {
                const content = document.getElementById(`${sectionId}-content`);
                const chevron = document.getElementById(`${sectionId}-chevron`);
                
                if (content && chevron && content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    chevron.classList.add('rotate-180');
                }
            });
        }

        function collapseAllSections() {
            ['section-propertyFacts', 'section-locatingThings', 'section-hostRecommendations'].forEach(sectionId => {
                const content = document.getElementById(`${sectionId}-content`);
                const chevron = document.getElementById(`${sectionId}-chevron`);
                
                if (content && chevron && !content.classList.contains('hidden')) {
                    content.classList.add('hidden');
                    chevron.classList.remove('rotate-180');
                }
            });
        }

        function testSectionToggle() {
            // Test toggling each section
            setTimeout(() => testModal.togglePropertyFactsSection('section-propertyFacts'), 500);
            setTimeout(() => testModal.togglePropertyFactsSection('section-locatingThings'), 1000);
            setTimeout(() => testModal.togglePropertyFactsSection('section-hostRecommendations'), 1500);
        }

        function showQuestionAnalysis() {
            const analysisDiv = document.getElementById('analysis-results');
            const analysisContent = document.getElementById('analysis-content');

            const data = testModal.propertyFactsData;
            let totalQuestions = 0;
            let analysisHTML = '';

            Object.keys(data).forEach(sectionKey => {
                const section = data[sectionKey];
                totalQuestions += section.questions.length;

                analysisHTML += `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">
                            <i class="${section.icon} text-persian-green mr-2"></i>
                            ${section.title}
                        </h3>
                        <p class="text-sm text-gray-600 mb-3">${section.description}</p>
                        <div class="bg-gray-50 p-3 rounded">
                            <p class="text-sm"><strong>Questions:</strong> ${section.questions.length}</p>
                            <p class="text-sm"><strong>Sample questions:</strong></p>
                            <ul class="text-xs text-gray-600 mt-1 ml-4">
                                ${section.questions.slice(0, 3).map(q => `<li>• ${q}</li>`).join('')}
                                ${section.questions.length > 3 ? `<li>• ... and ${section.questions.length - 3} more</li>` : ''}
                            </ul>
                        </div>
                    </div>
                `;
            });

            analysisHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <h3 class="font-semibold text-blue-900 mb-2">Overall Summary</h3>
                    <p class="text-sm text-blue-800">
                        <strong>Total Questions:</strong> ${totalQuestions} (was 25 originally, removed 4 location-based)
                    </p>
                    <p class="text-sm text-blue-800">
                        <strong>Improvement:</strong> +22 new questions based on guest research
                    </p>
                    <p class="text-sm text-blue-800">
                        <strong>Organization:</strong> Grouped into 3 folded sections for better UX
                    </p>
                    <p class="text-sm text-blue-800">
                        <strong>Custom Facts:</strong> Enhanced UI with dynamic saving and delete functionality
                    </p>
                </div>
            ` + analysisHTML;

            analysisContent.innerHTML = analysisHTML;
            analysisDiv.classList.remove('hidden');
        }

        function testCustomFacts() {
            // Simulate adding some custom facts
            const sampleFacts = [
                "The WiFi password is written on a sticky note on the router",
                "Extra blankets are stored in the hall closet on the top shelf",
                "The coffee machine needs to be turned on at the wall switch first"
            ];

            sampleFacts.forEach((fact, index) => {
                setTimeout(() => {
                    // Show form
                    testModal.showCustomPropertyFactForm();

                    // Fill content
                    setTimeout(() => {
                        const contentInput = document.getElementById('custom-fact-content');
                        if (contentInput) {
                            contentInput.value = fact;

                            // Save fact
                            setTimeout(() => {
                                testModal.saveCustomPropertyFact();
                            }, 500);
                        }
                    }, 200);
                }, index * 1500);
            });
        }

        // Auto-load on page load
        window.addEventListener('load', () => {
            loadPropertyFactsStep();
        });
    </script>
</body>
</html>
