<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Facts Revision Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'persian-green': '#00A693',
                        'saffron': '#F4C430',
                        'dark-purple': '#2D1B69'
                    }
                }
            }
        }
    </script>
    <style>
        .rotate-180 {
            transform: rotate(180deg);
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-dark-purple mb-6">Property Facts Step Revision Test</h1>
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <i class="fas fa-home text-persian-green text-2xl mr-3"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900">Property Facts</h3>
                        <p class="text-sm text-gray-600" id="property-facts-count">15 questions</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <i class="fas fa-map-marker-alt text-persian-green text-2xl mr-3"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900">Locating Things</h3>
                        <p class="text-sm text-gray-600" id="locating-things-count">20 questions</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <i class="fas fa-star text-persian-green text-2xl mr-3"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900">Host Recommendations</h3>
                        <p class="text-sm text-gray-600" id="host-recommendations-count">12 questions</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Property Facts Step Content -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div id="property-facts-content">
                <!-- Content will be loaded here -->
            </div>
            
            <!-- Test Controls -->
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-4">Test Controls</h3>
                <div class="flex flex-wrap gap-4">
                    <button onclick="loadPropertyFactsStep()" class="bg-persian-green text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Load Property Facts Step
                    </button>
                    <button onclick="expandAllSections()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Expand All Sections
                    </button>
                    <button onclick="collapseAllSections()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Collapse All Sections
                    </button>
                    <button onclick="testSectionToggle()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Test Section Toggle
                    </button>
                    <button onclick="showQuestionAnalysis()" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-opacity-90">
                        Show Analysis
                    </button>
                </div>
            </div>
        </div>

        <!-- Analysis Results -->
        <div id="analysis-results" class="mt-8 bg-white rounded-lg shadow-lg p-6 hidden">
            <h2 class="text-xl font-semibold mb-4">Question Analysis</h2>
            <div id="analysis-content" class="space-y-4">
                <!-- Analysis content will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // Mock Property Setup Modal for testing
        class MockPropertySetupModal {
            constructor() {
                this.propertyFactsData = {
                    propertyFacts: {
                        title: "Property Facts",
                        icon: "fas fa-home",
                        description: "Property-specific information that guests can't find elsewhere",
                        questions: [
                            "Are there any stairs in the property?",
                            "What's the noise level like in the area?",
                            "Is there a cell phone signal?",
                            "What is the maximum occupancy?",
                            "Can we use the water from the taps for drinking/cooking?",
                            "What sort of wild animals might we see?",
                            "Any things I should be careful about?",
                            "How many people does the dining table seat?",
                            "Is there air conditioning in all rooms?",
                            "What's the water pressure like in the shower?",
                            "Are there any quirks about the property I should know?",
                            "Is parking included and where exactly?",
                            "What's the internet speed like for video calls?",
                            "Are there any construction/renovation projects nearby?",
                            "What's the best cell phone carrier in this area?"
                        ]
                    },
                    locatingThings: {
                        title: "Locating Things",
                        icon: "fas fa-map-marker-alt",
                        description: "Help guests find items and amenities in your property",
                        questions: [
                            "Where are the extra towels/linens?",
                            "Where are extra blankets/comforters?",
                            "Where do I put the trash/recycling? When is trash pickup?",
                            "Where's the dishwasher detergent?",
                            "Where are the cleaning supplies/broom/vacuum?",
                            "What's the bedding configuration in each room?",
                            "Do you have a crib/pack-n-play or high chair available?",
                            "Is there a fireplace/fire pit? How do I use it?",
                            "Where are the wine glasses/coffee cups?",
                            "Where do you keep the extra toilet paper?",
                            "Where's the iron and ironing board?",
                            "Where are the beach towels (if applicable)?",
                            "Where's the hair dryer?",
                            "Where do you store the vacuum cleaner?",
                            "Where are the extra pillows?",
                            "Where's the first aid kit?",
                            "Where do you keep the flashlights/candles?",
                            "Where are the laundry detergent and fabric softener?",
                            "Where's the tool kit for minor repairs?",
                            "Where do you store seasonal items (umbrellas, fans, heaters)?"
                        ]
                    },
                    hostRecommendations: {
                        title: "Host Recommendations",
                        icon: "fas fa-star",
                        description: "Share your personal recommendations and local insights",
                        questions: [
                            "Any local numbers/recommendations you want to share?",
                            "Can you recommend local restaurants/cafes?",
                            "What are some popular local attractions/things to do nearby?",
                            "Local recommendation for family fun?",
                            "Are there sports channels on the TV?",
                            "Do you offer discounts for longer stays/return visits?",
                            "What's your favorite local coffee shop?",
                            "Any hidden gems only locals know about?",
                            "Best time to visit local attractions to avoid crowds?",
                            "Any local events or festivals during typical stay periods?",
                            "What's the one thing guests absolutely shouldn't miss?",
                            "Any local businesses you have partnerships/discounts with?"
                        ]
                    }
                };
            }

            renderPropertyFactsSections(propertyFactsData) {
                let html = '';
                let questionIndex = 0;

                Object.keys(propertyFactsData).forEach(sectionKey => {
                    const section = propertyFactsData[sectionKey];
                    const sectionId = `section-${sectionKey}`;
                    
                    html += `
                        <div class="border border-gray-200 rounded-lg mb-4">
                            <!-- Section Header -->
                            <div class="p-4 bg-gray-50 border-b border-gray-200 cursor-pointer" 
                                 onclick="testModal.togglePropertyFactsSection('${sectionId}')">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="${section.icon} text-persian-green mr-3"></i>
                                        <div>
                                            <h5 class="font-medium text-gray-900">${section.title}</h5>
                                            <p class="text-sm text-gray-600">${section.description}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm text-gray-500">${section.questions.length} questions</span>
                                        <i class="fas fa-chevron-down transition-transform duration-200" id="${sectionId}-chevron"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Section Content (Initially Hidden) -->
                            <div class="hidden p-4 space-y-4" id="${sectionId}-content">
                                ${section.questions.map(question => {
                                    const currentIndex = questionIndex++;
                                    return `
                                        <div class="bg-white p-3 rounded border border-gray-100 property-fact-item" data-type="default">
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                ${question}
                                            </label>
                                            <textarea
                                                id="fact-${currentIndex}"
                                                name="fact-${currentIndex}"
                                                data-question="${question}"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green resize-none"
                                                rows="2"
                                                placeholder="Enter your answer (optional)..."
                                            ></textarea>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                });

                return html;
            }

            togglePropertyFactsSection(sectionId) {
                const content = document.getElementById(`${sectionId}-content`);
                const chevron = document.getElementById(`${sectionId}-chevron`);
                
                if (content && chevron) {
                    const isHidden = content.classList.contains('hidden');
                    
                    if (isHidden) {
                        content.classList.remove('hidden');
                        chevron.classList.add('rotate-180');
                    } else {
                        content.classList.add('hidden');
                        chevron.classList.remove('rotate-180');
                    }
                }
            }
        }

        // Global test instance
        let testModal = new MockPropertySetupModal();

        function loadPropertyFactsStep() {
            const content = document.getElementById('property-facts-content');
            content.innerHTML = `
                <div>
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold text-dark-purple mb-2">
                            <i class="fas fa-clipboard-list text-persian-green mr-2"></i>
                            Property Information
                        </h4>
                        <p class="text-gray-600 mb-4">
                            Provide property-specific information that guests can't find elsewhere. This helps your AI assistant answer unique questions about your property.
                            <strong>All questions are optional</strong> - only answer what's relevant to your property.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <div class="flex items-start">
                                <i class="fas fa-info-circle text-blue-600 mr-2 mt-0.5"></i>
                                <p class="text-sm text-blue-800">
                                    <strong>Focus on unique insights:</strong> Skip anything guests can find via web search. Share property quirks, exact locations of items, and personal recommendations only you would know.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-6" id="property-facts-form">
                        ${testModal.renderPropertyFactsSections(testModal.propertyFactsData)}
                    </div>
                </div>
            `;
        }

        function expandAllSections() {
            ['section-propertyFacts', 'section-locatingThings', 'section-hostRecommendations'].forEach(sectionId => {
                const content = document.getElementById(`${sectionId}-content`);
                const chevron = document.getElementById(`${sectionId}-chevron`);
                
                if (content && chevron && content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    chevron.classList.add('rotate-180');
                }
            });
        }

        function collapseAllSections() {
            ['section-propertyFacts', 'section-locatingThings', 'section-hostRecommendations'].forEach(sectionId => {
                const content = document.getElementById(`${sectionId}-content`);
                const chevron = document.getElementById(`${sectionId}-chevron`);
                
                if (content && chevron && !content.classList.contains('hidden')) {
                    content.classList.add('hidden');
                    chevron.classList.remove('rotate-180');
                }
            });
        }

        function testSectionToggle() {
            // Test toggling each section
            setTimeout(() => testModal.togglePropertyFactsSection('section-propertyFacts'), 500);
            setTimeout(() => testModal.togglePropertyFactsSection('section-locatingThings'), 1000);
            setTimeout(() => testModal.togglePropertyFactsSection('section-hostRecommendations'), 1500);
        }

        function showQuestionAnalysis() {
            const analysisDiv = document.getElementById('analysis-results');
            const analysisContent = document.getElementById('analysis-content');
            
            const data = testModal.propertyFactsData;
            let totalQuestions = 0;
            let analysisHTML = '';
            
            Object.keys(data).forEach(sectionKey => {
                const section = data[sectionKey];
                totalQuestions += section.questions.length;
                
                analysisHTML += `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-semibold text-gray-900 mb-2">
                            <i class="${section.icon} text-persian-green mr-2"></i>
                            ${section.title}
                        </h3>
                        <p class="text-sm text-gray-600 mb-3">${section.description}</p>
                        <div class="bg-gray-50 p-3 rounded">
                            <p class="text-sm"><strong>Questions:</strong> ${section.questions.length}</p>
                            <p class="text-sm"><strong>Sample questions:</strong></p>
                            <ul class="text-xs text-gray-600 mt-1 ml-4">
                                ${section.questions.slice(0, 3).map(q => `<li>• ${q}</li>`).join('')}
                                ${section.questions.length > 3 ? `<li>• ... and ${section.questions.length - 3} more</li>` : ''}
                            </ul>
                        </div>
                    </div>
                `;
            });
            
            analysisHTML = `
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <h3 class="font-semibold text-blue-900 mb-2">Overall Summary</h3>
                    <p class="text-sm text-blue-800">
                        <strong>Total Questions:</strong> ${totalQuestions} (was 25 originally, removed 4 location-based)
                    </p>
                    <p class="text-sm text-blue-800">
                        <strong>Improvement:</strong> +22 new questions based on guest research
                    </p>
                    <p class="text-sm text-blue-800">
                        <strong>Organization:</strong> Grouped into 3 folded sections for better UX
                    </p>
                </div>
            ` + analysisHTML;
            
            analysisContent.innerHTML = analysisHTML;
            analysisDiv.classList.remove('hidden');
        }

        // Auto-load on page load
        window.addEventListener('load', () => {
            loadPropertyFactsStep();
        });
    </script>
</body>
</html>
