#!/usr/bin/env python3
"""
Test script to check property house rules data structure.
"""

import sys
import os
from datetime import datetime

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

from concierge.utils.firestore_client import get_property, list_knowledge_items_by_property

def test_property_house_rules():
    """Test the property house rules data structure."""
    
    # Property ID from the logs (latest property with issues)
    property_id = "44a4c8a9-e0a9-4d99-8d51-b26a6c7a7d8a"
    
    print(f"🔍 TESTING PROPERTY HOUSE RULES")
    print(f"Property ID: {property_id}")
    print("=" * 80)
    
    try:
        # Step 1: Get property data
        print("🔍 Step 1: Getting property data...")
        property_data = get_property(property_id)
        
        if not property_data:
            print("❌ Property not found")
            return
            
        print(f"✅ Property found: {property_data.get('name', 'N/A')}")
        
        # Step 2: Check import data structure
        print(f"\n🔍 Step 2: Checking import data structure...")
        import_data = property_data.get('importData', {})
        raw_data = import_data.get('rawData', {})
        
        print(f"  - Import source: {import_data.get('source', 'N/A')}")
        print(f"  - Extracted at: {import_data.get('extractedAt', 'N/A')}")
        print(f"  - Raw data keys: {list(raw_data.keys())}")
        
        # Check for house rules in different locations
        house_rules_direct = raw_data.get('house_rules', [])
        extracted_data = raw_data.get('extracted', {})
        house_rules_extracted = extracted_data.get('house_rules', [])
        
        print(f"  - House rules (direct): {len(house_rules_direct)}")
        print(f"  - House rules (extracted): {len(house_rules_extracted)}")
        
        # Show house rules content
        if house_rules_direct:
            print(f"\n📜 DIRECT HOUSE RULES:")
            for i, rule in enumerate(house_rules_direct):
                print(f"  {i+1}. {rule}")
                
        if house_rules_extracted:
            print(f"\n📜 EXTRACTED HOUSE RULES:")
            for i, rule in enumerate(house_rules_extracted):
                print(f"  {i+1}. {rule}")
        
        # Step 3: Get knowledge items
        print(f"\n🔍 Step 3: Getting knowledge items...")
        knowledge_items = list_knowledge_items_by_property(property_id)
        
        if knowledge_items:
            print(f"✅ Found {len(knowledge_items)} knowledge items")
            
            # Filter rule items
            rule_items = [item for item in knowledge_items if item.get('type') == 'rule']
            print(f"  - Rule items: {len(rule_items)}")
            
            # Show rule items
            if rule_items:
                print(f"\n📜 KNOWLEDGE RULE ITEMS:")
                for i, item in enumerate(rule_items):
                    print(f"  {i+1}. Type: {item.get('type', 'N/A')}")
                    print(f"     Content: {item.get('content', 'N/A')[:100]}...")
                    print(f"     Tags: {item.get('tags', [])}")
                    print()
            
            # Check for items with 'imported' tag
            imported_items = [item for item in knowledge_items if 'imported' in item.get('tags', [])]
            print(f"  - Imported items: {len(imported_items)}")
            
            if imported_items:
                print(f"\n📜 IMPORTED KNOWLEDGE ITEMS:")
                for i, item in enumerate(imported_items):
                    print(f"  {i+1}. Type: {item.get('type', 'N/A')}")
                    print(f"     Content: {item.get('content', 'N/A')[:100]}...")
                    print(f"     Tags: {item.get('tags', [])}")
                    print()
        else:
            print("❌ No knowledge items found")
        
        # Step 4: Check property times
        print(f"\n🔍 Step 4: Checking property times...")
        checkin_time = property_data.get('checkInTime', 'Not set')
        checkout_time = property_data.get('checkOutTime', 'Not set')
        
        print(f"  - Check-in time: {checkin_time}")
        print(f"  - Check-out time: {checkout_time}")
        
        # Final assessment
        print(f"\n" + "=" * 80)
        print("🎯 HOUSE RULES DATA ASSESSMENT:")
        
        has_import_data = bool(import_data)
        has_house_rules = len(house_rules_direct) > 0 or len(house_rules_extracted) > 0
        has_rule_knowledge = len([item for item in knowledge_items if item.get('type') == 'rule']) > 0
        has_times = checkin_time != 'Not set' or checkout_time != 'Not set'
        
        print(f"✅ Import data present: {'Yes' if has_import_data else 'No'}")
        print(f"✅ House rules in import: {'Yes' if has_house_rules else 'No'}")
        print(f"✅ Rule knowledge items: {'Yes' if has_rule_knowledge else 'No'}")
        print(f"✅ Check-in/out times: {'Yes' if has_times else 'No'}")
        
        # Overall assessment
        if has_import_data and (has_house_rules or has_rule_knowledge):
            print("🎉 GOOD: Property has house rules data available!")
        elif has_rule_knowledge:
            print("👍 OK: Property has rule knowledge items (fallback working)")
        else:
            print("⚠️  ISSUE: Property missing house rules data")
            
        return property_data
        
    except Exception as e:
        print(f"❌ Error during house rules test: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_property_house_rules()
