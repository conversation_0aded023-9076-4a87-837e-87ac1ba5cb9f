#!/usr/bin/env python3
"""
Test property import and analyze how extracted rules are stored and displayed in UI.
"""

import sys
import os
import json
import time

# Add the concierge directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'concierge'))

def test_property_import_and_ui():
    """Test property import and analyze storage/UI display."""
    
    print(f"🔍 TESTING PROPERTY IMPORT AND UI DISPLAY")
    print("=" * 80)
    
    # Test with the Saint Francis listing that shows good modal detection
    test_url = "https://www.airbnb.com/rooms/700299802944952028"
    
    try:
        # Import required modules
        from concierge.utils.airbnb_scraper import AirbnbScraper
        from concierge.utils.firestore_client import get_property, list_knowledge_items_by_property
        
        print(f"📋 STEP 1: EXTRACT PROPERTY DATA")
        print(f"URL: {test_url}")
        print("-" * 60)
        
        scraper = AirbnbScraper(use_selenium=True, headless=True)
        
        # Extract deep property data
        start_time = time.time()
        extracted_data = scraper.extract_deep_property_data(test_url)
        extraction_time = time.time() - start_time
        
        house_rules = extracted_data.get('house_rules', [])
        
        print(f"✅ Extraction completed in {extraction_time:.1f}s")
        print(f"Total rules extracted: {len(house_rules)}")
        
        print(f"\n📊 EXTRACTED HOUSE RULES:")
        for i, rule in enumerate(house_rules):
            rule_type = rule.get('type', 'unknown')
            description = rule.get('description', 'N/A')
            print(f"  {i+1}. [{rule_type}] {description}")
        
        # Clean up scraper
        if hasattr(scraper, 'driver') and scraper.driver:
            try:
                scraper.driver.quit()
            except:
                pass
        
        print(f"\n📋 STEP 2: CREATE PROPERTY FROM EXTRACTION")
        print("-" * 60)
        
        # Create a test host ID (you can replace with actual host ID)
        test_host_id = "test-host-123"
        
        # Create property from extraction
        scraper_for_creation = AirbnbScraper(use_selenium=False)  # No selenium needed for creation
        
        # Mock listing data
        listing_data = {
            'url': test_url,
            'title': 'Test Property Import',
            'location': 'Saint Francis, Wisconsin',
            'image_url': 'https://example.com/image.jpg'
        }
        
        property_id = scraper_for_creation.create_property_from_extraction(
            host_id=test_host_id,
            listing_data=listing_data,
            extracted_data=extracted_data
        )
        
        if property_id:
            print(f"✅ Property created with ID: {property_id}")
        else:
            print(f"❌ Failed to create property")
            return False
        
        print(f"\n📋 STEP 3: ANALYZE STORED DATA")
        print("-" * 60)
        
        # Get property record
        property_data = get_property(property_id)
        if property_data:
            print(f"✅ Property record found")
            print(f"Name: {property_data.get('name', 'N/A')}")
            print(f"Check-in time: {property_data.get('checkInTime', 'N/A')}")
            print(f"Check-out time: {property_data.get('checkOutTime', 'N/A')}")
            
            # Check house rules in property record
            property_house_rules = property_data.get('houseRules', [])
            print(f"House rules in property record: {len(property_house_rules)}")
            for i, rule in enumerate(property_house_rules):
                print(f"  {i+1}. {rule}")
        else:
            print(f"❌ Property record not found")
            return False
        
        # Get knowledge items
        knowledge_items = list_knowledge_items_by_property(property_id)
        print(f"\n📊 KNOWLEDGE ITEMS: {len(knowledge_items)} total")
        
        # Group by type
        by_type = {}
        for item in knowledge_items:
            item_type = item.get('type', 'unknown')
            if item_type not in by_type:
                by_type[item_type] = []
            by_type[item_type].append(item)
        
        for item_type, items in by_type.items():
            print(f"\n{item_type.upper()}: {len(items)} items")
            for i, item in enumerate(items):
                status = item.get('status', 'N/A')
                content = item.get('content', 'N/A')
                tags = item.get('tags', [])
                source = item.get('source', 'N/A')
                print(f"  {i+1}. [{status}] [{source}] {content[:60]}...")
                if 'house_rules' in tags or 'imported' in tags:
                    print(f"      Tags: {tags}")
        
        print(f"\n📋 STEP 4: UI DISPLAY SIMULATION")
        print("-" * 60)
        
        # Simulate how the UI would load and display house rules
        rule_items = [item for item in knowledge_items if item.get('type') == 'rule']
        imported_rule_items = [item for item in rule_items if 'imported' in item.get('tags', [])]
        
        print(f"Rule-type knowledge items: {len(rule_items)}")
        print(f"Imported rule items: {len(imported_rule_items)}")
        
        # Simulate the UI filtering logic
        print(f"\nUI would show these rules in Property Setup modal:")
        for i, item in enumerate(imported_rule_items):
            content = item.get('content', '')
            status = item.get('status', 'N/A')
            
            # Check if it's a time-related rule that should update property times
            is_time_rule = any(keyword in content.lower() for keyword in ['check-in', 'checkout', 'check in', 'check out'])
            
            if not is_time_rule:  # Only show non-time rules in UI
                print(f"  {i+1}. [{status}] {content}")
            else:
                print(f"  {i+1}. [{status}] {content} (FILTERED - updates property times)")
        
        print(f"\n📊 SUMMARY:")
        print("-" * 60)
        print(f"✅ Extraction: {len(house_rules)} rules")
        print(f"✅ Storage: {len(knowledge_items)} knowledge items ({len(rule_items)} rules)")
        print(f"✅ UI Display: {len([item for item in imported_rule_items if not any(keyword in item.get('content', '').lower() for keyword in ['check-in', 'checkout'])])} rules")
        print(f"✅ Property times: Check-in {property_data.get('checkInTime', 'N/A')}, Check-out {property_data.get('checkOutTime', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during property import test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_property_import_and_ui()
    if success:
        print(f"\n🎉 PROPERTY IMPORT TEST PASSED!")
    else:
        print(f"\n❌ PROPERTY IMPORT TEST FAILED")
    
    sys.exit(0 if success else 1)
