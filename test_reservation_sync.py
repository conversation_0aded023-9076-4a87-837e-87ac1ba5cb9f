#!/usr/bin/env python3
"""
Test script to manually run the updated update_all_reservations function
to verify that additional contacts are preserved during iCal sync.
"""

import sys
import os

# Add the concierge directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'concierge'))

def test_reservation_sync():
    """Test the updated reservation sync logic."""
    print("=" * 60)
    print("TESTING UPDATED RESERVATION SYNC")
    print("=" * 60)
    
    try:
        # Import the updated function
        from concierge.utils.reservations import update_all_reservations
        
        print("Starting manual test of update_all_reservations...")
        print("This will process all properties with iCal URLs and test the new logic.")
        print("Watch for log messages about preserving additional contacts.")
        print("-" * 60)
        
        # Run the updated function
        update_all_reservations()
        
        print("-" * 60)
        print("Test completed! Check the logs above for:")
        print("1. Messages about preserving additional contacts")
        print("2. Messages about skipping updates when no changes detected")
        print("3. Messages about preserving reservations with custom contacts")
        print("4. Overall sync statistics")
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_reservation_sync() 