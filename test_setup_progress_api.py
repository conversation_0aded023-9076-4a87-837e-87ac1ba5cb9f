#!/usr/bin/env python3
"""
Test the setup-progress API endpoint to ensure it's working correctly.
"""

import requests
import json
import sys

def test_setup_progress_api():
    """Test the setup-progress API endpoint."""
    
    print("🔍 TESTING SETUP-PROGRESS API ENDPOINT")
    print("=" * 60)
    
    # Test data (similar to what the frontend sends)
    test_data = {
        "step": 1,
        "data": {
            "name": "Test Property",
            "address": "123 Test Street, Test City, Test State",
            "description": "A test property for API testing",
            "icalUrl": "",
            "checkInTime": "15:00",
            "checkOutTime": "11:00",
            "wifiNetwork": "TestWiFi",
            "wifiPassword": "testpassword123",
            "amenities": {
                "basic": ["Kitchen", "Wifi", "Air conditioning"],
                "appliances": [
                    {"name": "TV", "location": "Living Room", "brand": "", "model": ""},
                    {"name": "Refrigerator", "location": "Kitchen", "brand": "", "model": ""}
                ]
            }
        }
    }
    
    # Test property ID (you may need to replace this with a valid property ID)
    property_id = "5a1a2550-daa7-46de-95d5-d6bc4a80af7a"
    
    # API endpoint
    url = f"http://127.0.0.1:8082/api/properties/{property_id}/setup-progress"
    
    print(f"📡 Testing API endpoint: {url}")
    print(f"📊 Test data: Step {test_data['step']}")
    print(f"📊 Amenities: {len(test_data['data']['amenities']['basic'])} basic, {len(test_data['data']['amenities']['appliances'])} appliances")
    
    try:
        # Make the API request
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"\n📡 API Response:")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ SUCCESS: {response_data}")
                return True
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {response.text}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Could not connect to {url}")
        print(f"💡 Make sure the Flask server is running on port 8082")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Timeout Error: Request took too long")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def test_emergency_info_step():
    """Test the emergency information step (step 3)."""
    
    print(f"\n🔍 TESTING EMERGENCY INFORMATION STEP")
    print("=" * 60)
    
    # Test emergency information data
    emergency_data = {
        "step": 3,
        "data": {
            "emergencyInfo": [
                {
                    "id": "fire_emergency",
                    "title": "Fire Emergency",
                    "instructions": "Call 911 immediately. Exit the building using the nearest exit.",
                    "location": "",
                    "enabled": True
                },
                {
                    "id": "first_aid",
                    "title": "First Aid Kit",
                    "instructions": "Basic first aid supplies for minor injuries.",
                    "location": "Kitchen cabinet above sink",
                    "enabled": True
                }
            ]
        }
    }
    
    property_id = "5a1a2550-daa7-46de-95d5-d6bc4a80af7a"
    url = f"http://127.0.0.1:8082/api/properties/{property_id}/setup-progress"
    
    print(f"📡 Testing Emergency Info API endpoint: {url}")
    print(f"📊 Emergency items: {len(emergency_data['data']['emergencyInfo'])}")
    
    try:
        response = requests.post(
            url,
            json=emergency_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"\n📡 Emergency Info API Response:")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print(f"✅ SUCCESS: {response_data}")
                return True
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {response.text}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing emergency info: {e}")
        return False

if __name__ == "__main__":
    print("🚀 SETUP-PROGRESS API TESTING")
    print("=" * 80)
    
    # Test basic information step
    basic_success = test_setup_progress_api()
    
    # Test emergency information step
    emergency_success = test_emergency_info_step()
    
    # Overall results
    print(f"\n📊 OVERALL RESULTS:")
    print("=" * 60)
    print(f"Basic Information (Step 1): {'✅ PASSED' if basic_success else '❌ FAILED'}")
    print(f"Emergency Information (Step 3): {'✅ PASSED' if emergency_success else '❌ FAILED'}")
    
    overall_success = basic_success and emergency_success
    print(f"Overall Test: {'✅ PASSED' if overall_success else '❌ FAILED'}")
    
    if overall_success:
        print(f"\n🎉 All API tests passed! The Property Setup modal should now work correctly.")
    else:
        print(f"\n❌ Some tests failed. Check the server logs for more details.")
    
    sys.exit(0 if overall_success else 1)
