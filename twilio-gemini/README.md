# Twilio + Gemini Live Integration

This project integrates Twilio phone calls with Google's Gemini Live API, allowing callers to have real-time voice conversations with Gemini AI through a phone call.

## How It Works

1. When someone calls your Twilio phone number, <PERSON><PERSON><PERSON> sends a webhook to your server
2. Your server responds with <PERSON>wi<PERSON> that tells <PERSON><PERSON><PERSON> to connect the call to your WebRTC client
3. The WebRTC client in your browser receives the call and establishes a connection
4. Audio from the caller is captured and sent to Gemini Live API
5. Responses from <PERSON> are played back to the caller
6. The conversation continues in real-time until the call ends

## Setup Instructions

### 1. Install Dependencies

```bash
./setup_and_run.sh
```

This will create a virtual environment, install dependencies, and start the server.

### 2. Configure Environment Variables

Copy `.env.example` to `.env` and fill in your credentials:

```bash
cp .env.example .env
nano .env
```

You'll need:
- T<PERSON><PERSON> Account SID and Auth Token
- Twilio API Key and Secret
- Twilio TwiML App SID
- Twilio Phone Number
- Gemini API Key

### 3. Expose Your Server with ngrok

In a new terminal:

```bash
ngrok http 5001
```

Note the ngrok URL (e.g., `https://abc123.ngrok-free.app`).

### 4. Configure Twilio

1. Go to the Twilio Console: https://console.twilio.com/
2. Navigate to Voice > TwiML Apps
3. Select your TwiML App
4. Set the Voice Request URL to your ngrok URL + `/voice` (e.g., `https://abc123.ngrok-free.app/voice`)
5. Save the changes

Also, make sure your Twilio phone number is configured to use this TwiML App:

1. Go to Phone Numbers > Manage > Active Numbers
2. Select your phone number
3. Under "Voice & Fax", set "A Call Comes In" to "TwiML App" and select your TwiML App
4. Save the changes

### 5. Test the Integration

1. Open your browser and go to `http://localhost:5001`
2. Call your Twilio phone number from another phone
3. The call should be connected to your browser and Gemini Live API
4. Speak naturally and Gemini will respond in real-time

## Architecture

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  Caller     │────▶│  Twilio     │────▶│  WebRTC     │
│  (Phone)    │◀────│  Phone      │◀────│  Client     │
└─────────────┘     └─────────────┘     └──────┬──────┘
                                               │
                                               │
                                        ┌──────▼──────┐
                                        │  Gemini     │
                                        │  Live API   │
                                        └─────────────┘
```

## Troubleshooting

### Common Issues

1. **WebRTC connection fails**:
   - Make sure your browser has permission to access your microphone
   - Check the browser console for any errors
   - Verify that your Twilio credentials are correct

2. **Gemini Live API connection fails**:
   - Check that your Gemini API key is valid
   - Verify that you have the correct permissions for the Gemini Live API

3. **Audio quality issues**:
   - Check the audio format and sample rate settings
   - Adjust the buffer size for smoother audio

4. **No audio response from Gemini**:
   - Check that the Web Speech API is supported in your browser
   - Verify that your browser's audio output is working
   - Check the browser console for any errors related to speech synthesis

## Current Implementation Notes

This implementation uses a simplified approach for testing:

1. Audio from the caller is captured and sent to the server
2. The server processes the audio (currently using a fixed test message)
3. The server generates a text response using Gemini's text API
4. The browser receives the text response and uses the Web Speech API to speak it

This is a temporary solution for testing. A full implementation would:

1. Send audio directly to Gemini Live API using WebSockets
2. Receive audio responses from Gemini Live API
3. Stream those audio responses back to the caller

## Next Steps

- Implement full bidirectional streaming with Gemini Live API
- Add support for context retrieval from your knowledge base
- Implement call recording and transcription
- Add support for multiple concurrent calls
