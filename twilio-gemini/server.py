from flask import Flask, request, render_template, jsonify
import os
import base64
import asyncio
from twilio.jwt.access_token import AccessToken
from twilio.jwt.access_token.grants import VoiceGrant
from twilio.twiml.voice_response import VoiceResponse, Dial
from dotenv import load_dotenv
import google.generativeai as genai
import threading
import queue
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)

# Twilio credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID', '')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN', '')
TWILIO_API_KEY = os.environ.get('TWILIO_API_KEY', '')
TWILIO_API_SECRET = os.environ.get('TWILIO_API_SECRET', '')
TWILIO_TWIML_APP_SID = os.environ.get('TWILIO_TWIML_APP_SID', '')
TWILIO_CALLER_ID = os.environ.get('TWILIO_CALLER_ID', '')

# Gemini API credentials
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY', '')

# Configure Gemini API
genai.configure(api_key=GEMINI_API_KEY)

# Global variables for audio streaming
audio_queue = queue.Queue()
response_queue = queue.Queue()
active_calls = {}

# Initialize Gemini model for text-based fallback
def create_gemini_model():
    """Create a Gemini model with the appropriate configuration."""
    try:
        # Use gemini-1.5-flash for text-based fallback
        model = genai.GenerativeModel(
            model_name="gemini-1.5-flash",
            generation_config={
                "temperature": 0.7,
                "top_p": 0.95,
                "top_k": 64
            }
        )
        logger.info("Created Gemini model for text-based fallback")
        return model
    except Exception as e:
        logger.error(f"Error creating Gemini model: {e}")
        raise

# Create the model
model = create_gemini_model()

# Constants for Gemini Live API
GEMINI_LIVE_MODEL = "gemini-2.0-flash-live-001"  # Correct model for live audio
GEMINI_VOICE = "Aoede"  # Default voice to use

# For a full implementation with Gemini Live API, we would need to:
# 1. Create a WebSocket connection to Gemini Live API using:
#    ws_url = f"wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key={api_key}&alt=json"
# 2. Send initial configuration with:
#    {
#      "setup": {
#        "model": f"models/{GEMINI_LIVE_MODEL}",
#        "generationConfig": {
#          "responseModalities": ["AUDIO"],
#          "speechConfig": {
#            "voiceConfig": {
#              "prebuiltVoiceConfig": {
#                "voiceName": GEMINI_VOICE
#              }
#            },
#            "languageCode": "en-US"
#          }
#        },
#        "systemInstruction": {
#          "parts": [
#            {
#              "text": system_instruction
#            }
#          ]
#        }
#      }
#    }
# 3. Send audio data to Gemini Live API
# 4. Receive audio responses from Gemini Live API
# 5. Send those audio responses back to the browser client

# Create system prompt
def create_system_prompt():
    """Create the system prompt for Gemini Live API."""
    system_prompt = """
    You are Staycee, a helpful AI concierge assistant.
    You are speaking with a guest over the phone.
    Your goal is to assist the guest with any questions or needs they have regarding their stay.
    Be conversational, friendly, and helpful.

    Keep your responses concise and conversational, as they will be read aloud over the phone.
    Limit your responses to 3-4 sentences maximum.

    CRITICAL INFRASTRUCTURE PROTECTION:
    If a guest asks about the location of critical infrastructure elements such as water shutoff valves, electrical panels, fuse boxes, circuit breakers, gas shutoff valves, HVAC system controls, air handler units, ventilation system access, sump pumps, water heaters, or other mechanical systems, you MUST first ask the guest to explain the specific reason they need this information. Only provide access details if there is a genuine emergency situation such as fire, smoke, electrical hazards, water leaks, flooding, pipe bursts, gas leaks, HVAC system failures causing dangerous temperatures, or any situation where immediate access would prevent property damage or ensure guest safety. For non-emergency requests, politely explain that this information is restricted for safety and security reasons, and suggest they contact the host directly.

    You have access to the following tools:
    1. google_search: Use this tool to search for information about local attractions, restaurants, services, or any other information that would be helpful to the guest.
    """

    return system_prompt

# Set system instructions
system_instruction = create_system_prompt()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/token', methods=['GET'])
def token():
    # Create access token with credentials
    token = AccessToken(
        TWILIO_ACCOUNT_SID,
        TWILIO_API_KEY,
        TWILIO_API_SECRET,
        identity="user"
    )

    # Create a Voice grant and add to token
    voice_grant = VoiceGrant(
        outgoing_application_sid=TWILIO_TWIML_APP_SID,
        incoming_allow=True
    )
    token.add_grant(voice_grant)

    # Return token as JSON
    jwt_token = token.to_jwt()
    # In Python 3, jwt_token is already a string, no need to decode
    if isinstance(jwt_token, bytes):
        jwt_token = jwt_token.decode()
    return jsonify(token=jwt_token)

@app.route('/voice', methods=['POST'])
def voice():
    """Respond to incoming calls with a simple text message."""
    resp = VoiceResponse()

    # If the request is an incoming call to the Twilio number
    if 'From' in request.form:
        caller_id = request.form.get('From', 'Unknown')
        call_sid = request.form.get('CallSid', '')

        logger.info(f"Incoming call from {caller_id} with SID {call_sid}")

        # Start a new Gemini session for this call
        start_gemini_session(call_sid, caller_id)

        dial = Dial()
        dial.client('user')
        resp.append(dial)
    else:
        resp.say("Thank you for calling. This is a test.")

    return str(resp)

@app.route('/audio', methods=['POST'])
def receive_audio():
    """Receive audio from the WebRTC client and send to Gemini."""
    if not request.data:
        logger.warning("No audio data received")
        return jsonify(success=False, error="No audio data received"), 400

    call_sid = request.headers.get('X-Call-SID', '')
    if not call_sid or call_sid not in active_calls:
        logger.warning(f"Invalid or missing call SID: {call_sid}")
        return jsonify(success=False, error="Invalid or missing call SID"), 400

    # Log audio data
    audio_size = len(request.data)
    logger.info(f"Received {audio_size} bytes of audio data from call {call_sid}")

    # Add audio data to the queue for processing
    audio_queue.put((call_sid, request.data))

    return jsonify(success=True)

@app.route('/response', methods=['GET'])
def get_response():
    """Get the latest response from Gemini for the WebRTC client."""
    call_sid = request.args.get('call_sid', '')
    if not call_sid or call_sid not in active_calls:
        logger.warning(f"Invalid or missing call SID in get_response: {call_sid}")
        return jsonify(success=False, error="Invalid or missing call SID"), 400

    # Check if there's a response available
    try:
        response = response_queue.get_nowait()
        logger.info(f"Returning response to client: {response[:50]}...")
        return jsonify(success=True, response=response)
    except queue.Empty:
        # No response available yet
        logger.debug(f"No response available yet for call {call_sid}")
        return jsonify(success=True, response=None)

def start_gemini_session(call_sid, caller_id):
    """Start a new Gemini session for a call."""
    if call_sid in active_calls:
        logger.warning(f"Session already exists for call {call_sid}")
        return

    try:
        # Create a new chat session with system instructions
        chat = model.start_chat(
            history=[
                {
                    "role": "user",
                    "parts": ["Hello, I'm calling for assistance."]
                },
                {
                    "role": "model",
                    "parts": ["Hello! I'm your AI concierge assistant. How can I help you today?"]
                }
            ]
        )

        # Store session information
        active_calls[call_sid] = {
            'caller_id': caller_id,
            'start_time': time.time(),
            'gemini_chat': chat,
            'system_instruction': system_instruction,
            'audio_buffer': bytearray(),
            'is_processing': False,
            'last_response_time': time.time()
        }

        # Start a background thread to process audio for this call
        thread = threading.Thread(target=process_audio_for_call, args=(call_sid,))
        thread.daemon = True
        thread.start()

        # Send a welcome message
        response_queue.put("Hello! I'm your AI concierge assistant. How can I help you today?")

        # For testing purposes, generate an immediate response after 3 seconds
        def generate_test_response():
            time.sleep(3)
            logger.info(f"Generating test response for call {call_sid}")
            try:
                # Check if the call is still active
                if call_sid not in active_calls:
                    logger.warning(f"Call {call_sid} no longer active, skipping test response")
                    return

                chat = active_calls[call_sid]['gemini_chat']
                test_message = "Can you tell me about nearby restaurants?"
                logger.info(f"Sending test message to Gemini: '{test_message}'")

                # Generate a response using the text-based model
                response = chat.send_message(test_message)
                response_text = response.text
                logger.info(f"Generated response for call {call_sid}: {response_text}")

                # Add the response to the queue
                response_queue.put(response_text)

                # Log that we're sending the response to the browser
                logger.info(f"Sent response to browser for call {call_sid}")
            except Exception as e:
                logger.error(f"Error generating test response: {e}")
                # Add a fallback response
                response_queue.put("I'm sorry, I'm having trouble generating a response. Please try again later.")

        # Start a thread to generate a test response
        test_thread = threading.Thread(target=generate_test_response)
        test_thread.daemon = True
        test_thread.start()

        logger.info(f"Started Gemini session for call {call_sid}")
    except Exception as e:
        logger.error(f"Error starting Gemini session for call {call_sid}: {e}")
        raise

def process_audio_for_call(call_sid):
    """Process audio for a specific call."""
    logger.info(f"Starting audio processing for call {call_sid}")

    # Audio processing constants
    AUDIO_CHUNK_SIZE = 4096  # Process audio in chunks
    SILENCE_THRESHOLD = 0.01  # Threshold for detecting silence
    SILENCE_DURATION = 1.0    # Duration of silence to trigger processing (seconds)
    SAMPLE_RATE = 24000       # Gemini expects 24kHz audio

    # Initialize variables
    last_audio_time = time.time()

    while call_sid in active_calls:
        try:
            # Wait for audio data
            sid, audio_data = audio_queue.get(timeout=1)
            if sid != call_sid:
                continue

            # Update last audio time
            last_audio_time = time.time()

            # Add audio to buffer
            call_data = active_calls[call_sid]
            call_data['audio_buffer'].extend(audio_data)

            # Check if we have enough audio to process
            if len(call_data['audio_buffer']) >= AUDIO_CHUNK_SIZE and not call_data['is_processing']:
                # Process the audio
                process_audio_chunk(call_sid)

            # Log audio received
            logger.info(f"Received audio data from call {call_sid}, buffer size: {len(call_data['audio_buffer'])} bytes")

        except queue.Empty:
            # No audio data available, check if we should process the buffer
            if call_sid in active_calls:
                call_data = active_calls[call_sid]

                # If we have audio in the buffer and there's been silence, process it
                if (len(call_data['audio_buffer']) > 0 and
                    not call_data['is_processing'] and
                    time.time() - last_audio_time > SILENCE_DURATION):
                    process_audio_chunk(call_sid)

            continue
        except Exception as e:
            logger.error(f"Error processing audio for call {call_sid}: {e}")

    logger.info(f"Stopped audio processing for call {call_sid}")

def process_audio_chunk(call_sid):
    """Process an audio chunk with Gemini Live API."""
    if call_sid not in active_calls:
        logger.warning(f"Call {call_sid} not found")
        return

    call_data = active_calls[call_sid]

    # Mark as processing
    call_data['is_processing'] = True

    try:
        # Get the audio buffer
        audio_buffer = call_data['audio_buffer']

        # Clear the buffer
        call_data['audio_buffer'] = bytearray()

        # Convert audio to the right format if needed
        # Note: Gemini expects 24kHz 16-bit PCM audio
        # For now, we'll assume the audio is already in the correct format

        # Log the audio buffer size
        logger.info(f"Processing audio chunk of size {len(audio_buffer)} bytes for call {call_sid}")

        # In a real implementation, we would use the Gemini Live API to process the audio
        # For now, we'll use the text-based API as a fallback

        # Generate a response using the chat model
        chat = call_data['gemini_chat']

        # For testing purposes, we'll use a fixed message
        # In a real implementation, we would use Gemini's speech-to-text capabilities
        test_message = "Can you tell me about nearby restaurants?"
        logger.info(f"Sending test message to Gemini: '{test_message}'")

        # Send the message to Gemini
        response = chat.send_message(test_message)

        # Get the response text
        response_text = response.text

        # Add the response to the queue
        response_queue.put(response_text)

        # Update last response time
        call_data['last_response_time'] = time.time()

        logger.info(f"Generated response for call {call_sid}: {response_text}")

        # TODO: In a full implementation, we would:
        # 1. Send the audio to Gemini Live API using a WebSocket connection
        # 2. Receive audio responses from Gemini
        # 3. Send those audio responses back to the browser client
        # 4. The browser would play the audio responses to the caller

    except Exception as e:
        logger.error(f"Error processing audio chunk for call {call_sid}: {e}")
        # Add a fallback response
        response_queue.put("I'm sorry, I'm having trouble understanding. Could you please try again?")
    finally:
        # Mark as not processing
        call_data['is_processing'] = False

@app.route('/end_call', methods=['POST'])
def end_call():
    """End a call and clean up resources."""
    call_sid = request.json.get('call_sid', '')
    if not call_sid:
        return jsonify(success=False, error="Missing call SID"), 400

    if call_sid in active_calls:
        try:
            # Log call duration
            call_data = active_calls[call_sid]
            duration = time.time() - call_data['start_time']
            logger.info(f"Call {call_sid} ended after {duration:.2f} seconds")

            # Clean up resources
            del active_calls[call_sid]

            return jsonify(success=True)
        except Exception as e:
            logger.error(f"Error ending call {call_sid}: {e}")
            return jsonify(success=False, error=str(e)), 500
    else:
        return jsonify(success=False, error="Call not found"), 404

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
