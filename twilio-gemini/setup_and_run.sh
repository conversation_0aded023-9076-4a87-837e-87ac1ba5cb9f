#!/bin/bash
# Script to set up and run the Twilio + Gemini integration

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Setting up Twilio + Gemini integration..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install it first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    print_error "pip3 is not installed. Please install it first."
    exit 1
fi

# Check if virtualenv is installed
if ! command -v virtualenv &> /dev/null; then
    print_message "Installing virtualenv..."
    pip3 install virtualenv
fi

# Create and activate virtual environment
print_message "Creating virtual environment..."
virtualenv venv
source venv/bin/activate

# Install dependencies
print_message "Installing dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from template..."
    cp .env.example .env
    print_message "Please edit the .env file with your Twilio and Gemini credentials."
    print_message "You can open it with: nano .env"
    exit 1
fi

# Create templates directory if it doesn't exist
if [ ! -d "templates" ]; then
    print_message "Creating templates directory..."
    mkdir -p templates
fi

# Start the server
print_message "Starting the server..."
print_message "Open a new terminal and run: ngrok http 5002"
print_message "Then update your TwiML App with the ngrok URL + /voice"
print_message ""
print_message "Press Ctrl+C to stop the server."
python server.py
