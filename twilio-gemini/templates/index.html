<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON> + <PERSON> Live Voice Assistant</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
        }
        #call-controls {
            margin-top: 20px;
        }
        .log {
            background-color: #f5f5f5;
            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
        }
        .conversation {
            background-color: #e9f7fe;
            border: 1px solid #c5e8fe;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
        }
        .user-message {
            background-color: #dcf8c6;
            border-radius: 10px;
            padding: 8px 12px;
            margin: 5px 0;
            max-width: 80%;
            align-self: flex-end;
            margin-left: auto;
        }
        .ai-message {
            background-color: #f1f0f0;
            border-radius: 10px;
            padding: 8px 12px;
            margin: 5px 0;
            max-width: 80%;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-connected {
            background-color: #28a745;
        }
        .status-disconnected {
            background-color: #dc3545;
        }
        .status-connecting {
            background-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Twilio + Gemini Live Voice Assistant</h1>
        <p>This client will receive incoming calls and connect them to Gemini Live API.</p>

        <div id="call-status" class="alert alert-info">
            <span id="status-indicator" class="status-indicator status-disconnected"></span>
            Ready to receive calls
        </div>

        <div id="call-controls">
            <button id="button-answer" class="btn btn-success" disabled>Answer Call</button>
            <button id="button-hangup" class="btn btn-danger" disabled>Hang Up</button>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <h4>System Log</h4>
                <div class="log">
                    <div id="log"></div>
                </div>
            </div>
            <div class="col-md-6">
                <h4>Conversation</h4>
                <div class="conversation">
                    <div id="conversation"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://sdk.twilio.com/js/client/releases/1.14.0/twilio.js"></script>
    <script>
        $(function() {
            let device;
            let call;
            let currentCallSid = null;
            let audioContext = null;
            let audioProcessor = null;
            let audioInput = null;
            let isProcessingAudio = false;

            // Log messages to the console
            function log(message) {
                $('#log').append('<div>' + message + '</div>');
                const logDiv = document.getElementById('log');
                logDiv.scrollTop = logDiv.scrollHeight;
            }

            // Add a message to the conversation
            function addMessage(message, isUser = false) {
                const messageClass = isUser ? 'user-message' : 'ai-message';
                $('#conversation').append(`<div class="${messageClass}">${message}</div>`);
                const conversationDiv = document.getElementById('conversation');
                conversationDiv.scrollTop = conversationDiv.scrollHeight;
            }

            // Update the status indicator
            function updateStatus(status) {
                const indicator = $('#status-indicator');
                indicator.removeClass('status-connected status-disconnected status-connecting');

                switch(status) {
                    case 'connected':
                        indicator.addClass('status-connected');
                        $('#call-status').text('Call in progress');
                        break;
                    case 'disconnected':
                        indicator.addClass('status-disconnected');
                        $('#call-status').text('Ready to receive calls');
                        break;
                    case 'connecting':
                        indicator.addClass('status-connecting');
                        $('#call-status').text('Call connecting...');
                        break;
                    default:
                        indicator.addClass('status-disconnected');
                        $('#call-status').text(status);
                }
            }

            // Initialize audio processing
            function initAudioProcessing() {
                try {
                    // Create audio context
                    audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    log('Audio context created');

                    // We'll set up the actual processing when a call is connected
                } catch (e) {
                    log('Error initializing audio context: ' + e.message);
                }
            }

            // Start audio processing for a call
            function startAudioProcessing(stream) {
                if (!audioContext) {
                    initAudioProcessing();
                }

                try {
                    // Create audio source from the call's stream
                    audioInput = audioContext.createMediaStreamSource(stream);

                    // Create script processor node
                    const bufferSize = 4096;
                    audioProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);

                    // Connect the audio graph
                    audioInput.connect(audioProcessor);
                    audioProcessor.connect(audioContext.destination);

                    // Set up audio processing
                    audioProcessor.onaudioprocess = function(e) {
                        if (!isProcessingAudio || !currentCallSid) return;

                        // Get audio data
                        const inputData = e.inputBuffer.getChannelData(0);

                        // Log audio data
                        const rms = calculateRMS(inputData);
                        if (rms > 0.01) {  // Only log if there's actual audio
                            log(`Processing audio: RMS=${rms.toFixed(4)}, length=${inputData.length}`);
                        }

                        // Convert to 16-bit PCM
                        const pcmData = convertFloatToPCM(inputData);

                        // Send to server
                        sendAudioToServer(pcmData);
                    };

                    // Function to calculate RMS (Root Mean Square) of audio data
                    function calculateRMS(buffer) {
                        let sum = 0;
                        for (let i = 0; i < buffer.length; i++) {
                            sum += buffer[i] * buffer[i];
                        }
                        return Math.sqrt(sum / buffer.length);
                    }

                    isProcessingAudio = true;
                    log('Audio processing started');

                    // Start polling for responses
                    pollForResponses();
                } catch (e) {
                    log('Error starting audio processing: ' + e.message);
                }
            }

            // Convert Float32Array to 16-bit PCM
            function convertFloatToPCM(float32Array) {
                const pcmData = new Int16Array(float32Array.length);
                for (let i = 0; i < float32Array.length; i++) {
                    const s = Math.max(-1, Math.min(1, float32Array[i]));
                    pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
                }
                return pcmData.buffer;
            }

            // Send audio data to the server
            function sendAudioToServer(audioData) {
                if (!currentCallSid) return;

                log('Sending audio data to server: ' + audioData.byteLength + ' bytes');

                fetch('/audio', {
                    method: 'POST',
                    body: audioData,
                    headers: {
                        'Content-Type': 'application/octet-stream',
                        'X-Call-SID': currentCallSid
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // Success, no need to log every successful audio packet
                    } else {
                        log('Error sending audio: ' + response.status);
                    }
                })
                .catch(error => {
                    log('Error sending audio: ' + error.message);
                });
            }

            // Poll for responses from Gemini
            function pollForResponses() {
                if (!currentCallSid) return;

                log('Starting to poll for responses...');

                const pollInterval = setInterval(() => {
                    if (!currentCallSid) {
                        clearInterval(pollInterval);
                        log('Stopped polling for responses');
                        return;
                    }

                    log('Polling for responses...');

                    fetch(`/response?call_sid=${currentCallSid}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.response) {
                                log('Received response: ' + data.response);

                                // Add the response to the conversation
                                addMessage(data.response);

                                // Use the Web Speech API to speak the response
                                // This is a temporary solution until we implement proper audio streaming
                                speakResponse(data.response);
                            }
                        })
                        .catch(error => {
                            log('Error polling for responses: ' + error.message);
                        });
                }, 1000);
            }

            // Use the Web Speech API to speak the response
            function speakResponse(text) {
                log('Attempting to speak response: ' + text);

                // Check if the Web Speech API is supported
                if ('speechSynthesis' in window) {
                    try {
                        // Create a new speech synthesis utterance
                        const utterance = new SpeechSynthesisUtterance(text);

                        // Set properties
                        utterance.lang = 'en-US';
                        utterance.rate = 1.0;
                        utterance.pitch = 1.0;

                        // Add event handlers for debugging
                        utterance.onstart = () => log('Speech started');
                        utterance.onend = () => log('Speech ended');
                        utterance.onerror = (event) => log('Speech error: ' + event.error);

                        // Speak the utterance
                        window.speechSynthesis.speak(utterance);

                        log('Speaking response using Web Speech API');

                        // Also play a beep sound to verify audio is working
                        playBeep();
                    } catch (error) {
                        log('Error using speech synthesis: ' + error.message);
                    }
                } else {
                    log('Web Speech API is not supported in this browser');
                    // Play a beep sound as fallback
                    playBeep();
                }
            }

            // Play a beep sound to verify audio is working
            function playBeep() {
                try {
                    // Create an audio context
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                    // Create an oscillator
                    const oscillator = audioContext.createOscillator();
                    oscillator.type = 'sine';
                    oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // A5

                    // Create a gain node
                    const gainNode = audioContext.createGain();
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);

                    // Connect the nodes
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);

                    // Start and stop the oscillator
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.2);

                    log('Played beep sound');
                } catch (error) {
                    log('Error playing beep: ' + error.message);
                }
            }

            // Stop audio processing
            function stopAudioProcessing() {
                if (audioProcessor) {
                    audioProcessor.disconnect();
                    audioProcessor = null;
                }

                if (audioInput) {
                    audioInput.disconnect();
                    audioInput = null;
                }

                isProcessingAudio = false;
                log('Audio processing stopped');

                // Notify server that call has ended
                if (currentCallSid) {
                    log('Notifying server that call has ended: ' + currentCallSid);

                    fetch('/end_call', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ call_sid: currentCallSid })
                    })
                    .then(response => {
                        if (response.ok) {
                            log('Successfully notified server that call has ended');
                        } else {
                            log('Error notifying server that call has ended: ' + response.status);
                        }
                    })
                    .catch(error => {
                        log('Error ending call on server: ' + error.message);
                    });

                    currentCallSid = null;
                }
            }

            // Fetch token from server
            $.getJSON('/token', function(data) {
                log('Got token');
                // Set up the Twilio Device with the token
                device = new Twilio.Device(data.token, {
                    codecPreferences: ['opus', 'pcmu'],
                    fakeLocalDTMF: true,
                    enableRingingState: true
                });

                device.on('ready', function() {
                    log('Twilio.Device Ready!');
                    updateStatus('disconnected');
                });

                device.on('error', function(error) {
                    log('Twilio.Device Error: ' + error.message);
                    updateStatus('Error: ' + error.message);
                });

                device.on('incoming', function(incoming) {
                    log('Incoming call from ' + incoming.parameters.From);
                    call = incoming;
                    currentCallSid = incoming.parameters.CallSid;
                    updateStatus('connecting');
                    $('#button-answer').prop('disabled', false);

                    // Auto answer after 1 second for testing
                    setTimeout(function() {
                        answerCall();
                    }, 1000);
                });

                device.on('connect', function(conn) {
                    log('Call connected');
                    updateStatus('connected');
                    $('#button-hangup').prop('disabled', false);
                    $('#button-answer').prop('disabled', true);

                    // Start audio processing
                    startAudioProcessing(conn.stream());

                    // Add initial message
                    addMessage("Hello! I'm your AI concierge assistant. How can I help you today?");
                });

                device.on('disconnect', function(conn) {
                    log('Call ended');
                    updateStatus('disconnected');
                    $('#button-hangup').prop('disabled', true);
                    $('#button-answer').prop('disabled', true);

                    // Stop audio processing
                    stopAudioProcessing();

                    // Clear conversation
                    $('#conversation').empty();
                });
            });

            // Answer incoming call
            function answerCall() {
                if (call) {
                    log('Answering call');
                    call.accept();
                }
            }

            // Hang up call
            function hangupCall() {
                if (call) {
                    log('Hanging up call');
                    call.disconnect();
                }
            }

            // Set up event handlers for buttons
            $('#button-answer').click(answerCall);
            $('#button-hangup').click(hangupCall);
        });
    </script>
</body>
</html>
