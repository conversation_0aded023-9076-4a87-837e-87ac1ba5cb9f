# Twilio WebRTC Client for Phone Calls

This is a simple WebRTC client that can receive phone calls from a Twilio number. It uses <PERSON><PERSON><PERSON>'s Voice SDK to handle the WebRTC connection.

## How It Works

1. When someone calls your Twilio phone number, <PERSON><PERSON><PERSON> sends a webhook to your server
2. Your server responds with Twi<PERSON> that tells <PERSON><PERSON><PERSON> to connect the call to your WebRTC client
3. The WebRTC client in your browser receives the call and establishes a connection
4. Audio is streamed bidirectionally between the caller and your browser

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Create a Twilio Account and Get Credentials

1. Sign up for a Twilio account at https://www.twilio.com/try-twilio
2. Get your Account SID and Auth Token from the Twilio Console
3. Create an API Key at https://www.twilio.com/console/project/api-keys
4. Buy a phone number or use an existing one

### 3. Create a TwiML App

1. Go to https://www.twilio.com/console/voice/twiml/apps
2. Create a new TwiML App
3. Set the Voice Request URL to `https://your-ngrok-url/voice` (we'll get this URL in step 5)
4. Save the TwiML App SID

### 4. Configure Environment Variables

1. Copy `.env.example` to `.env`
2. Fill in your Twilio credentials in the `.env` file

### 5. Start the Server and Expose with ngrok

1. Start the Flask server:
   ```bash
   python server.py
   ```

2. In a new terminal, start ngrok to expose your server:
   ```bash
   ngrok http 5000
   ```

3. Copy the ngrok URL (e.g., `https://abc123.ngrok.io`)

### 6. Update Your TwiML App and Phone Number

1. Go back to your TwiML App and update the Voice Request URL with your ngrok URL + `/voice`
2. Go to https://www.twilio.com/console/phone-numbers/incoming
3. Select your phone number
4. Under "Voice & Fax", set the "A Call Comes In" webhook to your TwiML App
5. Save the changes

### 7. Test the Setup

1. Open your browser and go to `http://localhost:5000`
2. Call your Twilio phone number from another phone
3. The call should be connected to your browser

## Troubleshooting

- Make sure your browser has permission to access your microphone
- Check the browser console for any errors
- Verify that your ngrok tunnel is running and the URL is correct
- Check the Twilio Console for any error messages in the logs

## Next Steps

Once you have this working, you can integrate with Gemini Live API by:

1. Capturing the audio from the WebRTC call
2. Sending it to Gemini Live API
3. Playing back the responses from Gemini Live API to the caller
