#!/bin/bash
# Script to configure Twilio for WebRTC client

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    echo -e "${GRE<PERSON>}[INFO] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

print_message "Configuring Twilio for WebRTC client..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please run setup_and_run.sh first."
    exit 1
fi

# Source the .env file
source .env

# Check if Twilio CLI is installed
if ! command -v twilio &> /dev/null; then
    print_warning "Twilio CLI is not installed. It's recommended for easier configuration."
    print_message "You can install it using: npm install -g twilio-cli"
    print_message "Then authenticate with: twilio login"
    print_message ""
    print_message "Continuing with manual configuration instructions..."
else
    # Check if authenticated
    if ! twilio profiles:list | grep -q "active"; then
        print_warning "Twilio CLI is not authenticated. Please run: twilio login"
        print_message "Continuing with manual configuration instructions..."
    else
        print_message "Twilio CLI is authenticated. You can use it to configure Twilio."
        print_message "To create a TwiML App: twilio api:core:applications:create --friendly-name=\"WebRTC Client\" --voice-url=https://your-ngrok-url/voice"
        print_message "To configure a phone number: twilio phone-numbers:update [phone-number] --voice-url=https://your-ngrok-url/voice"
    fi
fi

print_message ""
print_message "Manual Configuration Instructions:"
print_message ""
print_message "1. Create a TwiML App:"
print_message "   - Go to https://www.twilio.com/console/voice/twiml/apps"
print_message "   - Create a new TwiML App"
print_message "   - Set the Voice Request URL to your ngrok URL + /voice"
print_message "   - Save the TwiML App SID in your .env file"
print_message ""
print_message "2. Configure Your Phone Number:"
print_message "   - Go to https://www.twilio.com/console/phone-numbers/incoming"
print_message "   - Select your phone number"
print_message "   - Under 'Voice & Fax', set the 'A Call Comes In' webhook to your TwiML App"
print_message "   - Save the changes"
print_message ""
print_message "3. Test the Setup:"
print_message "   - Make sure your server is running (./setup_and_run.sh)"
print_message "   - Open your browser and go to http://localhost:5001"
print_message "   - Call your Twilio phone number from another phone"
print_message "   - The call should be connected to your browser"
