from flask import Flask, request, render_template, jsonify
import os
from twilio.jwt.access_token import AccessToken
from twilio.jwt.access_token.grants import VoiceGrant
from twilio.twiml.voice_response import VoiceResponse, Dial
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)

# Twilio credentials
TWILIO_ACCOUNT_SID = os.environ.get('TWILIO_ACCOUNT_SID', '')
TWILIO_AUTH_TOKEN = os.environ.get('TWILIO_AUTH_TOKEN', '')
TWILIO_API_KEY = os.environ.get('TWILIO_API_KEY', '')
TWILIO_API_SECRET = os.environ.get('TWILIO_API_SECRET', '')
TWILIO_TWIML_APP_SID = os.environ.get('TWILIO_TWIML_APP_SID', '')
TWILIO_CALLER_ID = os.environ.get('TWILIO_CALLER_ID', '')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/token', methods=['GET'])
def token():
    # Create access token with credentials
    token = AccessToken(
        TWILIO_ACCOUNT_SID,
        TWILIO_API_KEY,
        TWILIO_API_SECRET,
        identity="user"
    )

    # Create a Voice grant and add to token
    voice_grant = VoiceGrant(
        outgoing_application_sid=TWILIO_TWIML_APP_SID,
        incoming_allow=True
    )
    token.add_grant(voice_grant)

    # Return token as JSON
    jwt_token = token.to_jwt()
    # In Python 3, jwt_token is already a string, no need to decode
    if isinstance(jwt_token, bytes):
        jwt_token = jwt_token.decode()
    return jsonify(token=jwt_token)

@app.route('/voice', methods=['POST'])
def voice():
    """Respond to incoming calls with a simple text message."""
    resp = VoiceResponse()

    # If the request is an incoming call to the Twilio number
    if 'From' in request.form:
        dial = Dial()
        dial.client('user')
        resp.append(dial)
    else:
        resp.say("Thank you for calling. This is a test.")

    return str(resp)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
