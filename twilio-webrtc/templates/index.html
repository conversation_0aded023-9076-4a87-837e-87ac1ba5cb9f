<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON> Voice WebRTC</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
        }
        #call-controls {
            margin-top: 20px;
        }
        .log {
            background-color: #f5f5f5;
            border: 1px solid #e3e3e3;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Twilio Voice WebRTC Client</h1>
        <p>This client will receive incoming calls from your Twilio number.</p>
        
        <div id="call-status" class="alert alert-info">
            Ready to receive calls
        </div>
        
        <div id="call-controls">
            <button id="button-answer" class="btn btn-success" disabled>Answer Call</button>
            <button id="button-hangup" class="btn btn-danger" disabled>Hang Up</button>
        </div>
        
        <div class="log">
            <div id="log"></div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://sdk.twilio.com/js/client/releases/1.14.0/twilio.js"></script>
    <script>
        $(function() {
            let device;
            let call;
            
            // Log messages to the console
            function log(message) {
                $('#log').append('<div>' + message + '</div>');
                const logDiv = document.getElementById('log');
                logDiv.scrollTop = logDiv.scrollHeight;
            }
            
            // Fetch token from server
            $.getJSON('/token', function(data) {
                log('Got token');
                // Set up the Twilio Device with the token
                device = new Twilio.Device(data.token, {
                    codecPreferences: ['opus', 'pcmu'],
                    fakeLocalDTMF: true,
                    enableRingingState: true
                });
                
                device.on('ready', function() {
                    log('Twilio.Device Ready!');
                    $('#call-status').text('Ready to receive calls');
                });
                
                device.on('error', function(error) {
                    log('Twilio.Device Error: ' + error.message);
                    $('#call-status').text('Error: ' + error.message);
                });
                
                device.on('incoming', function(incoming) {
                    log('Incoming call from ' + incoming.parameters.From);
                    call = incoming;
                    $('#call-status').text('Incoming call from ' + incoming.parameters.From);
                    $('#button-answer').prop('disabled', false);
                    
                    // Auto answer after 1 second for testing
                    setTimeout(function() {
                        answerCall();
                    }, 1000);
                });
                
                device.on('connect', function(conn) {
                    log('Call connected');
                    $('#call-status').text('Call in progress');
                    $('#button-hangup').prop('disabled', false);
                    $('#button-answer').prop('disabled', true);
                });
                
                device.on('disconnect', function(conn) {
                    log('Call ended');
                    $('#call-status').text('Call ended');
                    $('#button-hangup').prop('disabled', true);
                    $('#button-answer').prop('disabled', true);
                });
            });
            
            // Answer incoming call
            function answerCall() {
                if (call) {
                    log('Answering call');
                    call.accept();
                }
            }
            
            // Hang up call
            function hangupCall() {
                if (call) {
                    log('Hanging up call');
                    call.disconnect();
                }
            }
            
            // Set up event handlers for buttons
            $('#button-answer').click(answerCall);
            $('#button-hangup').click(hangupCall);
        });
    </script>
</body>
</html>
