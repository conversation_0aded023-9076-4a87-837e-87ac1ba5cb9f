#!/usr/bin/env python3
"""
<PERSON>ript to migrate DynamoDB conversations from old UID to new synchronized UID.
This fixes conversation history after UID synchronization.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import boto3
from botocore.exceptions import ClientError
from concierge.config import AWS_REGION

def migrate_conversations():
    """Migrate conversations from old UID to new UID."""
    
    old_uid = "BqVhdHwSbuhLfEsOJIRf4XLYl9T2"  # Old Firestore UID
    new_uid = "8LnLzt6W9JM3sGFsPWvbGoVTrS32"  # New Firebase Auth UID
    
    # Use the same table name configuration as the DynamoDB client
    conversations_table_name = os.environ.get('CONVERSATIONS_TABLE_NAME', 'Conversations')
    
    print("=== DynamoDB Conversation Migration ===\n")
    
    # Initialize DynamoDB client
    try:
        region = AWS_REGION
        dynamodb = boto3.resource('dynamodb', region_name=region)
        conversations_table = dynamodb.Table(conversations_table_name)
        print(f"✅ Connected to DynamoDB in region: {region}")
        print(f"✅ Using table: {conversations_table_name}")
    except Exception as e:
        print(f"❌ ERROR: Failed to connect to DynamoDB: {e}")
        return False
    
    # Step 1: Scan for GSI2 entries with old UID in SK
    print(f"1. Scanning for GSI2 entries with old UID: {old_uid}")
    
    try:
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('SK').contains(f'GSI2#USER#{old_uid}')
        )
        
        gsi_entries = response.get('Items', [])
        print(f"✅ Found {len(gsi_entries)} GSI2 entries to migrate")
        
        if len(gsi_entries) == 0:
            print("ℹ️  No GSI2 entries found to migrate")
            return True
            
    except Exception as e:
        print(f"❌ ERROR: Failed to scan GSI2 entries: {e}")
        return False
    
    # Step 2: Display GSI2 entries for review
    print(f"\n2. GSI2 entries to migrate:")
    for i, entry in enumerate(gsi_entries, 1):
        pk = entry.get('PK', 'Unknown')
        sk = entry.get('SK', 'Unknown')
        print(f"   {i}. PK: {pk}")
        print(f"      SK: {sk}")
    
    # Step 3: Confirm migration
    print(f"\n3. Ready to migrate {len(gsi_entries)} GSI2 entries")
    print(f"   From UID: {old_uid}")
    print(f"   To UID:   {new_uid}")
    
    confirm = input("\nProceed with migration? (yes/no): ")
    if confirm.lower() != 'yes':
        print("❌ Migration cancelled")
        return False
    
    # Step 4: Migrate GSI2 entries
    print(f"\n4. Migrating GSI2 entries...")
    
    migrated_count = 0
    failed_count = 0
    
    for entry in gsi_entries:
        pk = entry.get('PK')
        old_sk = entry.get('SK')
        
        # Create new SK by replacing the old UID with new UID
        new_sk = old_sk.replace(f'GSI2#USER#{old_uid}', f'GSI2#USER#{new_uid}')
        
        try:
            # Delete the old entry
            conversations_table.delete_item(
                Key={'PK': pk, 'SK': old_sk}
            )
            
            # Create new entry with updated SK
            new_entry = entry.copy()
            new_entry['SK'] = new_sk
            conversations_table.put_item(Item=new_entry)
            
            migrated_count += 1
            print(f"   ✅ Migrated GSI2 entry {pk[:20]}...")
            print(f"      Old SK: {old_sk}")
            print(f"      New SK: {new_sk}")
            
        except Exception as e:
            failed_count += 1
            print(f"   ❌ Failed to migrate GSI2 entry {pk[:20]}...: {e}")
    
    # Step 5: Summary
    print(f"\n=== Migration Complete ===")
    print(f"✅ Successfully migrated: {migrated_count} GSI2 entries")
    if failed_count > 0:
        print(f"❌ Failed to migrate: {failed_count} GSI2 entries")
    print(f"🔄 GSI2 entries now associated with UID: {new_uid}")
    
    # Step 6: Verify migration
    print(f"\n5. Verifying migration...")
    
    try:
        # Check for GSI2 entries with new UID
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('SK').contains(f'GSI2#USER#{new_uid}')
        )
        
        new_entries = response.get('Items', [])
        print(f"✅ Found {len(new_entries)} GSI2 entries with new UID")
        
        # Check for remaining GSI2 entries with old UID
        response = conversations_table.scan(
            FilterExpression=boto3.dynamodb.conditions.Attr('SK').contains(f'GSI2#USER#{old_uid}')
        )
        
        remaining_entries = response.get('Items', [])
        if len(remaining_entries) > 0:
            print(f"⚠️  WARNING: {len(remaining_entries)} GSI2 entries still have old UID")
        else:
            print(f"✅ No GSI2 entries remaining with old UID")
            
    except Exception as e:
        print(f"❌ ERROR: Failed to verify migration: {e}")
        return False
    
    print(f"\n🎉 Migration successful! Your conversation history should now be accessible.")
    return True

if __name__ == "__main__":
    try:
        migrate_conversations()
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc() 