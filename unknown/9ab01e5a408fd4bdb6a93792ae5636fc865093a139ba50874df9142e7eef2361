version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - npm install
        build:
          commands:
            - echo "API_ENDPOINT=https://fx6ti5wvm9.execute-api.us-east-2.amazonaws.com/dev" >> .env
            - npm run build
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
      customHeaders:
        - pattern: '**/*'
          headers:
            - key: 'Strict-Transport-Security'
              value: 'max-age=31536000; includeSubDomains'
            - key: 'X-Content-Type-Options'
              value: 'nosniff'
            - key: 'X-XSS-Protection'
              value: '1; mode=block'
