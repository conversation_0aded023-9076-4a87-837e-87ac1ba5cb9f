#!/usr/bin/env python3
"""
Script to update user BqVhdHwSbuhLfEsOJIRf4XLYl9T2 with the correct phone number.
This will allow them to log in using the phone login flow.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from concierge.utils.firestore_client import get_user, update_user
from concierge.utils.phone_utils import clean_phone_for_storage

def update_user_phone():
    """Update the original user with the phone number."""
    
    original_user_id = "BqVhdHwSbuhLfEsOJIRf4XLYl9T2"
    new_user_id = "8LnLzt6W9JM3sGFsPWvbGoVTrS32"
    phone_number = "+17738377523"
    
    print("=== Updating User Phone Number ===\n")
    
    # Get the original user
    original_user = get_user(original_user_id)
    if not original_user:
        print(f"✗ Original user {original_user_id} not found")
        return
    
    # Get the new user 
    new_user = get_user(new_user_id)
    if not new_user:
        print(f"✗ New user {new_user_id} not found")
        return
    
    print(f"Original user ({original_user_id}):")
    print(f"  - Display Name: {original_user.get('displayName', 'N/A')}")
    print(f"  - Role: {original_user.get('role', 'N/A')}")
    print(f"  - Phone Number: {original_user.get('phoneNumber', 'None')}")
    print(f"  - Created At: {original_user.get('createdAt', 'N/A')}")
    
    print(f"\nNew user ({new_user_id}):")
    print(f"  - Display Name: {new_user.get('displayName', 'N/A')}")
    print(f"  - Role: {new_user.get('role', 'N/A')}")
    print(f"  - Phone Number: {new_user.get('phoneNumber', 'None')}")
    print(f"  - Created At: {new_user.get('createdAt', 'N/A')}")
    
    # Clean the phone number
    clean_phone = clean_phone_for_storage(phone_number)
    
    print(f"\n=== Updating Original User ===")
    print(f"Adding phone number {clean_phone} to user {original_user_id}")
    
    # Update the original user with the phone number
    update_data = {
        'phoneNumber': clean_phone,
        'updatedAt': 'SERVER_TIMESTAMP'
    }
    
    success = update_user(original_user_id, update_data)
    
    if success:
        print(f"✓ Successfully updated user {original_user_id} with phone number")
        
        # Verify the update
        updated_user = get_user(original_user_id)
        if updated_user and updated_user.get('phoneNumber') == clean_phone:
            print(f"✓ Verification: Phone number {clean_phone} is now stored")
        else:
            print(f"✗ Verification failed: Phone number not properly stored")
    else:
        print(f"✗ Failed to update user {original_user_id}")
    
    print(f"\n=== What to do next ===")
    print(f"1. Delete the duplicate user {new_user_id} (it was created by mistake)")
    print(f"2. The original user {original_user_id} can now log in with phone {phone_number}")
    print(f"3. Use the /auth/phone-login flow instead of Firebase direct login")

if __name__ == "__main__":
    update_user_phone() 